pipeline {
    agent any
    // global env variables
    environment {

        EMAIL_RECIPIENTS = '<EMAIL>'
        PROJECT_CODE = 'HOMEFOODI-USER-dev-master'
        PROJECT_SRC = './'
        SONAR_HOST = 'https://sonar.devsparxit.com'
    }
    stages {
         stage('Code Upload') {

            steps {

                withCredentials([usernamePassword(credentialsId: '1437e8c8-192f-4c9c-8cc4-bff726b3e919', usernameVariable: 'DEVUSER', passwordVariable: 'DEVPASS')]) {
                    sh 'echo Deploying....'
                    sh 'git config git-ftp.url "ftp://********/homefoodi-user"'
                    sh 'git config git-ftp.user ${DEVUSER}'
                    sh 'git config git-ftp.password ${DEVPASS}'
                    sh '/usr/local/bin/git-ftp push'
                }
            }
        }
    }
    post {
        always {
            echo 'Post always';
        }
        success {
            echo 'send success mail';
        }
        unstable {
            echo 'send unstable mail';
        }
        failure {
            echo 'send failure mail';
        }
    }
}
