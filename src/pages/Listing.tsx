
import { useState } from "react";
import { Layout } from "@/components/layout/Layout";
import { Button } from "@/components/ui/button";
import { Helmet } from "react-helmet";
import FilterDialog, { FilterState } from "@/components/filters/FilterDialog";
import { Filter } from "lucide-react";
import { ActionButtons } from "@/components/ui/action-buttons";

// Function to generate secure random offsets for coordinates
const getSecureRandomOffset = (): number => {
  // Always use crypto API for secure random generation
  if (typeof window !== 'undefined') {
    const array = new Uint8Array(1);
    window.crypto.getRandomValues(array);
    // Scale to our desired range (0-0.1)
    return (array[0] / 255) * 0.1;
  }
  // For SSR contexts, use a fixed offset as fallback
  return 0.05; // Fixed middle value in our range
};


// Mock vendor data
const mockVendors = Array.from({ length: 6 }).map((_, index) => ({
  id: index + 1,
  name: `Home Chef ${index + 1}`,
  phone: `+9198765${(43210 + index).toString().padStart(5, '0')}`,
  whatsapp: `9198765${(43210 + index).toString().padStart(5, '0')}`,
  location: {
    latitude: 28.6139 + getSecureRandomOffset(),
    longitude: 77.2090 + getSecureRandomOffset(),
  },
  service: "Home Cooking",
}));

const Listing = () => {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [appliedFilters, setAppliedFilters] = useState<FilterState>({
    vegType: [],
    cuisines: [],
    services: [],
    rating: "",
    price: "",
    distance: 5,
  });
  
  // Mock login state - in a real app, this would come from your auth system
  const [isLoggedIn, setIsLoggedIn] = useState(true);

  const handleApplyFilters = (filters: FilterState) => {
    setAppliedFilters(filters);
    console.log("Applied filters:", filters);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (appliedFilters.vegType.length > 0) count++;
    if (appliedFilters.cuisines.length > 0) count++;
    if (appliedFilters.services.length > 0) count++;
    if (appliedFilters.rating) count++;
    if (appliedFilters.price) count++;
    if (appliedFilters.distance !== 5) count++;
    return count;
  };

  // Mock function to handle login - in a real app, this would open your auth modal
  const handleLogin = () => {
    // This would typically open a login modal
    setIsLoggedIn(true);
  };

  return (
    <>
      <Helmet>
        <link
          href="https://fonts.googleapis.com/css2?family=Lato:wght@400;500;600;700;900&display=swap"
          rel="stylesheet"
        />
      </Helmet>

      <Layout>
        <div className="max-w-[1120px] mx-auto my-10 px-5">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-3xl font-bold text-[#2C2F24]">Home Chefs Near You</h1>
            <Button 
              variant="outline" 
              className="flex gap-2 items-center"
              onClick={() => setIsFilterOpen(true)}
            >
              <Filter className="h-4 w-4" />
              Filters
              {getActiveFiltersCount() > 0 && (
                <span className="bg-[#E87616] text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                  {getActiveFiltersCount()}
                </span>
              )}
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mockVendors.map((vendor, index) => (
              <div 
                key={index} 
                className="bg-white rounded-lg shadow-md p-4 flex flex-col"
              >
                <div className="flex-1 flex flex-col justify-center items-center mb-4">
                  <div className="text-gray-400 text-lg mb-4">Listing Item {index + 1}</div>
                  <h3 className="font-semibold text-lg">{vendor.name}</h3>
                </div>
                
                <ActionButtons 
                  vendor={vendor} 
                  isLoggedIn={isLoggedIn}
                  onLogin={handleLogin}
                  className="mt-auto"
                />
              </div>
            ))}
          </div>
        </div>

        <FilterDialog 
          open={isFilterOpen} 
          onOpenChange={setIsFilterOpen} 
          onApplyFilters={handleApplyFilters} 
        />
        
        {/* Sticky action buttons for the first vendor when scrolling (visible on mobile) */}
        <div className="block md:hidden">
          {mockVendors.length > 0 && (
            <ActionButtons 
              vendor={mockVendors[0]} 
              isLoggedIn={isLoggedIn}
              onLogin={handleLogin}
              variant="sticky"
              className="px-4 py-3"
            />
          )}
        </div>
      </Layout>
    </>
  );
};

export default Listing;
