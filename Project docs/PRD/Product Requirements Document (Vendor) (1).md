## Product Requirements Document (PRD) for HomeFoodi Project

### 1. Introduction

#### Overview
HomeFoodi is a local search engine providing information about three culinary services. It is a dynamic web-based platform designed to connect food enthusiasts with three types of culinary experts including home chefs, tiffin suppliers, and catering services. By offering a user-friendly interface and comprehensive functionality, HomeFoodi aims to simplify the process of finding and contacting culinary services, making it easy for users to locate a variety of home-cooked meals, tiffin services, and catering services at their convenience.

#### User Roles and Terminologies
- **Vendors** - Culinary service providers that is Home Chefs, Tiffin Service, and Caterers.
- **Users** - Who will reach out/search/Get in touch with the Vendors.
- **Admin Panel** - The administrator who will manage the platform.

#### Objectives and Key Goals
The primary objectives of the HomeFoodi website are:
- **User Engagement:** To create an engaging and seamless experience for users seeking culinary services.
- **Service Diversity:** To cater to a wide range of culinary preferences and needs by offering services from home chefs, tiffin suppliers, and caterers.
- **Ease of Access:** To provide a straightforward platform where users can easily browse, filter, and interact with service listings.
- **Vendor Support:** To empower vendors with tools for managing their services and get leads for their business.
- **Reliability and Performance:** To ensure the website is reliable and performs well across all user and admin functions.

### 2. Product Scope

#### Key Features

---
# Module: **Auth Module** #
---
### **Functional Requirements**

1. **Vendor Registration:**
   - Vendors will land on the home page and click on "Vendor Signup" to start the registration process.
   - Vendors can select their type(s): Home Chef, Tiffin Supplier, or Caterer. (They can opt for one, or they can opt for as many as three services but one at a time.)
   - Upon selecting the type(s), vendors are navigated to the authentication flow.
   
2. **Vendor Login:**
   - Vendors log in through the home page using their registered mobile number.
   - OTP verification is required for successful login.
   - After login, vendors will see a **"Vendor Dashboard"** button on the homepage.
   - Clicking the **Vendor Dashboard** button will redirect vendors to the **Dashboard Module**.

3. **User vs. Vendor Actions:**
   - Vendors can perform actions similar to general users (Refer to the User PRD) **until** they access the Vendor Dashboard (Refer to the dashboard module.)
   - Once inside the **Vendor Dashboard**, vendors will only have access to vendor-specific functionalities.
   - General users will **not** see the "Vendor Dashboard" button.
   
4. **Authentication Flow:**
   - **Login Screen:**
     - Input field for the mobile number (prefilled with the “+91” country code).
     - Checkbox for agreeing to the Terms and Conditions and Privacy Policy (mandatory to proceed).
     - Terms & Conditions Page and Privacy page will be hyperlinked.
     - Two buttons:
       - **Login with OTP** (Blue): Sends OTP to the provided number.
   - OTP Verification:
     - A pop-up with Input field for entering the received OTP.
     - Real-time validation of OTP.
   - Upon successful OTP verification:
     - If the vendor is new, they will proceed to the **Profile Setup Module**.
     - If the vendor is already registered, they will be redirected to the **Vendor Dashboard**.

5. **Registration Flow for Unregistered Vendors:**
   - After successful OTP verification, unregistered vendors are taken to the "How Do You Benefit?" screen.
     - Information includes:
       - Get countless customer leads.
       - Grow your business.
       - Affordable monthly price (Rs 1000/-).
     - "Next" button takes them to the profile setup module.

6. **Returning Vendors:**
   - Already registered vendors are redirected to their dashboard upon successful OTP verification.

7. **Back Button Handling:**
   - Back button on browsers must not take vendors to unauthorized or skipped steps in the authentication flow.

### **Non-Functional Requirements**

1. **Performance:**
   - OTP generation and delivery must occur quickly.
   - Page loading times should be minimal.

2. **Security:**
   - OTPs must be valid for 5 minutes.
   - Encrypt all data, including mobile numbers, during transmission and storage.

3. **Scalability:**
   - Handle multiple authentication requests per minute during peak times.

4. **Compatibility:**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18)


### **Validations**

1. **Mobile Number:**
   - Must be exactly 10 digits.
   - Should not allow special characters or alphabets.

2. **OTP Field:**
   - Must allow only numeric values.
   - Limit input to 6 digits.
   - Expired OTPs should not be accepted.
   - Incorrect OTPs should prompt an error after **3 failed attempts**.

3. **Terms & Conditions Checkbox:**
   - Ensure the checkbox is checked before enabling the "Login with OTP" button.
   
4. **Dashboard Access:**
   - Ensure that only **authenticated vendors** can see and access the "Vendor Dashboard" button.
   - Regular users should not see the "Vendor Dashboard" button.

### **Error Messages**

1. **Mobile Number Input:**
   - "Please enter a valid 10-digit mobile number."
   - "Mobile number cannot be empty."

2. **OTP Field:**
   - "Invalid OTP. Please try again."
   - "OTP expired. Request a new OTP."
   - "Maximum OTP attempts exceeded. Try again after 15 minutes."

3. **Terms & Conditions Checkbox:**
   - "You must agree to the Terms and Conditions and Privacy Policy to proceed."

4. **Server Errors:**
   - "Something went wrong. Please try again later."
   - "Failed to send OTP. Please check your network and try again."
   
5. **Registration Errors:**
   - "Mobile number already registered. Please log in."
   - "Failed to verify OTP. Please request a new OTP."

6. **Dashboard Access Errors:**
   - "Unauthorized access. Only registered vendors can access the dashboard."
   
### **User Journey Summary**

1. **Vendor Signup Process:**
   - Vendor clicks "Vendor Signup" on the home page.
   - If new, vendor is redirected to **Profile Setup Module**.
   - Enters mobile number → Receives OTP → Enters OTP → Registration is successful.
   - If existing, vendor is redirected to **Vendor Dashboard**.

2. **Vendor Login Process:**
   - Vendor logs in using mobile number → OTP verification → Homepage loads.
   - **"Vendor Dashboard" button** becomes visible.
   - Clicking "Vendor Dashboard" redirects to the dashboard.

3. **User vs. Vendor Experience:**
   - Vendor can browse the platform like a regular user until they enter the dashboard.
   - General users do **not** see the "Vendor Dashboard" button.

---
# Module: **Profile Setup** #
---
- Vendors can opt for all three profile setups (ie. a vendor can register as a  Home Chef and can be Tiffin Suppliers or Caterers as well). 
---
## Home Chefs ##
---
### **Functional Requirements**

1. **Profile Setup Flow:**
   - After clicking "Next" on the "How Do You Benefit" page, vendors are directed to the profile setup module.

2. **Steps in Profile Setup:**
   - **Profile:**
     - Upload a profile picture (supported format: 400x150 ratio only).
     - Input fields for:
       - Business Name.
       - "About" section (max 300 characters).
   - **Contact Details:**
     - Input fields for:
       - Phone Number (for calls).except - or ".
       - WhatsApp Number (direct link to WhatsApp chat).
       - SMS Number (for customer queries).
       - Address Details:
         - House/Flat Number.
         - Building/Society/Sector.
         - Area (dropdown populated via API/Admin).
         - City, State, Country, Postal Code.
   - **Cuisines:**
     - Multiple cuisine options (e.g., Bengali, Gujarati, South Indian, etc.).
     - Vendors can select one or more cuisines.
   - **Menu:**
     - Food Type:
       - Veg (starting price per person). Accept only Numeric value.
       - Non-Veg (starting price per person). Accept only Numeric value.
     - Select menu items (items populated via admin panel).
     - This can be single select/multi-select both.
   - **Services:**
     - Options include On-Demand Customized Food, Food for Senior Citizens, etc. (items populated via admin panel).
     - Vendors select applicable services.
     - Multi-select
     - Timing and delivery options:
       - Days of operation (e.g., Mon-Sat). It must be a drop down with option of Mon-Sat.
       - Delivery timings (start and end times). Must convert the input time to 12-hour shift. 
       - Delivery distance (adjustable slider).
   - **Preferences:**
     - Options like Oil Preferences, Spice Preferences, Vegan, Gluten-Free, etc. (items populated via admin panel).
     - Vendors can select one or more preferences.
   - **Payment Methods Accepted:**
     - Options include:
       - Cash.
       - Card.
       - Wallet.
       - UPI.
       - Net Banking.

3. **Profile Completion:**
   - Upon filling all steps, the profile is marked as "complete."
   - A random profile picture is auto-applied.

4. **Navigation:**
   - Vendors can move back and forth between steps using the step navigation menu.

5. **Subscription Flow:**
   - After profile completion, vendors are redirected to the subscription plan page.
   - Vendors are not listed on the service page until payment is made.

6. **Resumption on Logout/Drop-off:**
   - Vendors are redirected to the last incomplete step upon re-login.
   - If profile setup is complete but payment is pending, vendors are taken to the subscription page every time they log back in.

### **Non-Functional Requirements** 

1. **Performance:**
   - Business picture uploads must process fast.
   - Dropdowns and APIs (e.g., Area selection) should populate quickly.

2. **Scalability:**
   - System must handle multiple concurrent profile setups.

3. **Security:**
   - Data encryption for all personal information.
   - Address and contact details securely stored.

4. **Compatibility:**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18)
   
### **Validations**

1. **Profile Image:**
   - Must be in 400x150 ratio.
   - File size not exceeding 5 MB.
   - Only single image is allowed.

2. **Business Name:**
   - Max length: 64 characters.
   - No special characters allowed except -, ", and full stop.
   - This can be alphanumeric.

3. **Contact Details:**
   - Phone Number, WhatsApp Number, and SMS Number must be 10 digits. +91 must be auto-applied.
   - Postal Code must be 6 digits.
   - All address fields must be filled before proceeding.

4. **Cuisine/Menu/Service Selection:**
   - At least one option must be selected.

5. **Payment Methods:**
   - At least one payment method must be selected.

### **Error Messages**

1. **Profile Image:**
   - "Invalid image format. Please upload a 400x150 ratio image."
   - "File size exceeds the 5 MB limit."

2. **Business Name:**
   - "Business name cannot be empty."
   - "Special characters are not allowed except - or "."
   - This can be Alphanumeric.

3. **Contact Details:**
   - "Please enter a valid 10-digit phone number."
   - "Postal code must be 6 digits."
   - "All address fields must be completed."

4. **Cuisine/Menu/Service Selection:**
   - "Please select at least one option to proceed."

5. **Payment Methods:**
   - "Please select at least one payment method to proceed."

6. **General:**
   - "Something went wrong. Please try again."
   - "Incomplete profile setup. Please fill all fields to continue."

---
## Tiffin Suppliers ##
---

### **Functional Requirements**

1. **Profile Setup Flow:**
   - After clicking "Next" on the "How Do You Benefit" page, Tiffin Suppliers are directed to the profile setup module.

2. **Steps in Profile Setup:**
   - **Profile:**
     - Upload a profile picture (supported format: 400x150 ratio only).
     - Input fields for:
       - Business Name.
       - "About" section (max 300 characters).
   - **Contact Details:**
     - Input fields for:
       - Phone Number (for calls).
       - WhatsApp Number (direct link to WhatsApp chat).
       - SMS Number (for customer queries).
       - Address Details:
         - House/Flat Number.
         - Building/Society/Sector.
         - Area (dropdown populated via API/Admin).
         - City, State, Country, Postal Code.
   - **Cuisines:**
     - Multiple cuisine options (e.g., Gujarati, Bengali, South Indian, etc.).
     - Vendors can select one or more cuisines.
   - **Menu:**
     - Food Type:
       - Veg (starting price per person). Accept only Numeric value.
       - Non-Veg (starting price per person). Accept only Numeric value.
     - Meal Type:
       - Breakfast, Lunch, Dinner.
 
   - **Services:**
     - Options include Takeaway, Home Delivery, Dine-In (items populated via admin panel).
     - Vendors can select applicable services.
     - Timing and delivery options:
       - Days of operation (e.g., Mon-Sat). It must be a drop down with option of Mon-Sat.
       - Delivery timings (start and end times). Must convert the input time to 12-hour shift.
       - Delivery distance (adjustable slider).
   - **Preferences:**
     - Options like Oil Preferences, Spice Preferences, Vegan, Gluten-Free, etc. (items populated via admin panel).
     - Vendors can select one or more preferences.
   - **Payment Methods Accepted:**
     - Options include:
       - Cash.
       - Card.
       - Wallet.
       - UPI.
       - Net Banking.

3. **Profile Completion:**
   - Upon filling all steps, the profile is marked as "complete."
   - A random profile picture is auto-applied.

4. **Navigation:**
   - Vendors can move back and forth between steps using the step navigation menu.

5. **Subscription Flow:**
   - After profile completion, vendors are redirected to the subscription plan page.
   - Vendors are not listed on the service page until payment is made.

6. **Resumption on Logout/Drop-off:**
   - Vendors are redirected to the last incomplete step upon re-login.
   - If profile setup is complete but payment is pending, vendors are taken to the subscription page every time they log back in.

### **Non-Functional Requirements**

1. **Performance:**
   - Profile picture uploads must process quickly.
   - Dropdowns and APIs (e.g., Area selection) should populate fast.

2. **Scalability:**
   - System must handle multiple concurrent profile setups.

3. **Security:**
   - Data encryption for all personal information.
   - Address and contact details securely stored.

4. **Compatibility:**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18)

### **Validations**

1. **Profile Image:**
   - Must be in 400x150 ratio.
   - File size not exceeding 5 MB.
   - Only single image is allowed.

2. **Business Name:**
   - "Business name cannot be empty."
   - "Special characters are not allowed except -, ", or full stop."
   - This can be alphanumeric.

3. **Contact Details:**
   - Phone Number, WhatsApp Number, and SMS Number must be 10 digits. +91 must be auto-applied.
   - Postal Code must be 6 digits.
   - All address fields must be filled before proceeding.

4. **Cuisine/Menu/Service Selection:**
   - At least one option must be selected.

5. **Payment Methods:**
   - At least one payment method must be selected.

### **Error Messages**

1. **Profile Image:**
   - "Invalid image format. Please upload a 400x150 ratio image."
   - "File size exceeds the 5 MB limit."

2. **Business Name:**
   - "Business name cannot be empty."
   - "Special characters are not allowed except -, ", or full stop."
   - This can be Alphanumeric.

3. **Contact Details:**
   - "Please enter a valid 10-digit phone number."
   - "Postal code must be 6 digits."
   - "All address fields must be completed."

4. **Cuisine/Menu/Service Selection:**
   - "Please select at least one option to proceed."

5. **Payment Methods:**
   - "Please select at least one payment method to proceed."

6. **General:**
   - "Something went wrong. Please try again."
   - "Incomplete profile setup. Please fill all fields to continue."
 
---
## Caterers ##
---

### **Functional Requirements**

1. **Profile Setup Flow:**
   - After clicking "Next" on the "How Do You Benefit" page, Caterers are directed to the profile setup module.

2. **Steps in Profile Setup:**
   - **Profile:**
     - Upload a primary profile picture (supported format: 400x150 resolution). This picture will be shown on the caterer listing page and caterer details page. The pictures will be shown to the desktop and mobile vendors.
     - Option to upload up to 9 additional images of the business.
     - Input fields for:
       - Business Name.
       - "About" section (max 300 characters).
   - **Contact Details:**
     - Input fields for:
       - Phone Number (for calls).
       - WhatsApp Number (direct link to WhatsApp chat).
       - SMS Number (for customer queries).
       - Address Details:
         - House/Flat Number.
         - Building/Society/Sector.
         - Area (dropdown populated via API/Admin).
         - City, State, Country, Postal Code.
   - **Cuisines:**
     - Multiple cuisine options (e.g., Bengali, Gujarati, South Indian, etc.).
     - Vendors can select one or more cuisines.
   - **Menu:**
     - Food Type:
       - Veg (starting price per person).
       - - Starting price input (numerical value only).
       - Non-Veg (starting price per person).
       - - Starting price input (numerical value only).
     - Menu Type:
       - Options like Starters, Main Course, Desserts, etc. (The options will be listed and managed from the admin panel)
     
   - **Services:**
     - Service Types:
       - Options include House Party, Kitty Party, Wedding, etc. (Listed and managed from the admin panel)
       - Additional options like Buffet Setup, Crockery, Waiter. 
     - Days of Operation:
       - Dropdown selection (Mon-Sun).
     - Timings:
       - Start and end time (12-hour format).
     - Delivery Distance:
       - Adjustable slider to select a range (e.g., 5 KM to 8 KM).
   - **Preferences:**
     - Options like Oil Preferences, Spice Preferences, Vegan, Gluten-Free, etc. (items populated via admin panel).
     - Vendors can select one or more preferences.
   - **Payment Methods Accepted:**
     - Options include:
       - Cash.
       - Card.
       - Wallet.
       - UPI.
       - Net Banking.

3. **Profile Completion:**
   - Upon filling all steps, the profile is marked as "complete."
   - A placeholder profile picture is auto-applied on each caterer profile.

4. **Navigation:**
   - Vendors can move back and forth between steps using the step navigation menu.

5. **Subscription Flow:**
   - After profile completion, vendors are redirected to the subscription plan page.
   - Vendors are not listed on the service page until payment is made.

6. **Resumption on Logout/Drop-off:**
   - Vendors are redirected to the last incomplete step upon re-login.
   - If profile setup is complete but payment is pending, vendors are taken to the subscription page every time they will log back in.

### **Non-Functional Requirements**

1. **Performance:**
   - Profile picture uploads must process quickly.
   - Dropdowns and APIs (e.g., Area selection) should populate fast.

2. **Scalability:**
   - System must handle multiple concurrent profile setups.

3. **Security:**
   - Data encryption for all personal information.
   - Address and contact details securely stored.

4. **Compatibility:**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18)

### **Validations**

1. **Profile Image:**
   - Primary image must be 400x150 resolution.
   - File size not exceeding 5 MB.
   - Rest of the 9 images can be of any size.

2. **Business Name:**
   - Max length: 64 characters.

3. **Contact Details:**
   - Phone Number, WhatsApp Number, and SMS Number must be 10 digits. +91 must be auto-applied.
   - Postal Code must be 6 digits.
   - All address fields must be filled before proceeding.

4. **Cuisine/Menu/Service Selection:**
   - At least one option must be selected.

5. **Payment Methods:**
   - At least one payment method must be selected.


### **Error Messages**

1. **Profile Image:**
   - "Invalid image format. Please upload a 400x150 resolution image."
   - "File size exceeds the 5 MB limit."

2. **Business Name:**
   - "Business name cannot be empty."
   - "Special characters are not allowed except -, ", or full stop"

3. **Contact Details:**
   - "Please enter a valid 10-digit phone number."
   - "Postal code must be 6 digits."
   - "All address fields must be completed."

4. **Cuisine/Menu/Service Selection:**
   - "Please select at least one option to proceed."

5. **Payment Methods:**
   - "Please select at least one payment method to proceed."

6. **General:**
   - "Something went wrong. Please try again."
   - "Incomplete profile setup. Please fill all fields to continue."

---
# Module: **Subscription Module** #
---
### **Functional Requirements**

1. **Subscription Plans:**
   - Vendors are presented with three subscription plans after profile completion:
     - **1 Month:** Rs 1000/- (including GST) with 15 days extra.
     - **3 Months:** Rs 3000/- (including GST) with 30 days extra.
     - **6 Months:** Rs 6000/- (including GST) with 60 days extra.
   - Vendors can select only one plan at a time.
   - For the first time vendors, 1 month is free for each plan. That being said, they will have to make the payment anyhow for any of the 3 plans. 

2. **Payment Process:**
   - After selecting a subscription plan and clicking "Purchase," vendors are redirected to the payment platform.
   - We've finalized Razorpay as our payment gateway.
   - If the payment is successful:
     - Vendors are redirected to the "Thank You" page.
     - "Thank You" page displays:
       - Subscription details (plan type, amount paid, transaction ID, subscription ID).
       - A "Get Started" button to proceed to the dashboard.
     - The page lasts for 10 seconds before automatically redirecting to the dashboard.
   - If the payment fails:
     - Vendors are redirected to the subscription page.
     - Vendors cannot access the dashboard until payment is successfully made.
     - Even after logging out and logging back in, vendors are redirected to the subscription page until payment is completed.

3. **Subscription Management:**
   - Vendors can re-purchase a subscription if their previous payment failed.
   - Vendors can pay for another subscription plan (One out of three 1-month, 3-month, or 6-month) as a Advance payment. And only a single advance payment will be accepted. The button will be faded if the vendor has paid a plan as advance.
   - If a Vendor has opted for all services, they will have to make the payment for each listing type. Each of them will have their own details.

4. **Plan Details and Payment History:**
   - Vendors can view their active plan details, including:
     - Plan type.
     - Start and end date.
     - Option to "View Details" for the active plan payment information (plan type, amount paid, transaction ID, subscription ID).
     - Option to "Pay Advance" to make a payment in advance for the next cycle.
   - Vendors can access their payment history:
     - Details of previous plans(plan type, amount paid, transaction ID, subscription ID), purchase dates, and bills (PDF).
     - Option to download dynamically generated bills.

5. **E-Bill Feature:**
   - Vendors can add their email address to receive e-bills automatically.

6. **Subscription Plans Display:**
   - Monthly, Quarterly, and Bi-Yearly plans are displayed dynamically.
   - All plans and related bills are generated dynamically.

### **Non-Functional Requirements**

1. **Performance:**
   - Payment platform redirection and response must occur quickly.
   - Subscription details and payment history retrieval should load fast.

2. **Scalability:**
   - Support multiple concurrent vendor subscription purchases.

3. **Security:**
   - Secure payment gateway integration with encrypted transactions.
   - All personal and payment information must be securely stored and transmitted.

4. **Compatibility:**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18) 

### **Validations**

1. **Subscription Selection:**
   - Ensure only one plan can be selected at a time.

2. **Payment Completion:**
   - Prevent access to the dashboard if payment fails.

3. **E-Bill Email Address:**
   - Validate email format before saving.

### **Error Messages**

1. **Subscription Selection:**
   - "Please select a subscription plan to proceed."

2. **Payment Failure:**
   - "Payment failed. Please try again."

3. **E-Bill Email Address:**
   - "Invalid email address. Please enter a valid email."

4. **General:**
   - "Something went wrong. Please try again later."

### **Vendor Journey Summary:**
1. After profile setup, vendors land on the subscription page.
2. Vendors select a plan and proceed to payment.
3. If payment is successful:
   - "Thank You" page is displayed for 10 seconds.
   - Vendors can click "Get Started" to access the dashboard manually.
4. If payment fails:
   - Vendors remain on the subscription page until payment is completed.
5. Vendors can manage their subscriptions, view details, and access payment history from the dashboard.

---
# Module: **Dashboard** #
---

### **Functional Requirements**

1. **Dashboard Overview:**
   - The dashboard will be referred to as "Customer Leads."
   - It will display the total counts of:
     - Calls.
     - WhatsApp messages.
     - Enquiries.

2. **Filters and Dropdowns:**
   - Two dropdown filters:
     - **Type:**
       - Options: Calls, WhatsApp, Enquiries.
       - Allows vendors to filter customer leads by interaction type.
     - **Date:**
       - Options: Today, Yesterday, Last 7 Days, Month to Date, Year to Date, and Date Range.
       - Date Range will open a calendar for vendors to select a custom date range.
       - Clear Filter Button:
         - Resets all dropdown filters and displays all data.
   - The counters for Calls, WhatsApp, and Enquiries will dynamically update based on the applied filters.

3. **Customer Lead Cards:**
   - Each card will display:
     - User Name who contacted the vendor through any of the action buttons.
     - User Phone Number.
     - Date and Time of interaction.
   - Upon clicking a card, a popup will display the same details (User Name, Phone Number, Date, and Time).
   - The cards will be auto-created when any user will interact with the vendor from the vendor listing page or vendor detail page through action buttons.

4. **Pagination:**
   - The dashboard will support pagination for navigating through customer leads.
   - Vendors can move between pages using numbered pagination controls or a "Next" button.

5. **Sidebar Navigation:**
   - The dashboard will include a sidebar with the following options:
     - **Customer Leads**
     - **Outlet Details**
     - **Subscription Plans**
     - **My Plan & Billing History**
     - **Privacy Policy**
     - **Terms and Conditions**
     - **FSSAI Document**
     - **Help**
     - **Contact Us**
     - **Delete Account**

### **Non-Functional Requirements**

1. **Performance:**
   - Dashboard data and counters must load immediately.
   - Filters must apply quickly.
   - Popup with user details should appear instantly upon card selection.

2. **Scalability:**
   - The system must handle multiple customer leads per vendor.

3. **Security:**
   - User phone numbers and other sensitive details must be securely stored and encrypted.

4. **Compatibility:**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18)

### **Validations**

1. **Filters:**
   - Ensure at least one dropdown option is selected before applying a filter.
   - Validate that Date Range selection includes a valid start and end date.

2. **Popup Details:**
   - Ensure all details (User Name, Phone Number, Date, Time) are present before displaying the popup.

3. **Pagination:**
   - Prevent navigation to pages beyond the available range.

### **Error Messages**

1. **Filters:**
   - "Invalid date range. Please select a valid start and end date."
   - "No results found for the selected filters."

2. **Popup Details:**
   - "Unable to load customer details. Please try again."

3. **General:**
   - "Something went wrong. Please refresh the page and try again."

### **Vendor Journey Summary:**
1. Vendors log in and land on the Customer Leads dashboard.
2. The dashboard displays total counts for Calls, WhatsApp messages, and Enquiries.
3. Vendors apply filters using the Type and Date dropdowns.
4. Counters and customer lead cards dynamically update based on selected filters.
5. Vendors can navigate through pages of leads using pagination.
6. Clicking on a customer lead card opens a popup displaying detailed information.
7. Sidebar navigation allows vendors to access additional features like Subscription Plans, Billing History, and Help.

---
# Module: **Edit Profile** #
---
### **Functional Requirements**

1. **Edit Profile Access:**
   - Vendors can edit their profile based on the data provided during registration and the profile setup process (Home Chef, Tiffin Supplier, Caterer).
   - Profile editing is accessible via the "Outlet Details" section in the sidebar.

2. **Editable Sections:**
   - Vendors can edit the following sections one-by-one:
     - **Profile:**
       - Upload or change profile image.
       - Edit Business Name.
       - Edit "About" section (max 300 characters).
     - **Contact Details:**
       - Phone Number.
       - WhatsApp Number.
       - SMS Number.
       - Address details (House/Flat Number, Building/Society/Sector, Area, City, State, Country, Postal Code).
     - **Cuisines:**
       - Modify selected cuisines.
     - **Menu:**
       - Update food types, starting prices, and menu options.
     - **Services:**
       - Edit offered services (e.g., Home Delivery, Takeaway).
       - Modify delivery timings, days, and delivery distance.
     - **Preferences:**
       - Update dietary or cooking preferences (e.g., Vegan, Gluten-Free).
     - **Payment Options:**
       - Edit accepted payment methods (Cash, Card, Wallet, UPI, Net Banking).

3. **Save and Cancel Functionality:**
   - Each form will include:
     - **Save Button:**
       - Saves the edited information.
     - **Cancel Button:**
       - Reverts any changes made to the form.
       - Remains inactive until changes are made.

4. **Data Pre-Fill:**
   - All forms will pre-fill with the data provided during registration or previous edits.

5. **Unsaved Changes Warning:**
   - If a vendor makes changes to a form, doesn’t save, and attempts to navigate away, a pop-up will appear:
     - "You have unsaved changes. Do you want to save before leaving?"
     - Options:
       - "Save and Proceed."
       - "Discard Changes."

### **Non-Functional Requirements**

1. **Performance:**
   - Form loading and pre-fill should occur instantly.
   - Save actions should process fast.

2. **Scalability:**
   - System must support multiple concurrent profile edits across vendors.

3. **Security:**
   - All edited data must be encrypted during transmission and securely stored.

4. **Compatibility:**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18)

### **Validations**

1. **Profile Image:**
   - Supported file formats: JPG, PNG.
   - Maximum file size: 5 MB.

2. **Business Name:**
   - Max length: 50 characters.
   - No special characters allowed.

3. **Contact Details:**
   - Phone Number, WhatsApp Number, and SMS Number must be 10 digits.
   - Postal Code must be 6 digits.
   - All address fields must be filled before saving.

4. **Menu and Services:**
   - At least one menu item or service must remain selected.

5. **Preferences and Payment Options:**
   - At least one preference and one payment option must remain selected.

### **Error Messages**

1. **Save Action:**
   - "Failed to save changes. Please try again."

2. **Validations:**
   - "Invalid phone number. Please enter a 10-digit number."
   - "Postal code must be 6 digits."
   - "Please upload a valid image file (JPG, PNG)."
   - "At least one menu item/service must be selected."
   - "At least one payment method must be selected."

3. **Unsaved Changes Warning:**
   - "You have unsaved changes. Do you want to save before leaving?"

### **Vendor Journey Summary:**
1. Vendors access the "Outlet Details" section from the sidebar.
2. Select a section (e.g., Profile, Contact Details) to edit.
3. Make changes to the form and either:
   - Save changes using the "Save" button.
   - Cancel changes using the "Cancel" button.
4. If navigating away without saving, a pop-up appears prompting the vendor to save or discard changes.
5. Upon successful save, the updated data is reflected in the respective form.

---
# Module: **Header & Footer** #
---
### **Functional Requirements**

1. **Header:**
   - The header will include:
     - **Logo:** Displays the HomeFoodi brand logo.
     - **Option to Switch Profile:** Option to switch among other vendor profile type- Home Chef, Caterer, Tiffin Supplier.
     - **Notifications Icon:** Clicking on this icon opens the system tray for alerts and notifications.
     - **Plan Expiry Date:**
       - Shows the plan validity date (e.g., "Plan Validity: Feb 4, 2025").
       - If the vendor has not purchased a plan, this section will remain empty.
     - **Profile Picture with Dropdown:**
       - Displays the placeholder profile picture generated during registration.
       - Dropdown options include:
         - Logout.

2. **Footer:**
   - The footer will be accessible across all pages on the platform.
   - Includes the following sections:
     - **Quick Links:**
       - Home Page.
       - About Us.
       - Our Services.
     - **Support:**
       - Contact - will open the Contact Form Pop-up.
       - FAQ.
       - Feedback - will use the default emailer of the device. 
     - **Connect With Us:**
       - Email: <EMAIL>.
       - Phone: +91-8860357937 | 7669237937.
       - Social media icons linking to Facebook, X (Twitter), Instagram, YouTube, LinkedIn, Pinterest.
     - **Copyright and Legal:**
       - Privacy Policy.
       - Terms of Services.
       - Copyright © 2025 HomeFoodi | All rights reserved.

3. **Dynamic Behavior:**
   - Notifications will dynamically update based on new system alerts.
   - The plan expiry date will update automatically after a new subscription purchase.

4. **Placeholder Profile Picture:**
   - Vendors without an uploaded profile picture will display a default placeholder image generated during registration.

### **Non-Functional Requirements**

1. **Performance:**
   - Header and footer must load quickly on all pages.
   - Notifications icon must display new alerts instantly.

2. **Responsiveness:**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18)

3. **Accessibility:**
   - Text and icons in the header and footer must adhere to accessibility standards (WCAG 2.1 Level AA).

### **Validations**

1. **Notifications:**
   - Must display only unread notifications until cleared by the vendor.

2. **Plan Expiry Date:**
   - Must be accurate and update immediately after any subscription purchase.

### **Error Messages**

1. **Notifications Loading Error:**
   - "Failed to load notifications. Please refresh the page."

### **Vendor Journey Summary:**

1. **Header:**
   - Vendors see the HomeFoodi logo, notifications, plan expiry date, and profile picture in the header.
   - Clicking on the notifications icon displays the system tray with new alerts.
   - The profile picture dropdown allows the vendor to log out.

2. **Footer:**
   - Vendors can access quick links for navigation (Home Page, About Us, Our Services).
   - Support links provide access to Contact, FAQ, and Feedback options.
   - Vendors can view and interact with social media icons or contact details (email, phone).
   - Privacy Policy, Terms of Services, and copyright details are displayed at the bottom.

---
# Module: **Notification Module** #
---

### **Functional Requirements**

1. **Notification for Plan Renewal/Payment Due:**
   - Notifications will begin **7 days before the plan expiry** date.
   - Vendors will receive daily reminders until the plan expiry date.
   - Vendors will receive the feedback of their flagged review from admin when the admin will reject/accept the feedback.

2. **SMS Notifications:**
   - An SMS will be sent daily to the vendor starting 7 days before the plan expiry and ending on the expiry date.
   - SMS content will include:
     - Plan expiry date.
     - Reminder to renew the subscription.
     - Link to the subscription renewal page.

3. **System Tray for Notifications:**
   - Clicking on the **bell icon** in the header will toggle the notification tray:
     - Open the tray if it is closed.
     - Close the tray if it is open.
   - Notifications will be displayed in a **scrollable tray.**

4. **Notification Retention Period:**
   - Notifications from the last **one year** (from the current date) will be retained and shown in the tray.

5. **Notification Content:**
   - Each notification will include:
     - Date and time of the notification.
     - Brief description ("Plan expires in 7 days. Renew now." and "Your review was moderated by admin").
     - A clickable link for action ("Renew Plan" and in case of Admin feedback to the Vendor's flagged review - The vendor will see the feedback message in the pop-up "Hi, Your feedback has been moderated and we've rejected/accepted the suggestion" with a message).

6. **Dynamic Updates:**
   - Notifications will dynamically update based on:
     - Plan renewal actions.
     - New system alerts.

### **Non-Functional Requirements**

1. **Performance:**
   - Notifications tray must load instant of clicking the bell icon.
   - New notifications should appear instantly in the tray upon generation.

2. **Scalability:**
   - The system should support multiple concurrent notifications across all vendors without performance degradation.

3. **Security:**
   - All notifications must be encrypted during transmission and securely stored.

4. **Responsiveness:**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18)

### **Validations**

1. **Plan Renewal Notifications:**
   - Must trigger exactly **7 days before the expiry** and daily until the expiry date.
   - Notifications should stop immediately after the vendor renews their plan.

2. **SMS Delivery:**
   - SMS must successfully deliver to the vendor's registered phone number.
   - Retry SMS delivery if the first attempt fails.

3. **Notification Retention:**
   - Only notifications from the last **one year** should be displayed.

### **Error Messages**

1. **Notification Loading Error:**
   - "Failed to load notifications. Please refresh the page."

2. **SMS Delivery Failure:**
   - "Failed to send SMS notification. Please verify your phone number."

### **Vendor Journey Summary:**

1. **Pre-Expiry Notifications:**
   - Vendors receive daily notifications (in the tray and via SMS) starting 7 days before plan expiry.
   - Notifications include actionable links to renew the plan.

2. **Notification Tray Access:**
   - Vendors click the bell icon in the header to open/close the notifications tray.
   - The tray displays all notifications from the past year in chronological order.
   - Vendors can scroll through notifications to view older alerts.

3. **Dynamic Updates:**
   - Notifications dynamically update if the vendor renews their plan or if there are new system alerts.

4. **Retention and Removal:**
   - Notifications older than one year are automatically removed from the system.

---
# Module: **Review Management Module** #
---
### **Functional Requirements**

1. **Review Management Access:**
   - A dedicated option in the sidebar labeled "Reviews" allows vendors to access their reviews.
   - Vendors can view reviews received on their detail page in this section.

2. **Review Updates:**
   - Each time a user rates and reviews a vendor, the data will be dynamically updated in the vendor’s review management panel.
   - Reviews include:
     - User name.
     - User rating (out of 5 stars).
     - User comments.
     - Date and time of the review.

3. **Flagging Reviews:**
   - Vendors can flag any review they find inappropriate or incorrect only a single time for one review until the admin takes the action.
   - Clicking on the "Flag" button next to a review opens a feedback pop-up.
   - The feedback pop-up includes:
     - A text box where the vendor can explain why they are flagging the review (max 250 characters).
     - A "Submit" button to send the flagged review to the admin.

4. **Admin Notification:**
   - Flagged reviews will be sent to the admin for review.
   - Admins can take the following actions:
     - Approve deletion of the review.
     - Reject the flag and retain the review.

5. **Admin Feedback to Vendor:**
   - Admin’s decision will be notified to the vendor via Notifications Module:
     - If the review is deleted: "Your flagged review has been removed."
     - If the review is retained: "Your flagged review did not meet the criteria for removal."

### **Non-Functional Requirements**

1. **Performance:**
   - The review list should load immediately.
   - Flagged reviews should be sent to the admin instantly after submission.

2. **Scalability:**
   - The system must support multiple reviews across all vendors.

3. **Security:**
   - Review data must be encrypted during transmission and securely stored.

4. **Responsiveness:**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18)

### **Validations**

1. **Feedback Submission:**
   - Vendors must provide feedback (minimum 10 characters and maximum 250 characters) before submitting a flagged review.

2. **Review Visibility:**
   - Only reviews for the logged-in vendor should be visible in their panel.

### **Error Messages**

1. **Review Loading Error:**
   - "Failed to load reviews. Please refresh the page."

2. **Feedback Submission Error:**
   - "Failed to submit your feedback. Please try again."

3. **Validation Errors:**
   - "Feedback must be at least 10 characters long."

### **Vendor Journey Summary:**

1. **Accessing Reviews:**
   - Vendors navigate to the "Reviews" section in the sidebar.
   - The system displays all reviews received on their vendor detail page.

2. **Viewing and Flagging Reviews:**
   - Vendors can scroll through the list of reviews, with each review displaying user details, rating, and comments.
   - To flag a review, the vendor clicks the "Flag" button and provides a reason for the flagging in the feedback pop-up.

3. **Admin Handling:**
   - The flagged review is sent to the admin for review.
   - Admin decides whether to delete or retain the review and notifies the vendor of their decision.

4. **Vendor Notification:**
   - Vendors receive a system message about the admin’s decision on the flagged review.

Note: All the specified non-functional requirements and time-sensitive features are subject to third-party and server response dependencies.
---
End of the document
---








