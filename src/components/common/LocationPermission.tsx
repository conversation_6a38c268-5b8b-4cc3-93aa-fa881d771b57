/**
 * Location Permission Component
 * Created: 2025-03-20
 * This component handles requesting and managing location permissions from users
 * It shows a permission request only on first visit and stores the preference
 *
 * Geolocation usage justification:
 * - Necessary to show users nearby food services (chefs, tiffin services, caterers)
 * - Enables accurate distance calculation for delivery radius determination
 * - Improves user experience by displaying relevant local options
 * - Location data is stored only locally in the user's browser
 */

import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { X } from 'lucide-react';

const PERMISSION_STORAGE_KEY = 'homefoodi_location_permission';

const LocationPermission: React.FC = () => {
  const [showPermissionRequest, setShowPermissionRequest] = useState(false);

  
  useEffect(() => {
    // Check if we've already asked for permission
    const permissionStatus = localStorage.getItem(PERMISSION_STORAGE_KEY);
    
    // Only show the request if we haven't asked before
    if (!permissionStatus) {
      setShowPermissionRequest(true);
    }
  }, []);

  const handleAllowLocation = () => {
    if (navigator.geolocation) {
      // Configure geolocation options for better reliability and user experience
      const geoOptions = {
        enableHighAccuracy: true, // Get more precise location for better service matching
        timeout: 10000,          // 10 second timeout to prevent hanging
        maximumAge: 0            // Always get fresh location data
      };
      
      navigator.geolocation.getCurrentPosition(
        (position) => {
          // Success - store the permission status and location
          localStorage.setItem(PERMISSION_STORAGE_KEY, 'granted');
          localStorage.setItem('homefoodi_user_latitude', position.coords.latitude.toString());
          localStorage.setItem('homefoodi_user_longitude', position.coords.longitude.toString());
          
          toast({
            title: "Location Access Granted",
            description: "We'll use your location to show you nearby services.",
          });
          
          setShowPermissionRequest(false);
        },
        (error) => {
          // Handle specific geolocation errors with appropriate messages
          // Error - store that permission was denied
          localStorage.setItem(PERMISSION_STORAGE_KEY, 'denied');
          
          toast({
            title: "Location Access Denied",
            description: "You can change this in your browser settings if needed.",
            variant: "destructive",
          });
          
          setShowPermissionRequest(false);
        },
        geoOptions // Apply the geolocation options
      );
    } else {
      // Geolocation not supported
      localStorage.setItem(PERMISSION_STORAGE_KEY, 'not_supported');
      
      toast({
        title: "Location Not Supported",
        description: "Your browser doesn't support geolocation.",
        variant: "destructive",
      });
      
      setShowPermissionRequest(false);
    }
  };

  const handleDenyLocation = () => {
    // User explicitly denied - store this preference
    localStorage.setItem(PERMISSION_STORAGE_KEY, 'denied');
    setShowPermissionRequest(false);
  };

  if (!showPermissionRequest) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:max-w-[400px] bg-white p-4 rounded-lg shadow-lg z-50 border border-gray-200">
      <div className="flex justify-between items-start mb-2">
        <h3 className="font-medium text-lg">Location Access</h3>
        <button 
          onClick={handleDenyLocation}
          className="text-gray-500 hover:text-gray-700"
        >
          <X size={18} />
        </button>
      </div>
      
      <p className="text-gray-600 mb-4">
        HomeFoodi would like to access your location to show you nearby chefs, tiffin services, and caterers. This helps us determine which services can deliver to your area.
      </p>
      
      <div className="flex gap-3">
        <Button 
          variant="outline" 
          onClick={handleDenyLocation}
          className="flex-1"
        >
          Not Now
        </Button>
        <Button 
          onClick={handleAllowLocation}
          className="flex-1 bg-[#E87616] hover:bg-[#d06913]"
        >
          Allow Access
        </Button>
      </div>
    </div>
  );
};

export default LocationPermission;
