
import { cn } from "@/lib/utils";

interface RatingFilterProps {
  selectedRating: string;
  onChange: (rating: string) => void;
}

const RATINGS = [
  { value: "any", label: "Any" },
  { value: "3.5+", label: "3.5+" },
  { value: "4+", label: "4+" },
  { value: "4.5+", label: "4.5+" },
];

const RatingFilter = ({ selectedRating, onChange }: RatingFilterProps) => {
  // Handle rating selection with toggle functionality
  const handleRatingClick = (value: string) => {
    // If the clicked rating is already selected, toggle it off by setting to "any"
    if (selectedRating === value) {
      onChange("any");
    } else {
      // Otherwise, select the new rating
      onChange(value);
    }
  };

  return (
    <div className="flex flex-col gap-4 max-w-[300px]">
      {RATINGS.map((rating) => (
        <div
          key={rating.value}
          className={cn(
            "w-[55px] h-[30px] flex items-center justify-center rounded-md cursor-pointer transition-colors border",
            selectedRating === rating.value 
              ? " text-[#008001] border-[#008001]" 
              : "text-[#9B9B9B] border-[#D9D9D9]"
          )}
          onClick={() => handleRatingClick(rating.value)}
        >
          <span className="font-lato text-sm font-semibold">{rating.label}</span>
        </div>
      ))}
    </div>
  );
};

export default RatingFilter;
