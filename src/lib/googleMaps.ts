/**
 * Google Maps API Integration
 * Created: 2025-03-26
 * Description: Utility functions for Google Maps API integration, including loading the API,
 * getting address predictions, and geocoding addresses
 */

// Get the Google Maps API key from environment variables
const GOOGLE_MAPS_API_KEY = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;

// Validate API key is present
if (!GOOGLE_MAPS_API_KEY) {
  console.error('Google Maps API key is missing. Please add VITE_GOOGLE_MAPS_API_KEY to your .env file.');
}

// Check if the API key is valid (not a placeholder)
const isValidApiKey = (key: string) => {
  return key && key.length > 10 && !key.includes('your_') && !key.includes('placeholder');
};

// Validate the API key format
if (!isValidApiKey(GOOGLE_MAPS_API_KEY)) {
  console.warn('Google Maps API key may be invalid or a placeholder. Please check your .env file.');
}

/**
 * Load the Google Maps API script
 * @returns Promise that resolves when the API is loaded
 */
export const loadGoogleMapsApi = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // Check if the API is already loaded
    if (window.google && window.google.maps) {
      resolve();
      return;
    }

    // Create a callback function for the script using a secure random ID
    let callbackName: string;
    
    // Use crypto API for secure random ID generation
    const array = new Uint32Array(2);
    window.crypto.getRandomValues(array);
    callbackName = `googleMapsApiLoaded_${array[0].toString(36)}${array[1].toString(36)}`.substring(0, 20);
    
    window[callbackName] = () => {
      resolve();
      delete window[callbackName];
    };

    // Create and append the script element
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&libraries=places&callback=${callbackName}`;
    script.async = true;
    script.defer = true;
    script.onerror = (error) => {
      reject(new Error('Failed to load Google Maps API'));
      delete window[callbackName];
    };

    document.head.appendChild(script);
  });
};

/**
 * Get address predictions from Google Places Autocomplete API
 * @param input The user input to get predictions for
 * @param sessionToken A session token for the autocomplete session
 * @param userCoordinates Optional user coordinates for location bias
 * @returns Promise that resolves with an array of prediction objects with place_id and description
 */
export const getAddressPredictions = async (
  input: string,
  sessionToken: google.maps.places.AutocompleteSessionToken,
  userCoordinates?: { lat: number, lng: number }
): Promise<{ place_id: string, description: string }[]> => {
  // If input is empty or too short, return empty array
  if (!input || input.trim().length < 2) {
    return [];
  }

  return new Promise((resolve, reject) => {
    try {
      // Create autocomplete service
      const autocompleteService = new google.maps.places.AutocompleteService();
      
      // Set up request parameters
      const request: any = {
        input: input.trim(),
        componentRestrictions: { country: 'in' },
        sessionToken: sessionToken,
        // Include both establishments and geocode to get places like hospitals
        types: ['establishment', 'geocode']
      };

      // Add location bias if we have user coordinates
      if (userCoordinates) {
        request.locationBias = {
          center: userCoordinates,
          radius: 5000000000 // 50000km radius to get more results
        };
        console.log('Using dynamic location bias with 50km radius:', userCoordinates);
      } else {
        // Use a central India location with a very large radius to get nationwide results
        request.locationBias = {
          center: { lat: 20.5937, lng: 78.9629 }, // Center of India
          radius: 10000000000 // 100000km radius to cover most of India
        };
        console.log('Using central India location bias for nationwide results');
      }

      // Get predictions
      autocompleteService.getPlacePredictions(
        request,
        (predictions, status) => {
          if (status === google.maps.places.PlacesServiceStatus.OK && predictions && predictions.length > 0) {
            // Map predictions to objects with place_id and description
            const results = predictions.map(prediction => ({
              place_id: prediction.place_id,
              description: prediction.description
            }));
            
            // If we have user coordinates, apply a lighter prioritization that doesn't filter out good matches
            if (userCoordinates) {
              // Sort results based on known location keywords but with less aggressive filtering
              const sortedResults = results.sort((a, b) => {
                const aDesc = a.description.toLowerCase();
                const bDesc = b.description.toLowerCase();
                
                // Check for nearby cities if we know them
                const ncrKeywords = ['gurgaon', 'gurugram', 'delhi', 'noida', 'ghaziabad', 'faridabad'];
                const aHasNCR = ncrKeywords.some(keyword => aDesc.includes(keyword));
                const bHasNCR = ncrKeywords.some(keyword => bDesc.includes(keyword));
                
                if (aHasNCR && !bHasNCR) return -1;
                if (!aHasNCR && bHasNCR) return 1;
                
                return 0;
              });
              
              console.log('Google Maps API returned sorted predictions:', sortedResults);
              resolve(sortedResults);
            } else {
              console.log('Google Maps API returned predictions:', results);
              resolve(results);
            }
          } else {
            // Handle different error statuses
            switch (status) {
              case google.maps.places.PlacesServiceStatus.ZERO_RESULTS:
                console.log('No matching locations found for:', input);
                break;
              case google.maps.places.PlacesServiceStatus.OVER_QUERY_LIMIT:
                console.error('Google Maps API query limit exceeded');
                break;
              case google.maps.places.PlacesServiceStatus.REQUEST_DENIED:
                console.error('Google Maps API request denied. Check API key and restrictions');
                break;
              case google.maps.places.PlacesServiceStatus.INVALID_REQUEST:
                console.error('Invalid Google Maps API request:', request);
                break;
              default:
                console.warn('Google Maps API returned no predictions:', status);
            }
            resolve([]);
          }
        }
      );
    } catch (error) {
      console.error('Error getting address predictions:', error);
      resolve([]);
    }
  });
};

/**
 * Get place details from Google Places API
 * @param placeId The place ID to get details for
 * @returns Promise that resolves with the place details
 */
export const getPlaceDetails = async (
  placeId: string
): Promise<{
  formatted_address?: string;
  address_components?: Array<{
    long_name: string;
    short_name: string;
    types: string[];
  }>;
  geometry?: {
    location: {
      lat: () => number;
      lng: () => number;
    };
  };
} | null> => {
  return new Promise((resolve, reject) => {
    try {
      // Check if Google Maps API is loaded
      if (!window.google || !window.google.maps || !window.google.maps.places) {
        console.error('Google Maps API not loaded');
        resolve(null);
        return;
      }

      // Create a PlacesService instance (requires a DOM element)
      const placesService = new window.google.maps.places.PlacesService(document.createElement('div'));
      
      // Get place details with more fields for better address extraction
      placesService.getDetails(
        {
          placeId: placeId,
          fields: ['address_component', 'formatted_address', 'geometry', 'name', 'vicinity']
        },
        (place, status) => {
          if (status === window.google.maps.places.PlacesServiceStatus.OK && place) {
            console.log('Place details:', place);
            resolve(place);
          } else {
            console.warn('No place details or API error:', status);
            resolve(null);
          }
        }
      );
    } catch (error) {
      console.error('Error fetching place details:', error);
      resolve(null);
    }
  });
};

/**
 * Process place details to extract address components
 * @param place The place details from Google Places API
 * @returns Object with extracted address components and coordinates
 */
export const processPlaceDetails = (place: any): {
  houseNumber: string;
  building: string;
  landmark: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
  lattitute: string;
  longitute: string;
  fullFormattedAddress: string;
} => {
  // Default values
  const result = {
    houseNumber: '',
    building: '',
    landmark: '',
    city: '',
    state: '',
    country: 'India',
    postalCode: '',
    lattitute: '',
    longitute: '',
    fullFormattedAddress: place.formatted_address || ''
  };
  
  // Extract coordinates if available
  if (place.geometry && place.geometry.location) {
    result.lattitute = place.geometry.location.lat().toFixed(6);
    result.longitute = place.geometry.location.lng().toFixed(6);
  }
  
  // Extract address components if available
  if (place.address_components) {
    for (const component of place.address_components) {
      const types = component.types;
      
      if (types.includes('street_number')) {
        result.houseNumber = component.long_name;
      } else if (types.includes('route')) {
        result.building = component.long_name;
      } else if (types.includes('sublocality_level_1') || types.includes('sublocality')) {
        // If building is empty, use sublocality
        if (!result.building) {
          result.building = component.long_name;
        } else {
          // Otherwise, use as landmark if landmark is empty
          if (!result.landmark) {
            result.landmark = component.long_name;
          }
        }
      } else if (types.includes('locality')) {
        result.city = component.long_name;
      } else if (types.includes('administrative_area_level_1')) {
        result.state = component.long_name;
      } else if (types.includes('country')) {
        result.country = component.long_name;
      } else if (types.includes('postal_code')) {
        result.postalCode = component.long_name;
      } else if (types.includes('point_of_interest') || types.includes('establishment')) {
        if (!result.landmark) {
          result.landmark = component.long_name;
        }
      }
    }
  }
  
  // If postal_code is still empty, try to extract it from the formatted address
  if (!result.postalCode && place.formatted_address) {
    const postalCodeMatch = place.formatted_address.match(/\b\d{6}\b/);
    if (postalCodeMatch) {
      result.postalCode = postalCodeMatch[0];
    } else {
      // Default postal code based on city
      if (result.city === 'Noida' || place.formatted_address.toLowerCase().includes('noida')) {
        result.postalCode = '201301';
      } else if (result.city === 'Delhi' || place.formatted_address.toLowerCase().includes('delhi')) {
        result.postalCode = '110001';
      } else {
        result.postalCode = '110001'; // Default fallback
      }
    }
  }
  
  // Use the formatted address as the building/location if route is empty
  if (!result.building && place.formatted_address) {
    const addressParts = place.formatted_address.split(',');
    if (addressParts.length > 0) {
      result.building = addressParts[0].trim();
    }
  }
  
  return result;
};

/**
 * Create a mock place object for fallback when Google Maps API fails
 * @param description The location description
 * @returns A mock place object with basic address components
 */
export const createMockPlace = (description: string): any => {
  const parts = description.split(',').map(part => part.trim());
  
  // Extract possible postal code from the description
  let postalCode = '';
  
  // Check for Noida or Delhi in the description
  if (description.toLowerCase().includes('noida')) {
    postalCode = '201301'; // Default Noida postal code
  } else if (description.toLowerCase().includes('delhi')) {
    postalCode = '110001'; // Default Delhi postal code
  } else {
    postalCode = '110001'; // Default fallback
  }
  
  // Try to extract postal code from description
  const postalCodeMatch = description.match(/\b\d{6}\b/); // Match 6-digit postal code
  if (postalCodeMatch) {
    postalCode = postalCodeMatch[0];
  }
  
  // Create mock address components
  const mockPlace = {
    address_components: [
      { long_name: '123', types: ['street_number'] },
      { long_name: parts[0], types: ['route'] },
      { long_name: parts.length > 1 ? parts[1] : '', types: ['locality'] },
      { long_name: parts.length > 2 ? parts[2] : 'Haryana', types: ['administrative_area_level_1'] },
      { long_name: 'India', types: ['country'] },
      { long_name: postalCode, types: ['postal_code'] }
    ],
    formatted_address: description,
    // Add mock geometry with default coordinates for Noida
    geometry: {
      location: {
        lat: () => 28.5355,
        lng: () => 77.3910
      }
    }
  };
  
  return mockPlace;
};

/**
 * Geocode an address to get its coordinates
 * @param address The address to geocode
 * @returns Promise that resolves with the coordinates
 */
export const geocodeAddress = async (address: string): Promise<{ lat: number; lng: number } | null> => {
  return new Promise((resolve, reject) => {
    try {
      const geocoder = new google.maps.Geocoder();
      
      geocoder.geocode(
        { address },
        (results, status) => {
          if (status === google.maps.GeocoderStatus.OK && results && results.length > 0) {
            const location = results[0].geometry.location;
            resolve({
              lat: location.lat(),
              lng: location.lng()
            });
          } else {
            console.warn('Geocoding failed:', status);
            resolve(null);
          }
        }
      );
    } catch (error) {
      console.error('Error geocoding address:', error);
      resolve(null);
    }
  });
};

/**
 * Reverse geocode coordinates to get an address
 * @param lat Latitude
 * @param lng Longitude
 * @returns Promise that resolves with the address
 */
export const reverseGeocode = async (lat: number, lng: number): Promise<string | null> => {
  return new Promise((resolve, reject) => {
    try {
      const geocoder = new google.maps.Geocoder();
      const latlng = { lat, lng };
      
      geocoder.geocode(
        { location: latlng },
        (results, status) => {
          if (status === google.maps.GeocoderStatus.OK && results && results.length > 0) {
            resolve(results[0].formatted_address);
          } else {
            console.warn('Reverse geocoding failed:', status);
            resolve(null);
          }
        }
      );
    } catch (error) {
      console.error('Error reverse geocoding:', error);
      resolve(null);
    }
  });
};
