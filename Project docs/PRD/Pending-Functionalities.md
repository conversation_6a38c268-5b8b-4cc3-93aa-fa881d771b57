# HomeFoodi - Pending Functionalities (As Per PRD)

<!--
File: Pending-Functionalities.md
Description: This file contains a comprehensive list of all pending functionalities from the Product Requirements Document (PRD) for the HomeFoodi project.
Last Updated: 2025-03-18
-->

## Home Page Module
- [ ] Location Detection:
  - Browser location detection implementation
  - IP-based location fallback
  - Location validation
- [ ] Navigation Options:
  - Home Chefs, Tiffin Suppliers, and Caterer section redirects
  - Hero banner navigation buttons
- [ ] Call-to-Action Buttons:
  - "View More" redirects to respective service listing pages
- [ ] Vendor Registration:
  - "Join Us" button implementation and redirect
- [ ] Footer Links:
  - Quick Links section (About Us, Join Us, Blog, Privacy, Terms)
  - Support section (Contact pop-up, FAQ, Feedback emailer)
  - Social Media Icons with links
- [ ] Hero Section:
  - Location dropdown implementation
  - Vendor selection dropdown
- [ ] Content Display:
  - Service descriptions with appropriate images

## Service Listing Page Module
- [ ] Service Listing:
  - Proximity-based vendor listing
  - Service delivery range filtering
  - Vendor card implementation with all required details
- [ ] Filters and Sorting:
  - Veg/Non-Veg toggle
  - Cuisine dropdown (multi-select)
  - Services dropdown (single-select)
  - Rating dropdown
  - Price range selector
  - Distance slider
  - Sorting options (Distance, Rating, Price)
- [ ] Navigation and Switching:
  - Category switching via header
  - Vendor card redirects
- [ ] Action Buttons:
  - Call Now functionality
  - Send Inquiry SMS implementation
  - WhatsApp Chat integration
  - Direction mapping integration
- [ ] Results Counter:
  - Total results display

## Selected Service Listing Page Module
- [ ] Vendor Details Display:
  - Complete vendor information section
  - Distance calculation
  - Ratings and reviews display
  - Address and delivery information
- [ ] Image Display:
  - Caterer: 10 images with scroller
  - Tiffin Service/Home Chefs: Single image on mobile
- [ ] Sticky Action Bar:
  - Bottom-fixed action buttons until footer
- [ ] Information Tabs:
  - Menu tab with vendor-specific menu items
  - Review tab implementation
  - Preferences tab
  - Payment tab
  - About Us tab
  - Service tab
- [ ] Navigation Links:
  - Header navigation for returning to listing page

## Filters Module
- [ ] Filters as Pop-Up:
  - Overlay implementation
  - Apply/Clear functionality
- [ ] Filter Categories:
  - Veg/Non-Veg selector
  - Cuisines multi-select
  - Services options
  - Rating thresholds
  - Price sorting options
  - Distance slider with ranges
- [ ] Clear All Option:
  - Reset functionality
- [ ] Apply Button:
  - Filter execution and page update
- [ ] Default Behavior:
  - Location-based sorting when filters cleared
- [ ] Filter Navigation:
  - Vertical tab menu implementation

## Action Buttons Module
- [ ] User Authentication:
  - Login requirement for button access
  - Unregistered user flow to auth module
- [ ] Button Functionalities:
  - Call Now with device/browser detection
  - Send Enquiry with SMS integration
  - WhatsApp Chat with app/web detection
  - Direction with map application integration
- [ ] Sticky Action Bar:
  - Bottom-fixed buttons until footer
- [ ] Visual Design:
  - Button styling with distinct colors and icons

## Reviews Module
- [ ] Review Submission:
  - Text input with character limit
  - Star rating implementation
- [ ] Review Display:
  - User details, date/time, rating, content
- [ ] Flagging System:
  - Vendor flagging capability
  - Admin review queue
- [ ] Pagination:
  - 10 reviews per page
  - Navigation controls
- [ ] Overall Rating:
  - Aggregate rating calculation
  - Dynamic updates

## Auth Module
- [ ] Phone Number Input:
  - Input field for login/signup
- [ ] OTP Authentication:
  - OTP generation and delivery
  - Verification process
- [ ] Sign-Up Flow:
  - Name input after OTP verification
  - Default profile image assignment
- [ ] Login Flow:
  - Existing user authentication
  - Action button access control
- [ ] Logout Option:
  - User logout functionality
- [ ] Phone Number Storage:
  - Browser form saving capability
- [ ] Validation Requirements:
  - Mobile number (10 digits, no special chars)
  - OTP (6 digits, numeric only)
  - OTP expiry (5 minutes)
- [ ] Security Requirements:
  - Data encryption
  - JWT implementation

## Menu Implementation by Vendor Type
- [ ] Home Chef Menu (table: home_chef):
  - Menu name display
  - Image handling
  - Veg/Non-veg options
  - Price per person display
  - Status indication
- [ ] Tiffin Service Menu (table: tiffin_service_menu):
  - Meal type selection (breakfast, lunch, dinner)
  - Veg/Non-veg menu options
  - Menu price display
  - Starting price display
  - Status indication
- [ ] Caterer Menu (table: caterer_menu):
  - Menu name display
  - Image handling
  - Veg/Non-veg options
  - Price per person display
  - Status indication

## Profile Setup Module
### Home Chefs
- [ ] Profile Picture Requirements:
  - Enforce 400x150 resolution
  - Image format validation
  - Size validation (max 5MB)
  - Compression if needed
  - Error handling for invalid formats
- [ ] Profile Validations:
  - About section character limit

### Tiffin Service
- [ ] Profile Management:
  - Service area definition
  - Meal type selection
  - Pricing structure
  - Operating hours

### Caterers
- [ ] Image Management:
  - Support for 10 images
  - Image format validation
  - Resolution requirements
  - Size validation (max 5MB)
- [ ] Service Type Management:
  - House Party, Marriage, etc. options

## Performance Requirements
- [ ] Image Optimization:
  - Compression
  - Lazy loading
  - WebP/AVIF format support
  - Responsive images
- [ ] Error Recovery:
  - Graceful degradation
  - Retry mechanisms
  - Fallback options
- [ ] State Management Optimization:
  - Memoization
  - Virtual scrolling
  - Debouncing/throttling

## Security Implementation
- [ ] HTTPS Configuration
- [ ] Input Validation
- [ ] Authentication Best Practices
- [ ] CORS Policies
- [ ] Data Handling Security
- [ ] Rate Limiting
- [ ] Session Management:
  - Max session duration (30 minutes)

## Accessibility Standards
- [ ] Color Contrast Compliance (4.5:1 minimum)
- [ ] Focus Management
- [ ] Heading Hierarchy
- [ ] Alternative Text for Images
- [ ] ARIA Labels
- [ ] Form Error Handling

## Error Handling & Logging
- [ ] Error Categorization:
  - User errors
  - System errors
  - Network errors
- [ ] Logging Levels Implementation:
  - ERROR: System failures
  - WARN: Potential issues
  - INFO: Important events
  - DEBUG: Development info
- [ ] User Feedback:
  - Clear error messages
  - Recovery instructions
  - Support contact information

## Browser Compatibility
- [ ] Chrome:
  - v132 for Windows, macOS, Android
  - v133 for iOS & iPadOS
- [ ] Mozilla:
  - v134.0.2 for all platforms
- [ ] Safari:
  - v18.1.1 for macOS
  - v17/v18 for iOS/iPadOS