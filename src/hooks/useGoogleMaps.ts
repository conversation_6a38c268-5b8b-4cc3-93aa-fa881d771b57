/**
 * useGoogleMaps Hook
 * Updated: 2025-05-26
 * Description: Enhanced hook for Google Maps API integration, including location input,
 * predictions, and user location detection with improved search functionality
 * 
 * Geolocation usage justification:
 * - Essential for showing users nearby food services within their delivery radius
 * - Required to calculate accurate distances between users and vendors
 * - Necessary for filtering vendors based on delivery availability to user's location
 * - Improves user experience by automatically detecting location instead of manual entry
 * - Location data is only stored locally in the user's browser
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import { debounce } from '@/lib/utils';
import { 
  loadGoogleMapsApi, 
  getAddressPredictions, 
  reverseGeocode, 
  geocodeAddress, 
  getPlaceDetails, 
  processPlaceDetails, 
  createMockPlace 
} from '@/lib/googleMaps';
import { toast } from '@/components/ui/use-toast';
import { getUserAddress, setUserAddress } from '@/lib/location';

interface UseGoogleMapsProps {
  initialLocation?: string;
  debounceTime?: number;
}

interface LocationSearchResult {
  place_id: string;
  description: string;
}

interface UseGoogleMapsReturn {
  locationInput: string;
  setLocationInput: (input: string) => void;
  predictions: LocationSearchResult[];
  selectLocation: (location: string) => Promise<void>;
  selectLocationWithPlaceId: (placeId: string, description: string) => Promise<void>;
  detectCurrentLocation: () => Promise<void>;
  isLoadingLocation: boolean;
  recentLocations: string[];
  coordinates: { lat: number; lng: number } | null;
}

/**
 * Custom hook for Google Maps API integration
 * @param props Configuration options
 * @returns Object with location input, predictions, and functions
 */
const useGoogleMaps = ({
  initialLocation = '',
  debounceTime = 300
}: UseGoogleMapsProps = {}): UseGoogleMapsReturn => {
  // State for location input and predictions - initialize from localStorage if available
  const [locationInput, setLocationInput] = useState<string>(() => {
    // Try to get the saved address using the proper function
    const savedAddress = getUserAddress();
    if (savedAddress) {
      console.log('Initializing location input from localStorage:', savedAddress);
      return savedAddress;
    }
    // Fall back to the initialLocation prop
    return initialLocation;
  });
  const [predictions, setPredictions] = useState<LocationSearchResult[]>([]);
  const [isLoadingLocation, setIsLoadingLocation] = useState<boolean>(false);
  const [isApiLoaded, setIsApiLoaded] = useState<boolean>(false);
  const [recentLocations, setRecentLocations] = useState<string[]>([]);
  const [userCoordinates, setUserCoordinates] = useState<{lat: number, lng: number} | null>(null);
  
  // Initialize coordinates from localStorage if available
  const [coordinates, setCoordinates] = useState<{ lat: number; lng: number } | null>(() => {
    const savedLat = localStorage.getItem('homefoodi_user_latitude');
    const savedLng = localStorage.getItem('homefoodi_user_longitude');
    
    if (savedLat && savedLng) {
      try {
        return {
          lat: parseFloat(savedLat),
          lng: parseFloat(savedLng)
        };
      } catch (error) {
        console.error('Error parsing coordinates from localStorage:', error);
      }
    }
    return null;
  });
  
  // Ref for session token
  const sessionTokenRef = useRef<google.maps.places.AutocompleteSessionToken | null>(null);
  
  // Load Google Maps API on mount and get user location for better search results
  useEffect(() => {
    const loadApi = async () => {
      try {
        await loadGoogleMapsApi();
        setIsApiLoaded(true);
        // Create a new session token
        sessionTokenRef.current = new google.maps.places.AutocompleteSessionToken();
        
        // Get user's approximate location for better search results
        if (navigator.geolocation) {
          navigator.geolocation.getCurrentPosition(
            (position) => {
              const { latitude, longitude } = position.coords;
              console.log('Got user coordinates for search bias:', { latitude, longitude });
              setUserCoordinates({ lat: latitude, lng: longitude });
            },
            (error) => {
              console.warn('Error getting location for search bias:', error);
              // Default to Noida coordinates if geolocation fails
              setUserCoordinates({ lat: 28.5355, lng: 77.3910 });
            },
            { timeout: 5000, maximumAge: 60000 } // 5s timeout, 1min cache
          );
        }
      } catch (error) {
        console.error('Failed to load Google Maps API:', error);
        toast({
          title: 'Error',
          description: 'Failed to load location services. Please try again later.',
          variant: 'destructive',
        });
      }
    };
    
    loadApi();
  }, []);
  
  // Load recent locations from localStorage on mount
  useEffect(() => {
    const storedLocations = localStorage.getItem('recentLocations');
    if (storedLocations) {
      try {
        const parsed = JSON.parse(storedLocations);
        setRecentLocations(Array.isArray(parsed) ? parsed : []);
      } catch (error) {
        console.error('Error parsing recent locations:', error);
        setRecentLocations([]);
      }
    }
  }, []);
  
  // Function to fetch predictions based on input
  const fetchPredictions = useCallback(async (input: string) => {
    if (!isApiLoaded) {
      console.warn('Google Maps API not loaded yet');
      setPredictions([]);
      return;
    }
    
    if (!input.trim() || input.trim().length < 2) {
      setPredictions([]);
      return;
    }
    
    try {
      // Create a new session token if needed
      if (!sessionTokenRef.current) {
        sessionTokenRef.current = new google.maps.places.AutocompleteSessionToken();
      }
      
      const result = await getAddressPredictions(input, sessionTokenRef.current, userCoordinates);
      setPredictions(result);
    } catch (error) {
      console.error('Error fetching predictions:', error);
      setPredictions([]);
      toast({
        title: 'Location Search Error',
        description: 'Could not fetch location suggestions. Please try again.',
        variant: 'destructive',
        duration: 3000,
      });
    }
  }, [isApiLoaded, userCoordinates]);
  
  // Debounced version of fetchPredictions
  const debouncedFetchPredictions = useCallback(
    debounce((input: string) => fetchPredictions(input), debounceTime),
    [fetchPredictions, debounceTime]
  );
  
  // Update predictions when locationInput changes
  useEffect(() => {
    // Skip empty input
    if (!locationInput.trim() || !isApiLoaded) {
      setPredictions([]);
      return;
    }
    
    // Create a new session token if needed
    if (!sessionTokenRef.current && window.google?.maps?.places) {
      sessionTokenRef.current = new google.maps.places.AutocompleteSessionToken();
    }
    
    // Debounced function to get predictions
    const getPredictions = debounce(async () => {
      try {
        // Get predictions from Google Maps API with user coordinates for better results
        const results = await getAddressPredictions(locationInput, sessionTokenRef.current!, userCoordinates);
        setPredictions(results);
      } catch (error) {
        console.error('Error getting predictions:', error);
        setPredictions([]);
      }
    }, debounceTime);
    
    getPredictions();
    
    // No need for cleanup as our debounce implementation doesn't have a cancel method
  }, [locationInput, isApiLoaded, debounceTime, userCoordinates]);
  
  // Function to select a location
  const selectLocation = useCallback(async (location: string) => {
    if (!location.trim() || !isApiLoaded) {
      return;
    }
    
    setIsLoadingLocation(true);
    
    try {
      // Geocode the location to get coordinates
      const coords = await geocodeAddress(location);
      
      if (coords) {
        setCoordinates(coords);
        
        // Save to localStorage
        localStorage.setItem('homefoodi_user_latitude', coords.lat.toString());
        localStorage.setItem('homefoodi_user_longitude', coords.lng.toString());
        
        // Add to recent locations
        const updatedLocations = [location, ...recentLocations.filter(loc => loc !== location)].slice(0, 5);
        setRecentLocations(updatedLocations);
        localStorage.setItem('recentLocations', JSON.stringify(updatedLocations));
        
        // Save the address using the location service
        setUserAddress(location);
        
        // Dispatch event to notify other components
        window.dispatchEvent(new CustomEvent('locationSelected', { detail: location }));
        
        // Reset session token
        sessionTokenRef.current = new google.maps.places.AutocompleteSessionToken();
      } else {
        throw new Error('Failed to geocode location');
      }
    } catch (error) {
      console.error('Error selecting location:', error);
      toast({
        title: 'Error',
        description: 'Failed to set your location. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingLocation(false);
    }
  }, [isApiLoaded, recentLocations]);
  
  // Function to select a location with place ID for more accurate results
  const selectLocationWithPlaceId = useCallback(async (placeId: string, description: string) => {
    if (!placeId || !description.trim() || !isApiLoaded) {
      return;
    }
    
    setIsLoadingLocation(true);
    
    try {
      let placeDetails;
      
      // Check if it's a mock place ID (for fallback)
      if (placeId.startsWith('mock_')) {
        placeDetails = createMockPlace(description);
      } else {
        // Get place details from Google Maps API
        placeDetails = await getPlaceDetails(placeId);
        
        // If no place details, use mock place
        if (!placeDetails) {
          placeDetails = createMockPlace(description);
        }
      }
      
      // Process place details to extract address components
      const addressDetails = processPlaceDetails(placeDetails);
      
      // Set coordinates if available
      if (addressDetails.lattitute && addressDetails.longitute) {
        const coords = {
          lat: parseFloat(addressDetails.lattitute),
          lng: parseFloat(addressDetails.longitute)
        };
        
        setCoordinates(coords);
        
        // Save to localStorage
        localStorage.setItem('homefoodi_user_latitude', coords.lat.toString());
        localStorage.setItem('homefoodi_user_longitude', coords.lng.toString());
      }
      
      // Update location input
      setLocationInput(description);
      
      // Add to recent locations
      const updatedLocations = [description, ...recentLocations.filter(loc => loc !== description)].slice(0, 5);
      setRecentLocations(updatedLocations);
      localStorage.setItem('recentLocations', JSON.stringify(updatedLocations));
      
      // Save the address using the location service
      setUserAddress(description);
      
      // Dispatch event to notify other components
      window.dispatchEvent(new CustomEvent('locationSelected', { detail: description }));
      
      // Reset session token
      sessionTokenRef.current = new google.maps.places.AutocompleteSessionToken();
    } catch (error) {
      console.error('Error selecting location with place ID:', error);
      toast({
        title: 'Error',
        description: 'Failed to set your location. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingLocation(false);
    }
  }, [isApiLoaded, recentLocations]);
  
  /**
   * Detects the user's current location using the browser's geolocation API
   * This function is necessary to provide users with relevant nearby food services
   * and to determine which vendors can deliver to their location
   */
  const detectCurrentLocation = useCallback(async () => {
    if (!isApiLoaded) {
      toast({
        title: 'Error',
        description: 'Location services are not available. Please try again later.',
        variant: 'destructive',
      });
      return;
    }
    
    setIsLoadingLocation(true);
    
    try {
      // Request user's geolocation - necessary to show nearby food services
      // and determine which vendors can deliver to the user's location
      if (!navigator.geolocation) {
        throw new Error('Geolocation is not supported by your browser');
      }
      
      // Show toast to indicate we're detecting location and explain why it's needed
      toast({
        title: 'Detecting Location',
        description: 'Please allow location access to find nearby food services that deliver to your area...',
        duration: 3000,
      });
      
      // Request geolocation with appropriate options for optimal user experience
      // High accuracy is necessary to ensure users see the correct nearby vendors
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          enableHighAccuracy: true,  // Necessary for accurate vendor distance calculation
          timeout: 10000,            // Increased timeout for better reliability
          maximumAge: 0              // Always get fresh location data for accurate results
        });
      });
      
      const { latitude, longitude } = position.coords;
      console.log('Location detected:', latitude, longitude);
      
      // Save coordinates directly
      const coords = { lat: latitude, lng: longitude };
      setCoordinates(coords);
      
      // Reverse geocode to get address
      const address = await reverseGeocode(latitude, longitude);
      
      if (address) {
        setLocationInput(address);
        // Don't call selectLocation here as it would trigger another geocoding
        // Just update the location input and recent locations
        
        // Store only the most recent location
        setRecentLocations([address]);
        // Save to localStorage
        localStorage.setItem('recentLocations', JSON.stringify([address]));
        
        // Reset session token
        sessionTokenRef.current = new google.maps.places.AutocompleteSessionToken();
        
        toast({
          title: 'Location Detected',
          description: `Your location has been set to ${address} (${latitude.toFixed(6)}, ${longitude.toFixed(6)})`,
          duration: 4000,
        });
      } else {
        // Even if we couldn't get an address, we still have coordinates
        toast({
          title: 'Location Detected',
          description: `Your coordinates: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
          duration: 4000,
        });
      }
    } catch (error) {
      console.error('Error detecting location:', error);
      
      let errorMessage = 'Failed to detect your location. Please try again or enter it manually.';
      let errorTitle = 'Location Error';
      
      if (error instanceof Error) {
        if (error.message.includes('permission')) {
          errorTitle = 'Permission Denied';
          errorMessage = 'Location permission denied. Please allow location access in your browser settings and try again.';
        } else if (error.message.includes('timeout')) {
          errorTitle = 'Timeout Error';
          errorMessage = 'Location detection timed out. Please try again or enter your location manually.';
        } else if (error.message.includes('POSITION_UNAVAILABLE')) {
          errorTitle = 'Position Unavailable';
          errorMessage = 'Your current position is unavailable. Please check your device settings or try again later.';
        }
      }
      
      toast({
        title: errorTitle,
        description: errorMessage,
        variant: 'destructive',
        duration: 5000,
      });
    } finally {
      setIsLoadingLocation(false);
    }
  }, [isApiLoaded, selectLocation]);
  
  return {
    locationInput,
    setLocationInput,
    predictions,
    selectLocation,
    selectLocationWithPlaceId,
    detectCurrentLocation,
    isLoadingLocation,
    recentLocations,
    coordinates
  };
};

export default useGoogleMaps;
