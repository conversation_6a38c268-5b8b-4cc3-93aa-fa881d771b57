/**
 * JWT utility functions for authentication
 * Created: 2025-03-20
 * This file contains functions for generating and validating JWT tokens for user authentication
 */

// Simple JWT implementation
export interface JwtPayload {
  userId: string;
  phoneNumber: string;
  name?: string;
  exp?: number;
}

// Generate a JWT token
export const generateToken = (payload: JwtPayload): string => {
  // Set token expiration to 1 hour from now
  const expirationTime = Math.floor(Date.now() / 1000) + 60 * 60; // 1 hour
  
  // Add expiration to payload
  const tokenPayload = {
    ...payload,
    exp: expirationTime
  };
  
  // In a real implementation, we would use a proper JWT library with signing
  // For this demo, we'll create a simple encoded token
  const encodedPayload = btoa(JSON.stringify(tokenPayload));
  
  // In production, this would use proper JWT signing with a secret key
  return encodedPayload;
};

// Verify a JWT token
export const verifyToken = (token: string): JwtPayload | null => {
  try {
    // Decode the token
    const decodedPayload = JSON.parse(atob(token));
    
    // Check if token is expired
    const currentTime = Math.floor(Date.now() / 1000);
    if (decodedPayload.exp && decodedPayload.exp < currentTime) {
      // Token has expired
      return null;
    }
    
    return decodedPayload;
  } catch (error) {
    console.error('Error verifying token:', error);
    return null;
  }
};

// Store token in localStorage
export const storeToken = (token: string): void => {
  localStorage.setItem('homefoodi_auth_token', token);
};

// Get token from localStorage
export const getToken = (): string | null => {
  return localStorage.getItem('homefoodi_auth_token');
};

// Remove token from localStorage (logout)
export const removeToken = (): void => {
  localStorage.removeItem('homefoodi_auth_token');
};

// Check if user is authenticated
export const isAuthenticated = (): boolean => {
  const token = getToken();
  if (!token) return false;
  
  const payload = verifyToken(token);
  return payload !== null;
};
