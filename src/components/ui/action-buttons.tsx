/**
 * Action Buttons Component
 * Updated: 2025-04-25
 * This file contains the ActionButtons component used for vendor interactions (call, chat, enquiry, direction)
 * Updates: Added environment variable for payment API base URL
 */

import React, { useState } from "react";
import { Phone, MessageSquare, MessageCircle, Navigation } from "lucide-react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "./button";
import { useToast } from "@/hooks/use-toast";
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle,
  DialogFooter
} from "./dialog";
import { Input } from "./input";
import { Textarea } from "./textarea";
import { trackVendorInteraction, InteractionType } from "@/services/interactionService";
import { useAuth } from "../../contexts/AuthContext";
import { isAuthenticated, getUserId } from "@/utils/authUtils";

interface ActionButtonsProps {
  phoneNumber?: string;
  whatsappNumber?: string;
  smsNumber?: string; // Added SMS number field
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  vendorName?: string;
  address?: string;
  vendorId?: string;
  vendorType?: string;
  className?: string;
  variant?: "default" | "sticky";
}

export function ActionButtons({
  phoneNumber,
  whatsappNumber,
  smsNumber,
  coordinates,
  vendorName,
  address,
  vendorId,
  vendorType,
  className,
  variant = "default",
}: ActionButtonsProps) {
  const { toast } = useToast();
  const { user } = useAuth();
  const [enquiryOpen, setEnquiryOpen] = useState(false);
  const [thankYouOpen, setThankYouOpen] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    phone_number: "",
    message: ""
  });
  
  // Helper function to track interactions
  const trackInteraction = async (interactionType: InteractionType) => {
    if (!vendorId) {
      console.log('Cannot track interaction: missing vendorId');
      return;
    }
    
    // Check if user is authenticated
    if (!isAuthenticated()) {
      toast({
        title: "Authentication Required",
        description: `Please log in to ${interactionType.toLowerCase()} this vendor`,
        variant: "destructive"
      });
      

      return false;
    }
    
    const userId = getUserId();
    if (!userId) {
      toast({
        title: "Authentication Required",
        description: `Please log in to ${interactionType.toLowerCase()} this vendor`,
        variant: "destructive"
      });
      return false;
    }
    
    try {
      // Get vendor_type from localStorage if not provided via props
      const storedVendorType = localStorage.getItem('vendor_type') || vendorType || null;
      
      await trackVendorInteraction(vendorId, userId, interactionType, undefined, undefined, undefined, storedVendorType);
      return true;
    } catch (error) {
      console.error(`Error tracking ${interactionType} interaction:`, error);
      return false;
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    // For phone_number field, only allow digits and limit to 10 characters
    if (name === 'phone_number') {
      const digitsOnly = value.replace(/\D/g, '');
      const truncated = digitsOnly.slice(0, 10);
      
      setFormData(prev => ({
        ...prev,
        [name]: truncated
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmitEnquiry = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Check if user is authenticated
    if (!isAuthenticated()) {
      toast({
        title: "Authentication Required",
        description: "Please log in to send an enquiry",
        variant: "destructive"
      });
      

      return;
    }
    
    const userId = getUserId();
    if (!userId) {
      toast({
        title: "Authentication Required",
        description: "Please log in to send an enquiry",
        variant: "destructive"
      });
      return;
    }
    
    // Validate phone number (must be exactly 10 digits)
    const phoneRegex = /^\d{10}$/;
    if (!phoneRegex.test(formData.phone_number)) {
      toast({
        title: "Invalid Phone Number",
        description: "Please enter a valid 10-digit mobile number",
        variant: "destructive"
      });
      return;
    }
    
    // Send SMS enquiry to the vendor's SMS number
    try {
      // Use the smsNumber if available, otherwise fallback to phoneNumber
      const targetNumber = smsNumber || phoneNumber;
      
      if (!targetNumber) {
        toast({
          title: "Error",
          description: "Cannot send enquiry: Vendor contact number not available",
          variant: "destructive"
        });
        return;
      }
      
      // Prepare the data for the API call
      const enquiryData = {
        phone: targetNumber, // Vendor's SMS/phone number
        
        name: vendorName, // vendor's name
        name1: formData.name || 'Vendor' // User's name
      };
      
      
      console.log("Sending enquiry SMS with data:", enquiryData);
      
      // Get the base URL from environment variables
      const baseUrl = import.meta.env.VITE_PAYMENT_API_URL || 'https://homefoodi-payment.uatsparxit.xyz';
      
      // Make the API call to send the SMS
      const response = await fetch(`${baseUrl}/enquiry-send-sms`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(enquiryData)
      });
      
      if (!response.ok) {
        throw new Error(`Failed to send SMS: ${response.status} ${response.statusText}`);
      }
      
      console.log("Enquiry SMS sent successfully");
    } catch (error) {
      console.error('Error sending enquiry SMS:', error);
      toast({
        title: "Error",
        description: "Failed to send enquiry SMS. Please try again later.",
        variant: "destructive"
      });
      return;
    }
    
    // Track the Enquiry interaction with the message
    if (vendorId) {
      try {
        // Get vendor_type from localStorage if not provided via props
        const storedVendorType = localStorage.getItem('vendor_type') || vendorType || null;
        
        // Pass name, phone_number, message and vendor_type to the interaction service
        await trackVendorInteraction(
          vendorId, 
          userId, 
          'Enquiry', 
          formData.message,
          formData.phone_number,
          formData.name,
          storedVendorType
        );
      } catch (error) {
        console.error('Error tracking enquiry interaction:', error);
      }
    }
    
    // Close the enquiry dialog and reset form
    setEnquiryOpen(false);
    setFormData({ name: "", phone_number: "", message: "" });
    
    // Show thank you modal
    setThankYouOpen(true);
  };

  const handleCall = async () => {
    if (!phoneNumber) {
      toast({
        title: "Error",
        description: "No phone number available",
        variant: "destructive",
      });
      return;
    }
    
    // Track the call interaction
    const success = await trackInteraction('Call');
    if (!success) return;
    
    // Format phone number to ensure it works with the tel: protocol
    // Remove any non-numeric characters except the leading + if present
    const formattedNumber = phoneNumber.startsWith('+') 
      ? '+' + phoneNumber.substring(1).replace(/[^0-9]/g, '')
      : phoneNumber.replace(/[^0-9]/g, '');
      
    console.log('Calling phone number:', formattedNumber);
    
    // Create a clickable link element and trigger it programmatically
    // This approach works better on mobile devices than directly changing window.location
    const link = document.createElement('a');
    link.href = `tel:${formattedNumber}`;
    link.setAttribute('target', '_blank');
    link.setAttribute('rel', 'noopener noreferrer');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleWhatsApp = async () => {
    if (!whatsappNumber) {
      toast({
        title: "Error",
        description: "No WhatsApp number available",
        variant: "destructive",
      });
      return;
    }
    
    // Track the WhatsApp interaction
    const success = await trackInteraction('WhatsApp');
    if (!success) return;

    // Format WhatsApp number to ensure it works with the wa.me link
    // WhatsApp requires country code without + symbol
    let number = whatsappNumber;
    
    // Remove any non-numeric characters
    number = number.replace(/[^0-9]/g, '');
    
    // If number doesn't start with country code, add India's code (91)
    if (number.length === 10) {
      number = '91' + number;
    }
    
    console.log('Opening WhatsApp with number:', number);
    
    // Create a clickable link element and trigger it programmatically
    // This approach works better on mobile devices than using window.open
    const link = document.createElement('a');
    link.href = `https://wa.me/${number}`;
    link.setAttribute('target', '_blank');
    link.setAttribute('rel', 'noopener noreferrer');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleEnquiry = async () => {
    if (!phoneNumber) {
      toast({
        title: "Error",
        description: "Cannot send enquiry",
        variant: "destructive",
      });
      return;
    }
    
    // Check if user is authenticated
    if (!isAuthenticated()) {
      toast({
        title: "Authentication Required",
        description: "Please log in to send an enquiry",
        variant: "destructive"
      });
      

      return;
    }
    
    // Just open the enquiry dialog without tracking interaction yet
    // Tracking will happen only when the form is submitted
    setEnquiryOpen(true);
  };

  const handleDirection = async () => {
    // Check if user is authenticated and track in database
    if (!isAuthenticated()) {
      toast({
        title: "Authentication Required",
        description: "Please log in to view directions",
        variant: "destructive"
      });
      return;
    }
    
    // Track the Direction interaction in the database
    const tracked = await trackInteraction('Direction');
    if (!tracked) {
      // If tracking failed due to authentication issues, return early
      return;
    }
    
    // First try to use the address if available
    if (address) {
      // Encode the address for the URL
      const encodedAddress = encodeURIComponent(address);
      window.open(
        `https://www.google.com/maps/dir/?api=1&destination=${encodedAddress}`,
        "_blank"
      );
      return;
    }
    
    // Fall back to coordinates if address is not available
    if (!coordinates) {
      toast({
        title: "Error",
        description: "No location information available",
        variant: "destructive",
      });
      return;
    }

    const { latitude, longitude } = coordinates;
    window.open(
      `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`,
      "_blank"
    );
  };

  return (
    <div className={cn(
      variant === "sticky" 
        ? "fixed bottom-0 left-0 right-0 bg-white shadow-[0_-4px_10px_rgba(0,0,0,0.1)] z-50 flex justify-between" 
        : "flex flex-wrap gap-2 w-full",
      className
    )}>
      <Button 
        className="rounded-md flex items-center gap-1 md:gap-2 bg-[#E87616] hover:bg-[#E87616]/90 text-white text-[10px] xs:text-xs md:text-sm flex-1 md:flex-auto justify-center py-1 md:py-2 h-auto min-w-0 max-w-full px-1 xs:px-2 md:px-3 px-4" 
        onClick={handleCall}
      >
      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="13" viewBox="0 0 12 13" fill="none" className="min-w-[12px] min-h-[12px] w-3 h-3 md:w-4 md:h-4">
<path d="M12 11.1667V10.0694C12 9.52417 11.6681 9.03389 11.1619 8.83141L9.80576 8.28897C9.16191 8.03143 8.42813 8.31041 8.11801 8.93066L8 9.16667C8 9.16667 6.33333 8.83333 5 7.5C3.66667 6.16667 3.33333 4.5 3.33333 4.5L3.56934 4.38199C4.18959 4.07187 4.46857 3.33809 4.21103 2.69424L3.66859 1.33815C3.46611 0.831935 2.97583 0.5 2.43062 0.5H1.33333C0.596954 0.5 0 1.09695 0 1.83333C0 7.72437 4.77563 12.5 10.6667 12.5C11.403 12.5 12 11.903 12 11.1667Z" fill="white"/>
</svg><span className="whitespace-nowrap">Call Now</span>
      </Button>
      <Button 
        className="rounded-md flex items-center gap-1 md:gap-2 bg-[#008001] hover:bg-[#1e8000] text-white text-[10px] xs:text-xs md:text-sm flex-1 md:flex-auto justify-center py-1 md:py-2 h-auto min-w-0 max-w-full px-1 xs:px-2 md:px-3 px-4" 
        onClick={handleWhatsApp}
      >
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none" className="min-w-[12px] min-h-[12px] w-3 h-3 md:w-4 md:h-4">
<path d="M0 16.5L1.13085 12.3935C0.431709 11.1882 0.0649474 9.8232 0.0687679 8.42776C0.0687679 4.05513 3.6447 0.5 8.03438 0.5C10.1662 0.5 12.1681 1.3251 13.6695 2.82319C15.1748 4.32129 16.0038 6.31369 16 8.43156C16 12.8042 12.4241 16.3593 8.03056 16.3593H8.02674C6.6934 16.3593 5.383 16.0247 4.21776 15.3935L0 16.5ZM4.42024 13.9601L4.66093 14.1046C5.67717 14.7053 6.8424 15.0209 8.03056 15.0247H8.03438C11.6829 15.0247 14.6552 12.0703 14.6552 8.43536C14.6552 6.67491 13.9675 5.02091 12.7182 3.77377C11.4689 2.52662 9.80324 1.84221 8.03438 1.84221C4.38586 1.8384 1.41356 4.79278 1.41356 8.42776C1.41356 9.67111 1.76122 10.884 2.42598 11.9335L2.58261 12.1844L1.91404 14.6141L4.42024 13.9601Z" fill="white"/>
<path fillRule="evenodd" clipRule="evenodd" d="M6.04386 5.11212C5.89486 4.78132 5.73822 4.77372 5.59687 4.76992C5.48225 4.76611 5.34854 4.76611 5.21482 4.76611C5.08111 4.76611 4.86716 4.81554 4.68378 5.01326C4.5004 5.21098 3.98846 5.69007 3.98846 6.66725C3.98846 7.64064 4.70289 8.58361 4.80222 8.71669C4.90155 8.84976 6.18139 10.9144 8.20241 11.7091C9.8834 12.3707 10.2272 12.2376 10.5902 12.2034C10.9531 12.1692 11.7669 11.7243 11.935 11.2604C12.0993 10.7965 12.0993 10.4011 12.0496 10.3174C11.9999 10.2338 11.8662 10.1844 11.6675 10.0855C11.4689 9.98665 10.4908 9.50756 10.3075 9.43912C10.1241 9.37448 9.99037 9.34026 9.86048 9.53798C9.72676 9.7357 9.34472 10.1806 9.2301 10.3136C9.11549 10.4467 8.99706 10.4619 8.79839 10.3631C8.59973 10.2642 7.9579 10.0551 7.19763 9.37828C6.60546 8.85357 6.20432 8.20338 6.0897 8.00566C5.97509 7.80794 6.07824 7.70148 6.17757 7.60262C6.26544 7.51516 6.37624 7.37068 6.47557 7.25661C6.5749 7.14254 6.60928 7.05889 6.67423 6.92581C6.73918 6.79273 6.70861 6.67866 6.65895 6.5798C6.60928 6.48474 6.2196 5.50376 6.04386 5.11212Z" fill="white"/>
</svg><span className="whitespace-nowrap">Chat</span>
      </Button>
      <Button 
        className="rounded-md flex items-center gap-1 md:gap-2 bg-black hover:bg-black/90 text-white text-[10px] xs:text-xs md:text-sm flex-1 md:flex-auto justify-center py-1 md:py-2 h-auto min-w-0 max-w-full px-1 xs:px-2 md:px-3 px-4" 
        onClick={handleEnquiry}
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none" className="min-w-[12px] min-h-[12px] w-3 h-3 md:w-4 md:h-4">
<path d="M13.9997 7.3704C13.9997 8.7407 13.493 10.0074 12.6409 11.0092C11.5009 12.391 9.69301 13.2777 7.66634 13.2777L4.66089 15.0625C4.15422 15.3734 3.50937 14.9474 3.57846 14.3601L3.86634 12.0916C2.32331 11.0207 1.33301 9.30494 1.33301 7.3704C1.33301 5.34373 2.41544 3.5589 4.07362 2.49951C5.09847 1.83163 6.33058 1.45166 7.66634 1.45166C11.1669 1.45166 13.9997 4.10009 13.9997 7.3704Z" stroke="white" stroke-linecap="round" stroke-linejoin="round"/>
</svg><span className="whitespace-nowrap sm:hidden">Enquiry</span>
<span className="whitespace-nowrap hidden sm:block">Send Enquiry</span>
      </Button>
      <Button 
        className="rounded-md flex items-center gap-1 md:gap-2 bg-[#245BFE] hover:bg-[#245BFE]/90 text-white text-[10px] xs:text-xs md:text-sm flex-1 md:flex-auto justify-center py-1 md:py-2 h-auto min-w-0 max-w-full px-1 xs:px-2 md:px-3 px-4" 
        onClick={handleDirection}
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="10" height="15" viewBox="0 0 10 15" fill="none" className="min-w-[12px] min-h-[12px] w-3 h-3 md:w-4 md:h-4">
<path fillRule="evenodd" clipRule="evenodd" d="M10 5.8335C10 8.45174 6.75 12.1668 5 12.1668C3.25 12.1668 0 8.19248 0 5.57424C0 2.8335 2.42267 0.833496 5 0.833496C7.57733 0.833496 10 2.8335 10 5.8335ZM6.66667 5.50016C6.66667 6.42064 5.92047 7.16683 5 7.16683C4.07953 7.16683 3.33333 6.42064 3.33333 5.50016C3.33333 4.57969 4.07953 3.8335 5 3.8335C5.92047 3.8335 6.66667 4.57969 6.66667 5.50016ZM1 13.6668C0.723858 13.6668 0.5 13.8907 0.5 14.1668C0.5 14.443 0.723858 14.6668 1 14.6668H9C9.27614 14.6668 9.5 14.443 9.5 14.1668C9.5 13.8907 9.27614 13.6668 9 13.6668H1Z" fill="white"/>
</svg><span className="whitespace-nowrap">Direction</span>
      </Button>

      {/* Enquiry Dialog */}
      <Dialog open={enquiryOpen} onOpenChange={setEnquiryOpen}>
        <DialogContent className="sm:max-w-[775px]">
          <DialogHeader>
            <DialogTitle className="text-left text-[24px] font-semibold">Send Enquiry to {vendorName || 'Vendor'}</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmitEnquiry} className="space-y-4  sm:max-w-[545px]">
            <div>
              <Input 
                placeholder="Your Name" 
                name="name" 
                value={formData.name} 
                onChange={handleInputChange} 
                required 
                className="focus-visible:outline-none border-gray-300 focus:border-[#E87616] focus:ring-[#E87616]"
              />
            </div>
            {/* <div>
              <Input 
                placeholder="Email Address" 
                name="email" 
                type="email" 
                value={formData.email} 
                onChange={handleInputChange} 
                required 
                className="focus-visible:outline-none border-gray-300 focus:border-[#E87616] focus:ring-[#E87616]"
              />
            </div> */}
            <div>
              <Input 
                placeholder="Phone (10 digits)" 
                name="phone_number" 
                type="tel" 
                value={formData.phone_number} 
                onChange={handleInputChange} 
                required 
                pattern="[0-9]{10}"
                maxLength={10}
                title="Please enter a valid 10-digit mobile number"
                className="input-number focus-visible:outline-none border-gray-300 focus:border-[#E87616] focus:ring-[#E87616]"
              />
            </div>
            <div>
              <Textarea 
                placeholder="Message" 
                name="message" 
                value={formData.message} 
                onChange={handleInputChange} 
                required 
                className="focus-visible:outline-none border-gray-300 focus:border-[#E87616] focus:ring-[#E87616] min-h-[100px]"
              />
            </div>
            <div>
              <Button 
                type="submit" 
                className="mt-[20px] bg-[#E87616] hover:bg-[#E87616]/90 text-white"
              >
                SEND
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Thank You Modal */}
      <Dialog open={thankYouOpen} onOpenChange={setThankYouOpen}>
        <DialogContent className="sm:max-w-[600px] flex flex-col items-center text-center p-8">
          <div className="w-16 h-16 flex items-center justify-center mb-4 gap-[2px]">
          <svg xmlns="http://www.w3.org/2000/svg" width="158" height="156" viewBox="0 0 158 156" fill="none">
<path d="M156.817 64.7771C157.211 66.9669 157.474 69.1233 157.605 71.2462C157.737 73.3742 157.802 75.5332 157.802 77.7231C157.802 88.4148 155.733 98.4624 151.593 107.866C147.458 117.269 141.845 125.449 134.753 132.405C127.661 139.361 119.321 144.867 109.733 148.922C100.146 152.982 89.9013 155.013 79.0003 155.013C68.0994 155.013 57.8551 152.982 48.2675 148.922C38.6799 144.867 30.34 139.361 23.2479 132.405C16.1557 125.449 10.5423 117.269 6.40785 107.866C2.26811 98.4624 0.198242 88.4148 0.198242 77.7231C0.198242 67.0313 2.26811 56.9837 6.40785 47.5802C10.5423 38.1766 16.1557 29.9968 23.2479 23.0408C30.34 16.0847 38.6799 10.5765 48.2675 6.51627C57.8551 2.46115 68.0994 0.433594 79.0003 0.433594C85.6985 0.433594 92.1682 1.2374 98.4093 2.84503C104.645 4.4578 110.521 6.68116 116.037 9.5151C118.007 10.5456 119.224 12.1223 119.686 14.2452C120.143 16.3733 119.649 18.339 118.204 20.1424C116.891 21.817 115.184 22.9119 113.082 23.4272C110.981 23.9425 109.011 23.6848 107.172 22.6543C102.969 20.4644 98.4723 18.7898 93.6812 17.6305C88.8847 16.4712 83.9911 15.8915 79.0003 15.8915C61.5325 15.8915 46.6599 21.9123 34.3826 33.954C22.1 46.0009 15.9587 60.5906 15.9587 77.7231C15.9587 94.8555 22.1 109.445 34.3826 121.492C46.6599 133.534 61.5325 139.555 79.0003 139.555C96.4681 139.555 111.343 133.534 123.626 121.492C135.903 109.445 142.042 94.8555 142.042 77.7231C142.042 76.0484 141.976 74.4048 141.845 72.792C141.714 71.1844 141.517 69.5432 141.254 67.8686C140.991 66.0652 141.288 64.3906 142.144 62.8448C142.996 61.299 144.209 60.0753 145.785 59.1736C148.018 57.8854 150.285 57.8519 152.586 59.0731C154.881 60.2994 156.292 62.2008 156.817 64.7771ZM62.4519 107.866L39.7963 85.6452C38.3516 84.2282 37.6634 82.4557 37.7317 80.3277C37.7947 78.2048 38.5486 76.4349 39.9933 75.0179C41.438 73.6009 43.2767 72.8925 45.5094 72.8925C47.7422 72.8925 49.5809 73.6009 51.0256 75.0179L67.968 91.6352L141.451 19.3695C142.896 17.9525 144.703 17.275 146.873 17.3368C149.037 17.4038 150.842 18.1458 152.286 19.5627C153.731 20.9797 154.453 22.7831 154.453 24.973C154.453 27.1629 153.731 28.9663 152.286 30.3833L73.4842 107.866C72.0395 109.283 70.2008 109.991 67.968 109.991C65.7353 109.991 63.8966 109.283 62.4519 107.866Z" fill="#1B592D"/>
</svg>
          </div>
          <h2 className="text-2xl font-bold">Thank you</h2>
          <p className="text-gray-600">Your enquiry sent successfully</p>
        </DialogContent>
      </Dialog>
    </div>
  );
}
