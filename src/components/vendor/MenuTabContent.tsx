
/**
 * MenuTabContent Component
 * 
 * Updated to fetch and display menu data based on vendor type from the appropriate tables:
 * - home_chef table for home-chef vendor type
 * - tiffin_service_menu table for tiffin_supplier vendor type
 * - caterer_menu table for caterer vendor type
 */

import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { fetchVendorMenu } from "@/services/vendorService";
import { useParams } from "react-router-dom";

// Menu items divided into two columns for fallback display
const leftMenuItems = [
  "Dal",
  "Roti",
  "Curries",
  "Combos",
  "Parathas",
  "Raita",
  "Biryani",
  "Seasonal Vegetables",
];

const rightMenuItems = [
  "Thalis",
  "Snacks",
  "Khic<PERSON>",
  "Pickles",
  "idli/Dosa",
  "Kadhi",
  "Chutney",
  "Breakfast item",
];

interface MenuTabContentProps {
  className?: string;
  vendorId?: string;
}

// Define menu item interfaces based on the database schema
interface BaseMenuItem {
  id: string;
  status: boolean;
  created_at: string;
  updated_at: string;
  isVeg?: boolean;
  isNonVeg?: boolean;
  isPricingInfo?: boolean;
  veg_price_per_person?: number | null;
  non_veg_price_per_person?: number | null;
}

interface HomeChefMenuItem extends BaseMenuItem {
  menu_name: string;
  image_url?: string;
  veg: boolean;
  veg_price_per_person?: number;
  non_veg: boolean;
  non_veg_price_per_person?: number;
}

interface TiffinServiceMenuItem extends BaseMenuItem {
  meal_type: string; // breakfast, lunch, dinner
  veg_enabled: boolean;
  veg_menu_price?: number;
  veg_starting_price?: number;
  non_veg_enabled: boolean;
  non_veg_menu_price?: number;
  non_veg_starting_price?: number;
}

interface CatererMenuItem extends BaseMenuItem {
  menu_name: string;
  image_url?: string;
  veg: boolean;
  veg_price_per_person?: number;
  non_veg: boolean;
  non_veg_price_per_person?: number;
}

type MenuItem = HomeChefMenuItem | TiffinServiceMenuItem | CatererMenuItem;

export const MenuTabContent = ({ className, vendorId }: MenuTabContentProps) => {
  const { id } = useParams<{ id: string }>();
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [vendorType, setVendorType] = useState<string>('home-chef');
  
  // Pricing information state
  const [vegPrice, setVegPrice] = useState<string>('250');
  const [nonVegPrice, setNonVegPrice] = useState<string>('300');

  useEffect(() => {
    const fetchMenuData = async () => {
      try {
        setLoading(true);
        // Get the current vendor type from localStorage
        const currentVendorType = localStorage.getItem('vendor_type') || 'home-chef';
        setVendorType(currentVendorType);
        
        // Use the vendorId from props or from the URL params
        const effectiveVendorId = vendorId || id;
        
        if (!effectiveVendorId) {
          throw new Error('Vendor ID is required');
        }
        
        // Fetch menu data
        const menuData = await fetchVendorMenu(effectiveVendorId);
        console.log('Fetched menu data:', menuData, 'for vendor type:', currentVendorType);
        setMenuItems(menuData);
        
        // Set pricing information based on menu data
        updatePricingInfo(menuData, currentVendorType);
      } catch (err) {
        console.error('Error fetching menu data:', err);
        setError('Failed to load menu data');
      } finally {
        setLoading(false);
      }
    };
    
    fetchMenuData();
  }, [id, vendorId]);
  
  // Update pricing information based on menu data and vendor type
  const updatePricingInfo = (menuData: MenuItem[], vendorType: string) => {
    console.log('Updating pricing info for vendor type:', vendorType);
    console.log('Menu data for pricing calculation:', menuData);
    if (!menuData || menuData.length === 0) {
      console.log('No menu data available for pricing calculation');
      // Set not available message for both
      setVegPrice('not-available');
      setNonVegPrice('not-available');
      return;
    }
    
    // Check if the first item is our pricing info object
    const pricingInfoItem = menuData.find(item => 'isPricingInfo' in item && item.isPricingInfo === true);
    
    if (pricingInfoItem) {
      console.log('Found pricing info from vendor_other_details:', pricingInfoItem);
      
      // Get the pricing directly from vendor_other_details
      const vegPriceFromDetails = pricingInfoItem.veg_price_per_person;
      const nonVegPriceFromDetails = pricingInfoItem.non_veg_price_per_person;
      
      // Set the prices if they exist, otherwise show not available message
      if (vegPriceFromDetails && !isNaN(vegPriceFromDetails)) {
        setVegPrice(vegPriceFromDetails.toString());
      } else {
        // For veg, if the price is null, show not available message
        setVegPrice('not-available');
      }
      
      if (nonVegPriceFromDetails && !isNaN(nonVegPriceFromDetails)) {
        setNonVegPrice(nonVegPriceFromDetails.toString());
      } else {
        // For non-veg, if the price is null, show not available message
        setNonVegPrice('not-available');
      }
    } else {
      // For veg, check if we have menu items with veg prices
      const hasVegItems = menuData.some(item => 
        (vendorType === 'home-chef' || vendorType === 'caterer') 
          ? ('veg' in item && item.veg && (item as HomeChefMenuItem).veg_price_per_person) 
          : ('veg_enabled' in item && item.veg_enabled && (item as TiffinServiceMenuItem).veg_starting_price)
      );
      
      if (hasVegItems) {
        calculatePriceFromMenuItems(menuData, vendorType, 'veg');
      } else {
        // No veg prices available, show not available message
        setVegPrice('not-available');
      }
      
      // For non-veg, check if we have menu items with non-veg prices
      const hasNonVegItems = menuData.some(item => 
        (vendorType === 'home-chef' || vendorType === 'caterer') 
          ? ('non_veg' in item && item.non_veg && (item as HomeChefMenuItem).non_veg_price_per_person) 
          : ('non_veg_enabled' in item && item.non_veg_enabled && (item as TiffinServiceMenuItem).non_veg_starting_price)
      );
      
      if (hasNonVegItems) {
        calculatePriceFromMenuItems(menuData, vendorType, 'non_veg');
      } else {
        // No non-veg prices available, show not available message
        setNonVegPrice('not-available');
      }
    }
  };
  
  // Helper function to calculate prices from menu items (fallback)
  const calculatePriceFromMenuItems = (menuData: MenuItem[], vendorType: string, priceType: 'veg' | 'non_veg') => {
    // Filter out the pricing info item if it exists
    const actualMenuItems = menuData.filter(item => !('isPricingInfo' in item));
    
    if (vendorType === 'home-chef' || vendorType === 'caterer') {
      if (priceType === 'veg') {
        // For veg prices
        const vegItems = actualMenuItems.filter(item => 'veg' in item && item.veg);
        
        if (vegItems.length > 0) {
          // Get all valid prices (greater than 0)
          const validPrices = vegItems
            .map(item => (item as HomeChefMenuItem).veg_price_per_person || 0)
            .filter(price => price > 0);
          
          // Only calculate min if we have valid prices
          if (validPrices.length > 0) {
            const minVegPrice = Math.min(...validPrices);
            
            if (minVegPrice && !isNaN(minVegPrice)) {
              setVegPrice(minVegPrice.toString());
            }
          } else {
            // Default price if no valid prices found
            setVegPrice('250');
          }
        } else {
          setVegPrice('250');
        }
      } else {
        // For non-veg prices
        const nonVegItems = actualMenuItems.filter(item => 'non_veg' in item && item.non_veg);
        
        if (nonVegItems.length > 0) {
          // Get all valid prices (greater than 0)
          const validPrices = nonVegItems
            .map(item => (item as HomeChefMenuItem).non_veg_price_per_person || 0)
            .filter(price => price > 0);
          
          // Only calculate min if we have valid prices
          if (validPrices.length > 0) {
            const minNonVegPrice = Math.min(...validPrices);
            
            if (minNonVegPrice && !isNaN(minNonVegPrice)) {
              setNonVegPrice(minNonVegPrice.toString());
            }
          } else {
            // Default price if no valid prices found
            setNonVegPrice('300');
          }
        } else {
          setNonVegPrice('300');
        }
      }
    } else if (vendorType === 'tiffin_supplier') {
      if (priceType === 'veg') {
        // For veg prices
        const vegItems = actualMenuItems.filter(item => 'veg_enabled' in item && item.veg_enabled);
        
        if (vegItems.length > 0) {
          // Get all valid prices (greater than 0)
          const validPrices = vegItems
            .map(item => (item as TiffinServiceMenuItem).veg_starting_price || 0)
            .filter(price => price > 0);
          
          // Only calculate min if we have valid prices
          if (validPrices.length > 0) {
            const minVegPrice = Math.min(...validPrices);
            
            if (minVegPrice && !isNaN(minVegPrice)) {
              setVegPrice(minVegPrice.toString());
            }
          } else {
            // Default price if no valid prices found
            setVegPrice('250');
          }
        } else {
          setVegPrice('250');
        }
      } else {
        // For non-veg prices
        const nonVegItems = actualMenuItems.filter(item => 'non_veg_enabled' in item && item.non_veg_enabled);
        
        if (nonVegItems.length > 0) {
          // Get all valid prices (greater than 0)
          const validPrices = nonVegItems
            .map(item => (item as TiffinServiceMenuItem).non_veg_starting_price || 0)
            .filter(price => price > 0);
          
          // Only calculate min if we have valid prices
          if (validPrices.length > 0) {
            const minNonVegPrice = Math.min(...validPrices);
            
            if (minNonVegPrice && !isNaN(minNonVegPrice)) {
              setNonVegPrice(minNonVegPrice.toString());
            }
          } else {
            // Default price if no valid prices found
            setNonVegPrice('300');
          }
        } else {
          setNonVegPrice('300');
        }
      }
    }
  };
  
  // Helper function to get veg and non-veg menu items based on vendor type
  const getMenuItems = () => {
    console.log('Getting menu items for vendor type:', vendorType, 'with menu items:', menuItems);
    // Extract veg and non-veg menu names based on vendor type
    let vegMenuNames: string[] = [];
    let nonVegMenuNames: string[] = [];
    
    // Filter out the pricing info item if it exists
    const actualMenuItems = menuItems.filter(item => !('isPricingInfo' in item));
    
    if (vendorType === 'home-chef' || vendorType === 'caterer') {
      // For home chef and caterer, filter by veg and non_veg flags
      vegMenuNames = actualMenuItems
        .filter(item => 'veg' in item && item.veg === true && 'menu_name' in item)
        .map(item => (item as HomeChefMenuItem).menu_name);
      console.log('Veg menu names for', vendorType, ':', vegMenuNames);
        
      nonVegMenuNames = actualMenuItems
        .filter(item => 'non_veg' in item && item.non_veg === true && 'menu_name' in item)
        .map(item => (item as HomeChefMenuItem).menu_name);
      console.log('Non-veg menu names for', vendorType, ':', nonVegMenuNames);
        
      // Only use real data from database, no fallback to static data
      // This ensures we only display actual menu items from the database
      return { 
        vegItems: vegMenuNames,
        nonVegItems: nonVegMenuNames 
      };
    } else if (vendorType === 'tiffin_supplier') {
      // For tiffin service, filter by veg_enabled and non_veg_enabled flags
      vegMenuNames = actualMenuItems
        .filter(item => 'veg_enabled' in item && item.veg_enabled === true && 'meal_type' in item)
        .map(item => (item as TiffinServiceMenuItem).meal_type);
        
      nonVegMenuNames = actualMenuItems
        .filter(item => 'non_veg_enabled' in item && item.non_veg_enabled === true && 'meal_type' in item)
        .map(item => (item as TiffinServiceMenuItem).meal_type);
        
      // For tiffin service, only show actual data from database, no fallback
      return { 
        vegItems: vegMenuNames,
        nonVegItems: nonVegMenuNames 
      };
    }
    
    // Return empty arrays if no menu items found
    return { vegItems: [], nonVegItems: [] };
  };

  // Get veg and non-veg menu items or fallback to static ones
  const { vegItems, nonVegItems } = getMenuItems();

  // CheckCircle SVG component for menu items
  const CheckCircleIcon = () => (
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      width="24" 
      height="24" 
      viewBox="0 0 24 24" 
      fill="none" 
      stroke="currentColor" 
      strokeWidth="2" 
      strokeLinecap="round" 
      strokeLinejoin="round" 
      className="h-5 w-5 text-[#008001]"
    >
      <path d="M21.801 10A10 10 0 1 1 17 3.335"></path>
      <path d="m9 11 3 3L22 4"></path>
    </svg>
  );

  return (
    <div className={cn("mb-20 border-t border-[#E6E6E6]", className)}>
      {/* Veg/Non-Veg pricing section */}
      <div className="flex flex-col md:flex-row gap-10 bg-[#F8F8FA] p-5 mb-6">
        <div className="flex items-center gap-2">
          <div className="relative w-3.5 h-3.5 border border-[#008001]">
            <div className="absolute top-[2px] left-[2px] w-2 h-2 bg-[#008001] rounded-full"></div>
          </div>
          <span className="font-medium text-[#2C2F24]">Veg:</span>
          <span className="text-sm text-[#000]">
            {vegPrice === 'not-available' ? (
              "Price not available"
            ) : (
              <>Starting from ₹ {vegPrice} {vendorType !== 'tiffin_supplier' ? 'Per Person' : ''}</>
            )}
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="relative w-3.5 h-3.5 border border-[#FF0000] pt-[7px] pr-[8px] pb-[7px] pl-[7px] relative">
            <div className="w-0 h-0 absolute top-[2px] left-[2px] 
              border-l-[6px] border-l-transparent 
              border-r-[6px] border-r-transparent 
              border-b-[8px] border-b-[#FF0000]">
            </div>
          </div>
          <span className="font-medium text-[#2C2F24]">Non Veg:</span>
          <span className="text-sm text-[#000]">
            {nonVegPrice === 'not-available' ? (
              "Price not available"
            ) : (
              <>Starting from ₹ {nonVegPrice} {vendorType !== 'tiffin_supplier' ? 'Per Person' : ''}</>
            )}
          </span>
        </div>
      </div>

      {/* Menu Items section */}
      <h2 className="text-xl font-bold text-[#2C2F24] mb-6 ml-5">Menu Item</h2>
      
      <div className="flex flex-col md:flex-row gap-[50px] md:gap-20 mb-20 ml-5">
        {/* Vegetarian Items (Left Side) */}
        <div className="space-y-5">
          <h3 className="text-[16px] font-semibold text-[#008001] mb-4">Veg Items</h3>
          {vegItems.length > 0 ? (
            vegItems.map((item, index) => (
              <div key={index} className="flex items-center gap-2">
                <div className="flex justify-center items-center w-5 h-5">
                  <CheckCircleIcon />
                </div>
                <span className="text-[#000] font-medium text-[14px]">{item}</span>
              </div>
            ))
          ) : (
            <div className="text-gray-500 italic">No vegetarian items available</div>
          )}
        </div>
        
        <div className="hidden md:block w-px h-[303px] bg-[#D9D9D9]"></div>
        
        {/* Non-Vegetarian Items (Right Side) */}
        <div className="space-y-5">
          <h3 className="text-[16px] font-semibold text-[#FF0000] mb-4">Non-Veg Items</h3>
          {nonVegItems.length > 0 ? (
            nonVegItems.map((item, index) => (
              <div key={index} className="flex items-center gap-2">
                <div className="flex justify-center items-center w-5 h-5">
                  <CheckCircleIcon />
                </div>
                <span className="text-[#000] font-medium text-[14px]">{item}</span>
              </div>
            ))
          ) : (
            <div className="text-gray-500 italic">No non-vegetarian items available</div> 
          )}
        </div>
      </div>
    </div>
  );
};

export default MenuTabContent;
