/**
 * SSR Entry Point
 * 
 * This file is the server-side entry point for SSR rendering.
 * It renders the React app to HTML string on the server.
 * 
 * Currently prepared for future SSR implementation.
 */

import React from 'react';
import { renderToString } from 'react-dom/server';
import { StaticRouter } from 'react-router-dom/server';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Helmet } from 'react-helmet';
import App from './App';

export async function render(url: string) {
  // Create a new QueryClient for each request to avoid cross-request state pollution
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        // Disable retries during SSR
        retry: false,
        // Set stale time to prevent refetching on client
        staleTime: 60 * 1000, // 1 minute
      },
    },
  });

  // Render the app to HTML string
  const appHtml = renderToString(
    <QueryClientProvider client={queryClient}>
      <StaticRouter location={url}>
        <App />
      </StaticRouter>
    </QueryClientProvider>
  );

  // Get the helmet data for meta tags
  const helmet = Helmet.renderStatic();

  // Combine all head elements
  const head = `
    ${helmet.title.toString()}
    ${helmet.meta.toString()}
    ${helmet.link.toString()}
    ${helmet.script.toString()}
  `;

  return {
    html: appHtml,
    head: head.trim()
  };
}

// Export for potential use in other SSR setups
export default render;
