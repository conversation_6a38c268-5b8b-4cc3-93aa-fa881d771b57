# Next.js Migration Guide

This document outlines the complete migration from Vite React to Next.js with proper SSR and dynamic meta titles.

## ✅ What's Been Completed

### 1. **Project Structure Migration**
- ✅ Converted from Vite to Next.js App Router
- ✅ Updated `package.json` with Next.js dependencies
- ✅ Created `next.config.js` with proper configuration
- ✅ Updated TypeScript configuration for Next.js
- ✅ Created App Router directory structure (`app/`)

### 2. **Routing Migration**
- ✅ **Home Page**: `/` → `app/page.tsx`
- ✅ **Service Listing**: `/services` → `app/services/page.tsx`
- ✅ **Vendor Details**: `/vendor/[id]` → `app/vendor/[id]/page.tsx`
- ✅ **Signup**: `/signup` → `app/signup/page.tsx`

### 3. **SSR and Meta Title Implementation**
- ✅ **Dynamic Meta Titles**: Using Next.js `generateMetadata()` function
- ✅ **Server-Side Rendering**: Built-in with Next.js App Router
- ✅ **SEO Optimization**: Proper meta tags, Open Graph, Twitter Cards
- ✅ **Page-specific Titles**:
  - Service Listing: "Service Listing" or "Home Chefs in Delhi"
  - Vendor Details: "Service Detail" or "Vendor Name - City"

### 4. **Component Updates**
- ✅ Updated Layout component with `'use client'` directive
- ✅ Updated Header component for Next.js navigation
- ✅ Created client components for interactive functionality
- ✅ Maintained all existing UI components

## 🚀 Installation Steps

### Step 1: Install Dependencies
```bash
# Remove old dependencies and install new ones
rm -rf node_modules package-lock.json

# Install dependencies
npm install
```

### Step 2: Remove Vite Files
```bash
# Remove Vite-specific files
rm -f vite.config.ts
rm -f index.html
rm -rf dist/
rm -f tsconfig.app.json
rm -f tsconfig.node.json
```

### Step 3: Start Development Server
```bash
npm run dev
```

The application will be available at `http://localhost:8081`

## 📁 New File Structure

```
├── app/                          # Next.js App Router
│   ├── layout.tsx               # Root layout with metadata
│   ├── page.tsx                 # Home page
│   ├── providers.tsx            # Client-side providers
│   ├── globals.css              # Global styles
│   ├── services/                # Service listing pages
│   │   ├── page.tsx            # Service listing with metadata
│   │   └── ServiceListingClient.tsx
│   ├── vendor/                  # Vendor details pages
│   │   └── [id]/
│   │       ├── page.tsx        # Vendor details with metadata
│   │       └── VendorDetailsClient.tsx
│   └── signup/                  # Signup pages
│       ├── page.tsx
│       └── SignupClient.tsx
├── src/                         # Existing components (unchanged)
│   ├── components/
│   ├── hooks/
│   ├── services/
│   ├── utils/
│   └── contexts/
├── next.config.js               # Next.js configuration
├── next-env.d.ts               # Next.js TypeScript definitions
└── tsconfig.json               # Updated TypeScript config
```

## 🎯 Key Features Implemented

### 1. **Dynamic Meta Titles**
```typescript
// Service Listing
export async function generateMetadata({ searchParams }: Props): Promise<Metadata> {
  const vendorType = searchParams.type as string || 'home-chef';
  const city = searchParams.city as string;
  
  let title = 'Service Listing';
  if (city) {
    title = `${displayVendorType} in ${city}`;
  }
  
  return { title, description, openGraph: { title, description } };
}
```

### 2. **Server-Side Rendering**
- All pages are server-rendered by default
- Client components use `'use client'` directive
- Proper hydration for interactive elements

### 3. **SEO Optimization**
- Dynamic meta titles based on content
- Open Graph tags for social sharing
- Twitter Card support
- Structured data for search engines
- Proper canonical URLs

### 4. **Route Mapping**
- `/service-listing` → `/services` (with redirect)
- `/service/:city` → `/services?city=:city`
- `/service/:city/:name/:id` → `/vendor/:id?city=:city&name=:name`

## 🔧 Configuration Details

### Next.js Config (`next.config.js`)
```javascript
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  experimental: { appDir: true },
  async redirects() {
    return [
      { source: '/service-listing', destination: '/services', permanent: true }
    ];
  },
  async rewrites() {
    return [
      { source: '/service/:city', destination: '/services?city=:city' },
      { source: '/service/:city/:name/:id', destination: '/vendor/:id?city=:city&name=:name' }
    ];
  }
};
```

### Root Layout (`app/layout.tsx`)
```typescript
export const metadata: Metadata = {
  title: {
    default: 'Homefoodi - Order Fresh Home Cooked Meals',
    template: '%s | Homefoodi'
  },
  description: 'Order fresh, healthy, and customized meals...',
  openGraph: { /* ... */ },
  twitter: { /* ... */ }
};
```

## 🧪 Testing the Migration

### 1. **Test Page Titles**
- Visit `/services` → Should show "Service Listing | Homefoodi"
- Visit `/services?type=home-chef&city=Delhi` → Should show "Home Chefs in Delhi | Homefoodi"
- Visit `/vendor/123` → Should show "Service Detail | Homefoodi"

### 2. **Test SSR**
- View page source → Meta tags should be present in HTML
- Disable JavaScript → Pages should still render content
- Check Network tab → Initial HTML should contain content

### 3. **Test Navigation**
- All internal links should work
- Browser back/forward should work
- URL changes should update page titles

## 🚨 Breaking Changes

### 1. **Import Changes**
```typescript
// Old (React Router)
import { Link, useNavigate, useLocation } from 'react-router-dom';

// New (Next.js)
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
```

### 2. **Navigation Changes**
```typescript
// Old
navigate('/path');

// New
router.push('/path');
```

### 3. **Client Components**
Components with hooks or event handlers need `'use client'` directive:
```typescript
'use client';

import { useState } from 'react';
// Component code...
```

## 🎉 Benefits Achieved

1. **Better SEO**: Server-rendered pages with dynamic meta tags
2. **Faster Loading**: Built-in optimizations and code splitting
3. **Better Performance**: Automatic image optimization and caching
4. **Improved Developer Experience**: Built-in TypeScript support
5. **Production Ready**: Optimized builds and deployment options

## 🔄 Migration Complete!

The application has been successfully migrated from Vite React to Next.js with:
- ✅ Full SSR support
- ✅ Dynamic meta titles
- ✅ Proper routing
- ✅ SEO optimization
- ✅ All existing functionality preserved

Run `npm run dev` to start the Next.js development server!
