/**
 * Header component for HomeFoodi application
 * 
 * Changes made:
 * - Added functionality to store vendor_type in localStorage when service type is clicked
 * - Added vendor_type_display to localStorage for consistent UI display
 * - Added vendor type mapping to handle different formats from the database
 * - Maintained all existing UI and functionality
 */
import { images } from "@/assets/images/placeholder";
import { cn } from "@/lib/utils";
import { useState, useEffect } from "react";
import { Menu, LogOut, User as UserIcon, ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

// Import vendor type utilities
import { normalizeVendorType, getVendorTypeDisplay } from "@/utils/vendorTypeUtils";


interface NavButtonProps {
  children: React.ReactNode;
  active?: boolean;
  onClick?: () => void;
  to: string;
}

const NavButton = ({ children, active, onClick, to }: NavButtonProps) => {
  return (
    <Link to={to}>
      <div
        className={cn(
          "text-base text-center cursor-pointer px-[25px] py-2.5 rounded-[23.5px] rounded-[23.5px] border border-[#D9D9D9] hover:bg-[#ff2f1d] hover:text-white transition-all duration-300 ease-in-out",
          active && "bg-[#ff2f1d] text-white"
        )}
        onClick={onClick}
      >
        {children}
      </div>
    </Link>
  );
};

export const Header = () => {
  const [activeNav, setActiveNav] = useState("");
  const [userData, setUserData] = useState<any>(null);
  const [localIsAuthenticated, setLocalIsAuthenticated] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { isAuthenticated, logout, user } = useAuth();
  
  // Set initial active nav based on URL or localStorage on component mount
  useEffect(() => {
    // Check if we're on the service listing page or vendor detail page
    if (location.pathname === "/service-listing" || location.pathname.startsWith("/service/")) {
      // Get the vendor type from URL or localStorage
      let vendorType;
      
      if (location.pathname === "/service-listing") {
        // For service listing page, check URL parameters first
        if (location.search.includes("type=tiffin") || location.search.includes("type=tiffin_supplier")) {
          vendorType = "Tiffin Suppliers";
        } else if (location.search.includes("type=caterer") || location.search.includes("type=caters")) {
          vendorType = "Caterer";
        } else {
          vendorType = "Home Chefs";
        }
      } else if (location.pathname.startsWith("/service/")) {
        // For vendor detail page, get vendor type from localStorage
        const storedVendorType = localStorage.getItem('vendor_type');
        if (storedVendorType) {
          if (storedVendorType.includes('tiffin')) {
            vendorType = "Tiffin Suppliers";
          } else if (storedVendorType.includes('cater')) {
            vendorType = "Caterer";
          } else {
            vendorType = "Home Chefs";
          }
        } else {
          vendorType = "Home Chefs"; // Default
        }
      }
      
      // Update active nav and save to localStorage
      if (vendorType) {
        setActiveNav(vendorType);
        localStorage.setItem('activeNav', vendorType);
      }
    }
  }, [location.pathname, location.search]);
  
  // Get user data directly from localStorage
  useEffect(() => {
    const userDataString = localStorage.getItem('homefoodi_user');
    const token = localStorage.getItem('homefoodi_auth_token');
    
    if (userDataString && token) {
      try {
        const parsedUserData = JSON.parse(userDataString);
        setUserData(parsedUserData);
        setLocalIsAuthenticated(true);
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
      }
    }
  }, []);
  
  const handleLogout = () => {
    // Clear both the AuthContext state and local state
    logout();
    
    // Also clear local authentication state
    setLocalIsAuthenticated(false);
    setUserData(null);
    
    // Navigate to signup page
    navigate("/signup");
  };

  return (
    <header className="bg-white py-5 border-b border-[#ddd]">
      <div className="max-w-[1120px] flex justify-between items-center mx-auto max-md:px-5">
        <Link to="/" onClick={() => {
          // Preserve the current vendor type selection when navigating to home page
          const currentVendorType = localStorage.getItem('vendor_type') || 'home-chef';
          const currentVendorTypeDisplay = localStorage.getItem('vendor_type_display') || 'Home Chefs';
          
          // Make sure these values are preserved when returning to service listing
          localStorage.setItem('vendor_type', currentVendorType);
          localStorage.setItem('vendor_type_display', currentVendorTypeDisplay);
        }}>
          <img src={images.logo} alt="Logo" className="w-[161px] h-[37px]" />
        </Link>

        {/* Desktop Navigation */}
        <div className="flex gap-5 items-center max-lg:hidden">
          <NavButton
            to="/service-listing"
            active={activeNav === "Home Chefs"}
            onClick={() => {
              localStorage.setItem('vendor_type', 'home-chef');
              localStorage.setItem('vendor_type_display', 'Home Chefs');
              localStorage.setItem('activeNav', 'Home Chefs');
              setActiveNav("Home Chefs");
              // Dispatch custom event to notify about vendor type change
              window.dispatchEvent(new Event('vendorTypeChanged'));
            }}
          >
            Home Chefs
          </NavButton>
          <NavButton
            to="/service-listing?type=tiffin_supplier"
            active={activeNav === "Tiffin Suppliers"}
            onClick={() => {
              localStorage.setItem('vendor_type', 'tiffin_supplier');
              localStorage.setItem('vendor_type_display', 'Tiffin Suppliers');
              localStorage.setItem('activeNav', 'Tiffin Suppliers');
              setActiveNav("Tiffin Suppliers");
              // Dispatch custom event to notify about vendor type change
              window.dispatchEvent(new Event('vendorTypeChanged'));
            }}
          >
            Tiffin Suppliers
          </NavButton>
          <NavButton
            to="/service-listing?type=caterer"
            active={activeNav === "Caterer"}
            onClick={() => {
              localStorage.setItem('vendor_type', 'caterer');
              localStorage.setItem('vendor_type_display', 'Caterer');
              localStorage.setItem('activeNav', 'Caterer');
              setActiveNav("Caterer");
              // Dispatch custom event to notify about vendor type change
              window.dispatchEvent(new Event('vendorTypeChanged'));
            }}
          >
            Caterer
          </NavButton>
          
          {isAuthenticated || localIsAuthenticated ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  className="flex items-center gap-2 text-[#333] font-medium hover:bg-gray-100 focus-visible:ring-0 focus:outline-none border-0"
                >
                  {/* Circular avatar with user's first letter */}
                  <div 
                    className="w-[24px] h-[24px] rounded-full bg-[#E87616] text-white flex items-center justify-center font-bold text-xs"
                    aria-label={userData?.name || user?.name || 'Account'}
                  >
                    {(userData?.name || user?.name) ? (userData?.name || user?.name).charAt(0).toUpperCase() : 'A'}
                  </div>
                  {userData?.name || user?.name || 'Account'}
                  <ChevronDown size={14} className="text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={handleLogout} className="cursor-pointer">
                  <LogOut size={14} className="mr-2 text-[#E87616]" />
                  <span>Logout</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Link to="/signup">
              <div className="text-[#E87616] cursor-pointer py-2 flex gap-[10px] items-center justify-center filter brightness-110">

                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="15"
                  height="15"
                  viewBox="0 0 10 11"
                  fill="none"
                >
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M6 10.7749C5.725 10.7749 5.5 10.5545 5.5 10.2851C5.5 10.0158 5.725 9.79537 6 9.79537H8.5C8.775 9.79537 9 9.57497 9 9.3056V1.46931C9 1.19993 8.775 0.979537 8.5 0.979537H6C5.725 0.979537 5.5 0.759141 5.5 0.489768C5.5 0.220396 5.725 0 6 0H8.5C9.33 0 10 0.65629 10 1.46931V9.3056C10 10.1186 9.33 10.7749 8.5 10.7749H6Z"
                    fill="#E87616"
                  />
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M5.79503 5.38755L4.15003 3.77621C3.95503 3.5852 3.95503 3.27665 4.15003 3.08564C4.34503 2.89463 4.66003 2.89463 4.85503 3.08564L6.85503 5.04471C7.05003 5.23572 7.05003 5.54428 6.85503 5.73529L4.85503 7.69436C4.66003 7.88537 4.34503 7.88537 4.15003 7.69436C3.95503 7.50335 3.95503 7.1948 4.15003 7.00379L5.79503 5.39245V5.38755Z"
                    fill="#E87616"
                  />
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M6.5 4.89795C6.775 4.89795 7 5.11834 7 5.38772C7 5.65709 6.775 5.87749 6.5 5.87749H0.5C0.225 5.87749 0 5.65709 0 5.38772C0 5.11834 0.225 4.89795 0.5 4.89795H6.5Z"
                    fill="#E87616"
                  />
                </svg>
                Sign in
              </div>
            </Link>
          )}
          <a 
            href="https://homefoodi-vendor.devsparxit.com/dashboard" 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-black cursor-pointer hover:text-[#E87616] transition-colors"
          >
            Vendor Signup
          </a>
        </div>

        {/* Mobile Navigation */}
        <div className="lg:hidden">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon">
                <Menu className="h-6 w-6" />
              </Button>
            </SheetTrigger>
            <SheetContent>
              <div className="flex flex-col gap-4 mt-8">
                <Link to="/service-listing">
                  <NavButton
                    to="/service-listing"
                    active={activeNav === "Home Chefs"}
                    onClick={() => {
                      localStorage.setItem('vendor_type', 'home-chef');
                      localStorage.setItem('vendor_type_display', 'Home Chefs');
                      setActiveNav("Home Chefs");
                      // Dispatch custom event to notify about vendor type change
                      window.dispatchEvent(new Event('vendorTypeChanged'));
                    }}
                  >
                    Home Chefs
                  </NavButton>
                </Link>
                <NavButton
                  to="/service-listing?type=tiffin_supplier"
                  active={activeNav === "Tiffin Suppliers"}
                  onClick={() => {
                    localStorage.setItem('vendor_type', 'tiffin_supplier');
                    localStorage.setItem('vendor_type_display', 'Tiffin Suppliers');
                    setActiveNav("Tiffin Suppliers");
                    // Dispatch custom event to notify about vendor type change
                    window.dispatchEvent(new Event('vendorTypeChanged'));
                  }}
                >
                  Tiffin Suppliers
                </NavButton>
                <NavButton
                  to="/service-listing?type=caterer"
                  active={activeNav === "Caterer"}
                  onClick={() => {
                    localStorage.setItem('vendor_type', 'caterer');
                    localStorage.setItem('vendor_type_display', 'Caterer');
                    setActiveNav("Caterer");
                    // Dispatch custom event to notify about vendor type change
                    window.dispatchEvent(new Event('vendorTypeChanged'));
                  }}
                >
                  Caterer
                </NavButton>
                
                {isAuthenticated || localIsAuthenticated ? (
                  <Button 
                    variant="ghost" 
                    onClick={handleLogout}
                    className="text-[#E87616] cursor-pointer py-2 flex gap-[10px]"
                  >
                    <LogOut size={15} className="text-[#E87616]" />
                    Logout
                  </Button>
                ) : (
                  <Link to="/signup">
                    <div className="text-[#E87616] cursor-pointer py-2 flex gap-[10px]">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="15"
                        height="15"
                        viewBox="0 0 10 11"
                        fill="none"
                      >
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M6 10.7749C5.725 10.7749 5.5 10.5545 5.5 10.2851C5.5 10.0158 5.725 9.79537 6 9.79537H8.5C8.775 9.79537 9 9.57497 9 9.3056V1.46931C9 1.19993 8.775 0.979537 8.5 0.979537H6C5.725 0.979537 5.5 0.759141 5.5 0.489768C5.5 0.220396 5.725 0 6 0H8.5C9.33 0 10 0.65629 10 1.46931V9.3056C10 10.1186 9.33 10.7749 8.5 10.7749H6Z"
                          fill="#E87616"
                        />
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M5.79503 5.38755L4.15003 3.77621C3.95503 3.5852 3.95503 3.27665 4.15003 3.08564C4.34503 2.89463 4.66003 2.89463 4.85503 3.08564L6.85503 5.04471C7.05003 5.23572 7.05003 5.54428 6.85503 5.73529L4.85503 7.69436C4.66003 7.88537 4.34503 7.88537 4.15003 7.69436C3.95503 7.50335 3.95503 7.1948 4.15003 7.00379L5.79503 5.39245V5.38755Z"
                          fill="#E87616"
                        />
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M6.5 4.89795C6.775 4.89795 7 5.11834 7 5.38772C7 5.65709 6.775 5.87749 6.5 5.87749H0.5C0.225 5.87749 0 5.65709 0 5.38772C0 5.11834 0.225 4.89795 0.5 4.89795H6.5Z"
                          fill="#E87616"
                        />
                      </svg>
                      Sign in
                    </div>
                  </Link>
                )}
                {/* Only show Account button when user is authenticated */}
                {(isAuthenticated || localIsAuthenticated) && (
                  <Button 
                    variant="ghost" 
                    className="flex items-center gap-2 text-[#333] font-medium hover:bg-gray-100 focus-visible:ring-0 focus:outline-none border-0 mr-auto p-0"
                  >
                    {/* Circular avatar with user's first letter */}
                    <div 
                      className="w-[24px] h-[24px] rounded-full bg-[#E87616] text-white flex items-center justify-center font-bold text-xs"
                      aria-label={userData?.name || user?.name || 'Account'}
                    >
                      {(userData?.name || user?.name) ? (userData?.name || user?.name).charAt(0).toUpperCase() : 'A'}
                    </div>
                    {userData?.name || user?.name || 'Account'}
                    {/* <ChevronDown size={14} className="text-gray-500" /> */}
                  </Button>
                )}
                <a 
                  href="https://homefoodi-vendor.devsparxit.com/dashboard" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-black cursor-pointer py-2 hover:text-[#E87616] transition-colors"
                >
                  Vendor Signup
                </a>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
};

export default Header;
