/**
 * ServiceSection component for the home page
 * 
 * Updated to add redirection functionality to service-listing page
 * with appropriate vendor type parameter when a service type is clicked
 */

import { useNavigate } from 'react-router-dom';

interface ServiceSectionProps {
  title: string;
  subtitle: string;
  description: string;
  imageSrc: string;
  imageAlt: string;
}

export const ServiceSection = ({
  title,
  subtitle,
  description,
  imageSrc,
  imageAlt,
}: ServiceSectionProps) => {
  const navigate = useNavigate();
  
  // Function to handle redirection based on service type
  const handleServiceClick = () => {
    let vendorType = '';
    let redirectUrl = '/service-listing';
    let activeTab = '';
    
    // Set vendor type based on title
    if (title === 'Home Chefs') {
      vendorType = 'home-chef';
      activeTab = 'Home Chefs';
      // No need to add type parameter for home-chef as it's the default
    } else if (title === 'Tiffin Suppliers') {
      vendorType = 'tiffin_supplier';
      activeTab = 'Tiffin Suppliers';
      redirectUrl = '/service-listing?type=tiffin_supplier';
    } else if (title === 'Caterer') {
      vendorType = 'caterer';
      activeTab = 'Caterer';
      redirectUrl = '/service-listing?type=caterer';
    }
    
    // Store vendor type in localStorage
    localStorage.setItem('vendor_type', vendorType);
    localStorage.setItem('vendor_type_display', title);
    
    // Set the active tab in localStorage so it persists across navigation
    localStorage.setItem('activeNav', activeTab);
    
    // Dispatch custom event to notify about vendor type change
    window.dispatchEvent(new Event('vendorTypeChanged'));
    
    // Navigate to service listing page with appropriate type parameter
    navigate(redirectUrl);
  };
  return (
    <section className="flex gap-10 border bg-[#FBFBFC] mb-10 p-5 rounded-md border-solid border-[#D9D9D9] max-md:flex-col">
      <div className="w-[445px] h-[297px] overflow-hidden rounded-md max-md:w-full">
        <img
          src={imageSrc}
          alt={imageAlt}
          className="w-full h-full object-cover"
        />
      </div>

      <div className="flex-1">
        <h2 className="text-[#E87616] text-[32px] font-bold mb-5">{title}</h2>
        <h3 className="text-[#2C2F24] font-bold mb-2.5">{subtitle}</h3>
        <p className="text-[#2C2F24] mb-[40px]">{description}</p>
        <button 
          className="text-white font-bold cursor-pointer bg-[#E87616] px-5 py-2 rounded-[5px]"
          onClick={handleServiceClick}
        >
          View More
        </button>
      </div>
    </section>
  );
};

export default ServiceSection;
