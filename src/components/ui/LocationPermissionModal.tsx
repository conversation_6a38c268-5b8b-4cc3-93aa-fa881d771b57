/**
 * LocationPermissionModal.tsx
 * Created: 2025-04-22
 * 
 * This component displays a modal dialog when users first visit the site,
 * asking for location permission and explaining why it's needed.
 * It follows the JustDial-style approach to location permissions.
 */

import React, { useState } from "react";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { MapPin, Info } from "lucide-react";
import { requestLocationPermission } from "@/lib/location";

interface LocationPermissionModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onPermissionGranted: () => void;
}

export const LocationPermissionModal: React.FC<LocationPermissionModalProps> = ({
  open,
  onOpenChange,
  onPermissionGranted
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleAllowLocation = async () => {
    setIsLoading(true);
    try {
      await requestLocationPermission();
      onPermissionGranted();
      onOpenChange(false);
    } catch (error) {
      console.error("Error requesting location permission:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDenyLocation = () => {
    // Store user's choice in localStorage to not show the modal again
    localStorage.setItem('homefoodi_location_permission_denied', 'true');
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5 text-[#E87616]" />
            <span>Enable Location Services</span>
          </DialogTitle>
          <DialogDescription>
            Allow HomeFoodi to access your location to find the best home chefs, tiffin services, and caterers near you.
          </DialogDescription>
        </DialogHeader>
        
        <div className="p-4 bg-gray-50 rounded-md my-2">
          <div className="flex items-start gap-2">
            <Info className="h-5 w-5 text-blue-500 mt-0.5" />
            <div className="text-sm text-gray-700">
              <p className="font-medium mb-1">Why we need your location:</p>
              <ul className="list-disc pl-5 space-y-1">
                <li>Find vendors in your area</li>
                <li>Calculate accurate delivery distances</li>
                <li>Show personalized recommendations</li>
                <li>Improve your overall experience</li>
              </ul>
            </div>
          </div>
        </div>
        
        <DialogFooter className="sm:justify-between">
          <Button
            variant="outline"
            onClick={handleDenyLocation}
            className="sm:w-[45%]"
          >
            Not Now
          </Button>
          <Button
            onClick={handleAllowLocation}
            className="bg-[#E87616] hover:bg-[#D06513] sm:w-[45%]"
            disabled={isLoading}
          >
            {isLoading ? "Getting Location..." : "Allow Location"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default LocationPermissionModal;
