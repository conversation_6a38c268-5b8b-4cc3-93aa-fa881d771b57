/**
 * VegTypeFilter Component
 * 
 * This component allows users to filter vendors based on whether they offer veg or non-veg menu items.
 * The filter uses the veg_menu and non_veg_menu arrays from the vendor_other_details table.
 */

import { cn } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface VegTypeFilterProps {
  selectedVegTypes: string[];
  onChange: (vegTypes: string[]) => void;
}

const VEG_TYPES = [
  { value: "veg", label: "Veg" },
  { value: "non-veg", label: "Non-Veg" },
];

const VegTypeFilter = ({ selectedVegTypes, onChange }: VegTypeFilterProps) => {
  const handleVegTypeChange = (value: string) => {
    // If the value is already selected, remove it
    if (selectedVegTypes.includes(value)) {
      onChange(selectedVegTypes.filter((type) => type !== value));
    } else {
      // Otherwise, add it to the selected types
      onChange([...selectedVegTypes, value]);
    }
  };

  return (
    <div className="flex flex-col gap-4">
      {VEG_TYPES.map((type) => (
        <div key={type.value} className="flex items-center space-x-2">
          <Checkbox
            id={`veg-type-${type.value}`}
            checked={selectedVegTypes.includes(type.value)}
            onCheckedChange={() => handleVegTypeChange(type.value)}
            className={cn(
              "h-4 w-4 rounded border",
              type.value === "veg" 
                ? "data-[state=checked]:bg-[#008001] data-[state=checked]:border-[#008001]" 
                : "data-[state=checked]:bg-[#FF0000] data-[state=checked]:border-[#FF0000]"
            )}
          />
          <Label 
            htmlFor={`veg-type-${type.value}`}
            className={cn(
              "text-sm font-medium cursor-pointer",
              type.value === "veg" 
                ? selectedVegTypes.includes(type.value) ? "text-[#008001]" : "text-[#9B9B9B]" 
                : selectedVegTypes.includes(type.value) ? "text-[#FF0000]" : "text-[#9B9B9B]"
            )}
          >
            {type.label}
          </Label>
        </div>
      ))}
    </div>
  );
};

export default VegTypeFilter;
