/**
 * ContentPage Component
 * 
 * This component fetches and displays content pages from the database based on the slug parameter.
 * It supports pages like About Us, Privacy Policy, Terms & Conditions, etc.
 */

import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { fetchContentPageBySlug, ContentPage as ContentPageType } from '../services/contentService';
import { Loader } from '../components/ui/loader';
import ErrorBoundary from '../components/ErrorBoundary';

const ContentPage = () => {
  const { slug } = useParams<{ slug: string }>();
  const [contentPage, setContentPage] = useState<ContentPageType | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchContent = async () => {
      try {
        setLoading(true);
        setError(null);
        
        if (!slug) {
          setError('Page not found');
          setLoading(false);
          return;
        }
        
        const data = await fetchContentPageBySlug(slug);
        
        if (!data) {
          setError('Content not found');
        } else {
          setContentPage(data);
        }
      } catch (err) {
        console.error('Error fetching content page:', err);
        setError('Failed to load content');
      } finally {
        setLoading(false);
      }
    };

    fetchContent();
  }, [slug]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <Loader />
      </div>
    );
  }

  if (error || !contentPage) {
    return (
      <div className="max-w-[1120px] mx-auto py-16 px-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-500 mb-4">
            {error || 'Content not available'}
          </h1>
          <p>The requested page could not be found or is currently unavailable.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-[1120px] mx-auto py-16 px-4">
      <h1 className="text-3xl font-bold mb-8">{contentPage.title}</h1>
      <div 
        className="content-page prose max-w-none"
        dangerouslySetInnerHTML={{ __html: contentPage.content }}
      />
    </div>
  );
};

const ContentPageWithErrorBoundary = () => (
  <ErrorBoundary>
    <ContentPage />
  </ErrorBoundary>
);

export default ContentPageWithErrorBoundary;
