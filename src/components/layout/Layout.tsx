/**
 * Layout Component for Next.js
 * Updated: 2025-03-20
 * Implements browser's native location permission request
 */

'use client';

import { useEffect } from 'react';
import { Header } from "./Header";
import { Footer } from "./Footer";
import { requestLocationPermission } from "@/lib/location";

interface LayoutProps {
  children: React.ReactNode;
}

export const Layout = ({ children }: LayoutProps) => {
  // Request location permission when component mounts (only asks once)
  useEffect(() => {
    // Small delay to avoid blocking initial render
    const timer = setTimeout(() => {
      requestLocationPermission();
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow">{children}</main>
      <Footer />
    </div>
  );
};

export default Layout;
