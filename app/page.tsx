import type { Metadata } from 'next';
import { Layout } from '@/components/layout/Layout';
import { Hero } from '@/components/home/<USER>';
import { ServiceSection } from '@/components/home/<USER>';

export const metadata: Metadata = {
  title: 'Homefoodi - Order Fresh Home Cooked Meals',
  description: 'Order fresh, healthy, and customized meals from verified home chefs, tiffin suppliers, and caterers.',
  openGraph: {
    title: 'Homefoodi - Order Fresh Home Cooked Meals',
    description: 'Order fresh, healthy, and customized meals from verified home chefs, tiffin suppliers, and caterers.',
  },
};

export default function HomePage() {
  return (
    <Layout>
      <Hero />
      <ServiceSection />
    </Layout>
  );
}
