import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { Providers } from './providers';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: {
    default: 'Homefoodi - Order Fresh Home Cooked Meals',
    template: '%s | Homefoodi'
  },
  description: 'Order fresh, healthy, and customized meals from verified home chefs, tiffin suppliers, and caterers.',
  keywords: ['homefoodi', 'food delivery', 'homemade food', 'healthy meals', 'home chef', 'tiffin service', 'catering'],
  authors: [{ name: 'Homefoodi' }],
  creator: 'Homefoodi',
  publisher: 'Homefoodi',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://homefoodi.com'),
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://homefoodi.com',
    siteName: 'Homefoodi',
    title: 'Homefoodi - Order Fresh Home Cooked Meals',
    description: 'Order fresh, healthy, and customized meals from verified home chefs, tiffin suppliers, and caterers.',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Homefoodi - Order Fresh Home Cooked Meals',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Homefoodi - Order Fresh Home Cooked Meals',
    description: 'Order fresh, healthy, and customized meals from verified home chefs, tiffin suppliers, and caterers.',
    images: ['/og-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <link
          href="https://fonts.googleapis.com/css2?family=Lato:wght@400;500;600;700;900&display=swap"
          rel="stylesheet"
        />
        <link 
          rel="stylesheet" 
          href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@latest/dist/tabler-icons.min.css"
        />
      </head>
      <body className={inter.className}>
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
