import React, { useState, useEffect ,useRef } from "react";
import { Helmet } from "react-helmet";
import { useParams } from "react-router-dom";
import { Layout } from "@/components/layout/Layout";
import { MapPin, Star, CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { ActionButtons } from "@/components/ui/action-buttons";
import { RatingProvider, useRating } from "@/contexts/RatingContext";
import MenuTabContent from "@/components/vendor/MenuTabContent";
import ReviewTabContent from "@/components/vendor/ReviewTabContent";
import ServicesTabContent from "@/components/vendor/ServicesTabContent";
import PreferencesTabContent from "@/components/vendor/PreferencesTabContent";
import PaymentTabContent from "@/components/vendor/PaymentTabContent";
import AboutUsTabContent from "@/components/vendor/AboutUsTabContent";
import RatingLoader from "@/components/vendor/RatingLoader";
import { fetchVendorById, BaseVendor } from "@/services/vendorService";
import { calculateDistance } from "@/utils/distanceUtils";

// Helper function to format time from 24-hour format to 12-hour format
const formatTime = (time: string): string => {
  if (!time) return '';
  
  try {
    // Handle different time formats
    let hours, minutes;
    if (time.includes(':')) {
      [hours, minutes] = time.split(':');
    } else {
      hours = time.substring(0, 2);
      minutes = time.substring(2, 4);
    }
    
    hours = parseInt(hours);
    const ampm = hours >= 12 ? 'pm' : 'am';
    hours = hours % 12;
    hours = hours ? hours : 12; // Convert 0 to 12
    
    return `${hours}:${minutes} ${ampm}`;
  } catch (error) {
    console.error('Error formatting time:', error);
    return time; // Return original if parsing fails
  }
};

// Helper function to get service type display name
const getServiceType = (vendorType: string): string => {
  const typeMap: Record<string, string> = {
    'home-chef': 'Home Cooking',
    'home_chef': 'Home Cooking',
    'tiffin': 'Tiffin Service',
    'tiffin_supplier': 'Tiffin Service',
    'tiffin_service': 'Tiffin Service',
    'caterer': 'Catering Service',
    'catering': 'Catering Service',
    'caters': 'Catering Service'
  };
  
  return typeMap[vendorType] || 'Food Service';
};

// Helper function to format operating days
const formatDays = (startDay?: string, endDay?: string): string => {
  if (!startDay && !endDay) return 'Mon - Sun';
  
  // Map of day abbreviations
  const dayMap: Record<string, string> = {
    'monday': 'Mon',
    'tuesday': 'Tue',
    'wednesday': 'Wed',
    'thursday': 'Thu',
    'friday': 'Fri',
    'saturday': 'Sat',
    'sunday': 'Sun'
  };
  
  // Format the days
  let formattedStartDay = startDay ? dayMap[startDay.toLowerCase()] || startDay : 'Mon';
  let formattedEndDay = endDay ? dayMap[endDay.toLowerCase()] || endDay : 'Sun';
  
  // Ensure capitalization for any days not in the map
  if (!Object.values(dayMap).includes(formattedStartDay)) {
    formattedStartDay = formattedStartDay.charAt(0).toUpperCase() + formattedStartDay.slice(1).toLowerCase();
  }
  
  if (!Object.values(dayMap).includes(formattedEndDay)) {
    formattedEndDay = formattedEndDay.charAt(0).toUpperCase() + formattedEndDay.slice(1).toLowerCase();
  }
  
  return `${formattedStartDay} - ${formattedEndDay}`;
};

// Mock vendor data
const mockVendor = {
  id: 1,
  name: "Abha's Kitchen",
  image: "/lovable-uploads/9404f837-a745-4b95-bdf9-e61948617947.png",
  type: "North Indian | Fast Food",
  location: "Sector 16, Noida",
  rating: 4.9,
  distance: 2,
  isVeg: true,
  isMixed: false,
  vegMenuItems: ["Dal", "Paneer", "Roti"],
  nonVegMenuItems: [],
  timings: "Lunch - Dinner",
  phone: "+************",
  whatsapp: "************",
  address:
    "A412 Bhadar Girnar Height 1, Joshipura, Junagadh-362001 (Opposite R S Kalaria School Bahauddin College Road)",
  delivery: "Delivery up to 5 kms",
  operatingHours: "Mon - Sun  (9:00 am - 10:30 pm)",
  coordinates: {
    latitude: 28.6139,
    longitude: 77.209,
  },
  // Add vendorCoordinates for distance calculation
  vendorCoordinates: {
    latitude: 28.6139,
    longitude: 77.209,
  },
  service: "Home Cooking",
  // Include vendor_type for database tracking
  vendor_type: "home_chef",
  // We'll use an empty array for services as they will be fetched dynamically
  service_types: [],
};

// Tabs for the vendor details
const tabs = [
  { id: "menu", label: "Menu" },
  { id: "review", label: "Review" },
  { id: "services", label: "Services" },
  { id: "preferences", label: "Preferences" },
  { id: "payment", label: "Payment" },
  { id: "about", label: "About Us" },
];

// No longer needed as we'll pass props directly to ActionButtons

// Inner component that uses the rating context
const VendorDetailsInner = () => {
  const { id, city, name } = useParams<{ id: string; city: string; name: string }>();
  const [activeTab, setActiveTab] = useState("menu");

  // Fetch vendor details based on ID
  const [vendor, setVendor] = useState<typeof mockVendor | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userCoordinates, setUserCoordinates] = useState<{lat: number, lng: number} | null>(null);
  
  // Use the rating context to get the shared rating
  const { vendorRating } = useRating();

  // Get user coordinates from localStorage
  useEffect(() => {
    try {
      const storedCoordinates = localStorage.getItem('userCoordinates');
      if (storedCoordinates) {
        const coordinates = JSON.parse(storedCoordinates);
        setUserCoordinates(coordinates);
        console.log('User coordinates loaded:', coordinates);
      } else {
        console.log('No user coordinates found in localStorage');
      }
    } catch (error) {
      console.error('Error parsing user coordinates:', error);
    }
  }, []);

  useEffect(() => {
    const fetchVendorData = async () => {
      if (!id) return;
      
      try {
        setLoading(true);
        const vendorData = await fetchVendorById(id);
        
        if (vendorData) {
          // Format vendor data to match the expected structure in the UI
          const formattedVendor = {
            id: parseInt(vendorData.id) || 1, // Convert string ID to number
            name: vendorData.business_name || 'Unnamed Vendor',
            image: vendorData.profile_image || '/lovable-uploads/image (1).png',
            profile_url: vendorData.profile_url || vendorData.profile_image || '/lovable-uploads/image (1).png',
            type: vendorData.cuisineNames && vendorData.cuisineNames.length > 0 
              ? vendorData.cuisineNames.join(', ') 
              : 'No Cuisine',
            location: `${vendorData.city || ''}, ${vendorData.state || ''}`,
            rating: vendorData.rating || 0, // Use 0 as fallback instead of 4.5 to be consistent with listing page
            // We'll calculate the actual distance in the UI based on user coordinates
            distance: vendorData.distance || (vendorData.delivery_radius ? Math.floor(vendorData.delivery_radius) : 5),
            // Store the vendor's coordinates for distance calculation
            vendorCoordinates: {
              latitude: vendorData.coordinates?.latitude || 
                       (vendorData.lattitute ? parseFloat(vendorData.lattitute as string) : 0),
              longitude: vendorData.coordinates?.longitude || 
                        (vendorData.longitute ? parseFloat(vendorData.longitute as string) : 0),
            },
            isVeg: vendorData.isVeg || false,
            isMixed: vendorData.isMixed || false,
            vegMenuItems: Array.isArray(vendorData.vegMenuItems) ? vendorData.vegMenuItems : [],
            nonVegMenuItems: Array.isArray(vendorData.nonVegMenuItems) ? vendorData.nonVegMenuItems : [],
            timings: vendorData.start_time && vendorData.end_time ? 
              `${formatTime(vendorData.start_time)} - ${formatTime(vendorData.end_time)}` : 
              vendorData.timings || 'Hours not specified',
            phone: vendorData.phone_number || '',
            whatsapp: vendorData.whatsapp || vendorData.phone_number || '',
            address: vendorData.address || 'Address not available',
            delivery: vendorData.delivery_radius ? 
              `Delivery up to ${vendorData.delivery_radius} kms` : 
              'Delivery information not available',
            operatingHours: vendorData.start_time && vendorData.end_time ? 
              `${formatDays(vendorData.start_day, vendorData.end_day)} (${formatTime(vendorData.start_time)} - ${formatTime(vendorData.end_time)})` : 
              'Operating hours not specified',
            coordinates: vendorData.coordinates || {
              latitude: 28.6139,
              longitude: 77.209,
            },
            service: getServiceType(vendorData.vendor_type || ''),
            // Include the original vendor_type for database tracking
            vendor_type: vendorData.vendor_type || '',
            // Service types IDs will be used to fetch services in the ServicesTabContent component
            service_types: vendorData.service_types || []
          };
          
          // Save vendor_type to localStorage for interaction tracking
          if (vendorData.vendor_type) {
            localStorage.setItem('vendor_type', vendorData.vendor_type);
          }
          
          // Enhanced debug logging to see what data we're getting
          console.log('Raw vendor data from API:', vendorData);
          console.log('Formatted vendor data:', formattedVendor);
          console.log('Menu items:', {
            vegMenuItems: formattedVendor.vegMenuItems,
            nonVegMenuItems: formattedVendor.nonVegMenuItems,
            isMixed: formattedVendor.isMixed,
            isVeg: formattedVendor.isVeg
          });
          
          // Ensure menu items arrays are properly initialized
          if (!formattedVendor.vegMenuItems || !Array.isArray(formattedVendor.vegMenuItems)) {
            console.log('Initializing vegMenuItems array');
            formattedVendor.vegMenuItems = [];
          }
          
          if (!formattedVendor.nonVegMenuItems || !Array.isArray(formattedVendor.nonVegMenuItems)) {
            console.log('Initializing nonVegMenuItems array');
            formattedVendor.nonVegMenuItems = [];
          }
          
          console.log('Formatted vendor data with menu items:', {
            vegMenuItems: formattedVendor.vegMenuItems,
            nonVegMenuItems: formattedVendor.nonVegMenuItems
          });
          setVendor(formattedVendor);
        }
      } catch (err) {
        console.error('Error fetching vendor details:', err);
        setError('Failed to load vendor details');
      } finally {
        setLoading(false);
      }
    };
    
    fetchVendorData();
  }, [id]);

  // Mock login state - in a real app, this would come from your auth system
  const [isLoggedIn, setIsLoggedIn] = useState(true);

  // Mock function to handle login - in a real app, this would open your auth modal
  const handleLogin = () => {
    setIsLoggedIn(true);
  };


   // 👇 Added useRef to store button references for dynamic tab indicator
   const tabRefs = useRef<(HTMLButtonElement | null)[]>([]);
  
   // 👇 Added state to track the indicator position & width
   const [indicatorStyle, setIndicatorStyle] = useState({ left: "0px", width: "0px" });
 
   // 👇 Updates indicator position dynamically when tab changes
   useEffect(() => {
     const activeIndex = tabs.findIndex((tab) => tab.id === activeTab);
     if (activeIndex !== -1 && tabRefs.current[activeIndex]) {
       const { offsetLeft, offsetWidth } = tabRefs.current[activeIndex]!;
       setIndicatorStyle({ left: `${offsetLeft}px`, width: `${offsetWidth}px` });
     }
   }, [activeTab]);



  // Show loading state if data is not yet loaded
  if (loading) {
    return (
      <Layout>
        <div className="max-w-[1120px] mx-auto mt-[20px] md:mt-[30px] p-4 flex justify-center items-center h-[50vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#E87616]"></div>
        </div>
      </Layout>
    );
  }

  // Show error state if there was an error loading the data
  if (error || !vendor) {
    return (
      <Layout>
        <div className="max-w-[1120px] mx-auto mt-[20px] md:mt-[30px] p-4 text-center">
          <div className="text-red-500 text-xl font-semibold mb-2">Error loading vendor details</div>
          <div className="text-gray-600">{error || 'Vendor data not available'}</div>
        </div>
      </Layout>
    );
  }

  // Debug: Log to see if component is rendering
  console.log('VendorDetails component rendering, vendor:', vendor?.name);

  // Force title update as backup
  useEffect(() => {
    document.title = "Service Detail";
    console.log('VendorDetails: Title set to Service Detail');
  }, []);

  return (
    <>
      <Helmet>
        <title>Service Detail</title>
        <meta name="description" content="Vendor details page" />
      </Helmet>

      <Layout>
        {/* This component loads the rating as soon as the page loads */}
        <RatingLoader vendorId={id} />
        <div className="w-full">
          {/* Vendor Header */}
          <div className="bg-[#F8F8FA] p-3 md:p-5 mb-3 md:mb-5 max-w-[1120px] mx-auto mt-[20px] md:mt-[30px]">
            <div className="max-w-[1120px] mx-auto flex flex-col md:flex-row justify-between flex-wrap">
              <div className="w-full md:w-[50%] mb-4 md:mb-0">
                {/* Vendor Image - Only visible on mobile and tablet (hidden on desktop/laptop) */}
                <div className="block md:hidden mb-4">
                  <img 
                    src={(vendor as any).profile_url || vendor.image || '/lovable-uploads/image (1).png'}
                    alt={`${vendor.name}`} 
                    className="w-full h-[180px] sm:h-[220px] object-cover rounded-md shadow-md"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.onerror = null;
                      target.src = '/lovable-uploads/image (1).png';
                    }}
                    loading="eager"
                    fetchPriority="high"
                  />
                </div>
                <div className="flex flex-col md:flex-row gap-[15px] md:gap-[30px] items-start">
                  <div className="border-b md:border-b-0 md:border-r border-[#9B9B9B] pb-3 md:pb-0 pr-0 md:pr-[20px] w-full md:w-auto">
                    <h1 className="text-xl md:text-2xl font-bold text-[#2C2F24]">
                      {vendor.name}
                    </h1>
                    <p className="text-sm text-[#9B9B9B] max-w-[350px]">{vendor.type}</p>
                  </div>

                  <div className="flex items-center gap-1 mt-0">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="12"
                      height="14"
                      viewBox="0 0 12 14"
                      fill="none"
                    >
                      <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M6 13.6668C8.25 13.6668 12 9.53222 12 6.25942C12 2.98662 9.31371 0.333496 6 0.333496C2.68629 0.333496 0 2.98662 0 6.25942C0 9.53222 3.75 13.6668 6 13.6668ZM6 8.3335C7.10457 8.3335 8 7.43807 8 6.3335C8 5.22893 7.10457 4.3335 6 4.3335C4.89543 4.3335 4 5.22893 4 6.3335C4 7.43807 4.89543 8.3335 6 8.3335Z"
                        fill="#008001"
                      />
                    </svg>
                    {/* <MapPin className="h-4 w-4 text-[#008001]" /> */}
                    <span className="font-bold text-sm">
                      {userCoordinates && vendor.vendorCoordinates && 
                       vendor.vendorCoordinates.latitude && 
                       vendor.vendorCoordinates.longitude ? 
                        calculateDistance(
                          userCoordinates.lat,
                          userCoordinates.lng,
                          vendor.vendorCoordinates.latitude,
                          vendor.vendorCoordinates.longitude
                        ).toFixed(2) : 
                        (vendor.distance ? vendor.distance.toFixed(2) : '0.00')
                      } km
                    </span>
                  </div>
                </div>

                <div className="flex flex-col gap-2 mt-2">
                  <div className="flex items-center gap-2">
                    {/* Veg/Non-Veg Label */}
                    {vendor.isMixed ? (
                      <span className="italic font-bold text-sm">
                        <span className="text-[#008001]">Veg</span>
                        <span className="text-[#000000]"> / </span>
                        <span className="text-[#FF0000]">Non-Veg</span>
                      </span>
                    ) : (
                      <span className={cn(
                        "italic font-bold text-sm",
                        vendor.isVeg ? "text-[#008001]" : "text-[#FF0000]"
                      )
                      }>
                        {vendor.isVeg ? "Veg" : "Non-Veg"}
                      </span>
                    )
                    }
                    <span className="text-[#2C2F24]">|</span>
                    <span className="italic font-bold text-sm text-[#2C2F24]">
                      {vendor.timings}
                    </span>
                  </div>
                  
                  {/* Display menu items below the Veg/Non-Veg label */}
                  <div className="flex flex-col">
                    {/* Debug info - hidden div for debugging */}
                    <div className="hidden">
                      {/* Console log removed to fix TypeScript error */}
                    </div>
                    
                    {/* Veg Menu Items */}
                    {vendor.vegMenuItems && Array.isArray(vendor.vegMenuItems) && vendor.vegMenuItems.length > 0 && (
                      <div className="flex items-center">
                        <span className="text-[#008001] text-xs font-semibold">Veg Items:</span>
                        <span className="italic text-xs text-[#2C2F24] truncate max-w-[250px] ml-1">
                          {vendor.vegMenuItems
                            .filter(item => typeof item === 'string' && item.trim() !== '')
                            .slice(0, 3)
                            .map(item => item.trim())
                            .join(', ')}
                          {vendor.vegMenuItems.length > 3 ? '...' : ''}
                        </span>
                      </div>
                    )}
                    
                    {/* Non-Veg Menu Items */}
                    {vendor.nonVegMenuItems && Array.isArray(vendor.nonVegMenuItems) && vendor.nonVegMenuItems.length > 0 && (
                      <div className="flex items-center">
                        <span className="text-[#FF0000] text-xs font-semibold">Non-Veg Items:</span>
                        <span className="italic text-xs text-[#2C2F24] truncate max-w-[250px] ml-1">
                          {vendor.nonVegMenuItems
                            .filter(item => typeof item === 'string' && item.trim() !== '')
                            .slice(0, 3)
                            .map(item => item.trim())
                            .join(', ')}
                          {vendor.nonVegMenuItems.length > 3 ? '...' : ''}
                        </span>
                      </div>
                    )}
                    
                    {/* Show timings if no menu items available */}
                    {((!vendor.vegMenuItems || !Array.isArray(vendor.vegMenuItems) || vendor.vegMenuItems.length === 0) && 
                     (!vendor.nonVegMenuItems || !Array.isArray(vendor.nonVegMenuItems) || vendor.nonVegMenuItems.length === 0)) && (
                      <span className="italic font-bold text-sm text-[#2C2F24]">
                        {vendor.timings || 'Hours not specified'}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              

              <div className="w-full md:w-[50%] flex items-center">
                <ActionButtons
                  className="flex justify-start md:justify-end w-full"
                  phoneNumber={vendor.phone}
                  whatsappNumber={vendor.whatsapp}
                  coordinates={vendor.coordinates}
                  vendorName={vendor.name}
                  vendorId={id}
                  address={vendor.address}
                />
              </div>
            </div>
          </div>

          {/* Vendor Information */}
          <div className="max-w-[1120px] mx-auto px-3 md:px-0">
            <div className="bg-[#F8F8FA] grid grid-cols-1 gap-3 md:gap-5 p-3 md:p-5 mb-4 md:mb-8">
              <div className="flex gap-3">
                <div className="text-[#E87616]">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="34"
                    height="34"
                    viewBox="0 0 34 34"
                    fill="none"
                  >
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M16.9939 31.1555C21.7734 31.1555 29.7392 22.3727 29.7392 15.4205C29.7392 8.46836 24.0329 2.83252 16.9939 2.83252C9.95482 2.83252 4.24854 8.46836 4.24854 15.4205C4.24854 22.3727 12.2144 31.1555 16.9939 31.1555ZM16.9939 19.8263C19.3402 19.8263 21.2423 17.9242 21.2423 15.5779C21.2423 13.2315 19.3402 11.3294 16.9939 11.3294C14.6475 11.3294 12.7454 13.2315 12.7454 15.5779C12.7454 17.9242 14.6475 19.8263 16.9939 19.8263Z"
                      fill="#008001"
                    />
                  </svg>{" "}
                </div>
                <div>
                  <h3 className="text-[#E87616] font-bold text-base uppercase">
                    Address
                  </h3>
                  <p className="text-[#989CA3] text-base">{vendor.address}</p>
                </div>
              </div>
            </div>

            <div className="flex flex-col md:flex-row gap-3 md:gap-[1%] flex-wrap">
            <div className="bg-[#F8F8FA] grid grid-cols-1 gap-3 md:gap-5 p-3 md:p-5 mb-4 md:mb-8 w-full md:w-[30%]">
              <div className="flex gap-3">
                <div className="text-[#E87616]">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="34"
                    height="34"
                    viewBox="0 0 34 34"
                    fill="none"
                  >
                    <path
                      d="M21.2499 4.25016V17.0002C21.2499 18.5585 19.9749 19.8335 18.4166 19.8335H2.83325V8.50016C2.83325 5.36933 5.36909 2.8335 8.49992 2.8335H19.8333C20.6124 2.8335 21.2499 3.471 21.2499 4.25016Z"
                      fill="#E87616"
                    />
                    <path
                      d="M30.6666 20.3335V24.0835C30.6666 26.159 28.9921 27.8335 26.9166 27.8335H25.9623C25.7195 26.2343 24.3312 25.0002 22.6666 25.0002C21.002 25.0002 19.6137 26.2343 19.3709 27.8335H14.629C14.3862 26.2343 12.9979 25.0002 11.3333 25.0002C9.66863 25.0002 8.28035 26.2343 8.03754 27.8335H7.08325C5.00773 27.8335 3.33325 26.159 3.33325 24.0835V20.3335H18.4166C20.2511 20.3335 21.7499 18.8346 21.7499 17.0002V7.5835H23.8566C24.6935 7.5835 25.4654 8.03677 25.8884 8.76402C25.8886 8.76448 25.8889 8.76495 25.8892 8.76541L27.8821 12.2502H26.9166C25.8613 12.2502 24.9999 13.1115 24.9999 14.1668V18.4168C24.9999 19.4721 25.8613 20.3335 26.9166 20.3335H30.6666Z"
                      stroke="#E87616"
                    />
                    <path
                      d="M11.3333 31.1667C12.8981 31.1667 14.1667 29.8981 14.1667 28.3333C14.1667 26.7685 12.8981 25.5 11.3333 25.5C9.76853 25.5 8.5 26.7685 8.5 28.3333C8.5 29.8981 9.76853 31.1667 11.3333 31.1667Z"
                      fill="#E87616"
                    />
                    <path
                      d="M22.6666 31.1667C24.2314 31.1667 25.4999 29.8981 25.4999 28.3333C25.4999 26.7685 24.2314 25.5 22.6666 25.5C21.1018 25.5 19.8333 26.7685 19.8333 28.3333C19.8333 29.8981 21.1018 31.1667 22.6666 31.1667Z"
                      fill="#E87616"
                    />
                    <path
                      d="M31.1667 17.7509V19.8333H26.9167C26.1375 19.8333 25.5 19.1958 25.5 18.4167V14.1667C25.5 13.3875 26.1375 12.75 26.9167 12.75H28.7442L30.7983 16.3483C31.0392 16.7733 31.1667 17.255 31.1667 17.7509Z"
                      fill="#E87616"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="text-[#E87616] font-bold text-base uppercase">
                    Delivery
                  </h3>
                  <p className="text-[#989CA3] text-base">{vendor.delivery}</p>
                </div>
              </div>
            </div>

            <div className="bg-[#F8F8FA] grid grid-cols-1 gap-3 md:gap-5 p-3 md:p-5 mb-4 md:mb-8 w-full md:w-[69%]">
            <div className="flex gap-3">
                <div className="text-[#E87616]">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="34"
                    height="34"
                    viewBox="0 0 34 34"
                    fill="none"
                  >
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M16.9999 31.1668C24.824 31.1668 31.1666 24.8242 31.1666 17.0002C31.1666 9.17613 24.824 2.8335 16.9999 2.8335C9.17588 2.8335 2.83325 9.17613 2.83325 17.0002C2.83325 24.8242 9.17588 31.1668 16.9999 31.1668ZM18.0624 9.91683C18.0624 9.33003 17.5867 8.85433 16.9999 8.85433C16.4131 8.85433 15.9374 9.33003 15.9374 9.91683V17.0002C15.9374 17.4575 16.2301 17.8635 16.6639 18.0081L20.9139 19.4248C21.4706 19.6104 22.0723 19.3095 22.2579 18.7528C22.4435 18.1961 22.1426 17.5944 21.5859 17.4089L18.0624 16.2344V9.91683Z"
                      fill="#E87616"
                    />
                  </svg>
                </div>
                <div>
                  <h3 className="text-[#E87616] font-bold text-base uppercase">
                    Timings
                  </h3>
                  <p className="text-[#989CA3] text-base">
                    {vendor.operatingHours}
                  </p>
                </div>
              </div>
            </div>
            </div>

            {/* Tabs Navigation */}
            <div className=" relative">
            <div className="flex overflow-x-auto scrollbar-hide space-x-4 md:space-x-8 py-2 md:py-4 mb-[20px] md:mb-[30px]">
              {tabs.map((tab, index) => (
               <button
               key={tab.id}
               ref={(el) => (tabRefs.current[index] = el)}
               className={`relative text-base font-semibold whitespace-nowrap focus-visible:outline-none
                 ${activeTab === tab.id
                   ? "text-[#000] after:content-[''] after:absolute after:left-0 after:top-full after:w-full after:h-[2px] after:bg-black"
                   : "text-[#9B9B9B]"}
               `}
               onClick={() => setActiveTab(tab.id)}
             >
               {tab.label}
             </button>
             
              
              ))}
            </div>

            {/* 👇 Sliding indicator bar (dynamically moves) */}
            {/* <div
              className="absolute bottom-0 h-[2px] bg-[#000] transition-all duration-300"
              style={{ ...indicatorStyle }} 
            /> */}
          </div>

          {/* Tab Content */}
          {/* <div className="max-w-[1120px] mx-auto mt-6">
            {activeTab === "menu" && <MenuTabContent />}
            {activeTab === "review" && <ReviewTabContent />}
            {activeTab === "services" && <ServicesTabContent />}
            {activeTab === "preferences" && <PreferencesTabContent />}
            {activeTab === "payment" && <PaymentTabContent />}
            {activeTab === "about" && <AboutUsTabContent />}
          </div> */}

<div className="max-w-[1120px] mx-auto ">
              {/* Menu Tab Content */}
              {activeTab === "menu" && <MenuTabContent vendorId={id} />}

              {/* Review Tab Content */}
              {activeTab === "review" && <ReviewTabContent vendorId={id} />}

              {/* Services Tab Content */}
              {activeTab === "services" && (
                <ServicesTabContent vendorId={id} />
              )}

              {/* Preferences tab content */}
              {activeTab === "preferences" && <PreferencesTabContent vendorId={id} />}

              {/* Payment tab content */}
              {activeTab === "payment" && <PaymentTabContent />}

              {/* About tab content */}
              {activeTab === "about" && <AboutUsTabContent vendorId={id} />}
            </div>


           
          </div>
           {/* Sticky bottom action bar */}
           <div className="sticky bottom-0 w-full bg-white shadow-[0px_-1px_8px_0px_rgba(0,0,0,0.18)] p-3 md:p-4 z-50 border-t border-gray-200 md:px-5 xl:px-10">
              <div className="max-w-[1120px] mx-auto flex flex-col md:flex-row justify-between items-start md:items-center gap-3 md:gap-0">
                <div className="flex flex-col">
                <h1 className="text-xl md:text-2xl font-bold text-[#2C2F24]">
                      {vendor.name}
                    </h1>
                 <div className="flex gap-1 items-center b">
                   {Number(vendorRating || vendor.rating) === 0 ? (
                     <>
                     <div className="font-bold">No ratings yet</div>
                     <Star className="h-4 w-4 md:h-5 md:w-5 fill-[#FFC529] text-[#FFC529]" />
                     </>
                   ) : (
                     <>
                       <div className="font-bold">{vendorRating || vendor.rating}</div>
                       <Star className="h-4 w-4 md:h-5 md:w-5 fill-[#FFC529] text-[#FFC529]" />
                     </>
                   )}
                 </div>
                </div>
                <div className="flex w-full md:w-auto">
                  <ActionButtons
                    phoneNumber={vendor.phone}
                    whatsappNumber={vendor.whatsapp}
                    coordinates={vendor.coordinates}
                    vendorName={vendor.name}
                    vendorId={id}
                    address={vendor.address}
                    vendorType={vendor.vendor_type}
                    className="w-full md:flex-wrap"
                  />
                </div>
              </div>
            </div>
        </div>
      </Layout>
    </>
  );
};

// Wrapper component that provides the rating context
const VendorDetails = () => {
  return (
    <RatingProvider>
      <VendorDetailsInner />
    </RatingProvider>
  );
};

export default VendorDetails;
