/**
 * Authentication Context Provider
 * Created: 2025-03-20
 * This file provides authentication context and session management for the application
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { getToken, verifyToken } from '../lib/jwt';
import { User } from '../lib/supabase';
import { initializeSession, logout } from '../lib/session';

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  user: null,
  logout: () => {},
});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    // Check if user is authenticated on mount
    const token = getToken();
    if (token) {
      const payload = verifyToken(token);
      if (payload) {
        setIsAuthenticated(true);
        
        // Get user data from localStorage
        const userString = localStorage.getItem('homefoodi_user');
        if (userString) {
          try {
            const userData = JSON.parse(userString) as User;
            setUser(userData);
          } catch (error) {
            console.error('Error parsing user data:', error);
            // Invalid user data, log out
            handleLogout();
          }
        }
      } else {
        // Invalid token, log out
        handleLogout();
      }
    }

    // Initialize session management
    initializeSession();
  }, []);

  const handleLogout = () => {
    logout();
    setIsAuthenticated(false);
    setUser(null);
  };

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        user,
        logout: handleLogout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
