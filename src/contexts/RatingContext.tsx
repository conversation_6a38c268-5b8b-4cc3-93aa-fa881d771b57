/**
 * RatingContext
 * 
 * This context provides a way to share rating data between components
 * without having to pass props through multiple levels of components.
 */

import React, { createContext, useContext, useState, ReactNode } from 'react';

interface RatingContextType {
  vendorRating: number;
  setVendorRating: (rating: number) => void;
}

const RatingContext = createContext<RatingContextType | undefined>(undefined);

export function RatingProvider({ children }: { children: ReactNode }) {
  const [vendorRating, setVendorRating] = useState<number>(0);

  return (
    <RatingContext.Provider value={{ vendorRating, setVendorRating }}>
      {children}
    </RatingContext.Provider>
  );
}

export function useRating() {
  const context = useContext(RatingContext);
  if (context === undefined) {
    throw new Error('useRating must be used within a RatingProvider');
  }
  return context;
}
