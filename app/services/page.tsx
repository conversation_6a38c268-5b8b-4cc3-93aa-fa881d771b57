import type { Metadata } from 'next';
import { Suspense } from 'react';
import ServiceListingClient from './ServiceListingClient';

type Props = {
  searchParams: { [key: string]: string | string[] | undefined };
};

export async function generateMetadata({ searchParams }: Props): Promise<Metadata> {
  const vendorType = searchParams.type as string || 'home-chef';
  const city = searchParams.city as string;
  
  // Map vendor types to user-friendly names
  const vendorTypeMap: { [key: string]: string } = {
    'home-chef': 'Home Chefs',
    'tiffin_supplier': 'Tiffin Suppliers',
    'caters': 'Caterers'
  };

  const displayVendorType = vendorTypeMap[vendorType] || 'Home Chefs';
  
  let title = 'Service Listing';
  let description = `Find the best ${vendorTypeMap[vendorType]?.toLowerCase() || 'food service providers'}`;
  
  if (city) {
    title = `${displayVendorType} in ${city}`;
    description += ` in ${city}`;
  } else {
    title = displayVendorType;
  }
  
  description += '. Order fresh, healthy, and customized meals from verified vendors on Homefoodi.';

  return {
    title,
    description,
    openGraph: {
      title,
      description,
    },
    keywords: [
      'homefoodi',
      'food delivery',
      vendorType,
      displayVendorType.toLowerCase(),
      ...(city ? [city, `food delivery ${city}`] : [])
    ],
  };
}

export default function ServicesPage({ searchParams }: Props) {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ServiceListingClient searchParams={searchParams} />
    </Suspense>
  );
}
