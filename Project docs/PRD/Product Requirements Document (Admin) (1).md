## Product Requirements Document (PRD) for HomeFoodi Project

### 1. Introduction

#### Overview
HomeFoodi is a local search engine providing information about three culinary services. It is a dynamic web-based platform designed to connect food enthusiasts with three types of culinary experts including home chefs, tiffin suppliers, and catering services. By offering a user-friendly interface and comprehensive functionality, HomeFoodi aims to simplify the process of finding and contacting culinary services, making it easy for users to locate a variety of home-cooked meals, tiffin services, and catering services at their convenience.

#### User Roles and Terminologies
- **Vendors** - Culinary service providers that is Home Chefs, Tiffin Service, and Caterers.
- **Users** - Who will reach out/search/Get in touch with the Vendors.
- **Admin Panel** - The administrator who will manage the platform.

#### Objectives and Key Goals
The primary objectives of the HomeFoodi website are:
- **User Engagement:** To create an engaging and seamless experience for users seeking culinary services.
- **Service Diversity:** To cater to a wide range of culinary preferences and needs by offering services from home chefs, tiffin suppliers, and caterers.
- **Ease of Access:** To provide a straightforward platform where users can easily browse, filter, and interact with service listings.
- **Vendor Support:** To empower vendors with tools for managing their services and get leads for their business.
- **Reliability and Performance:** To ensure the website is reliable and performs well across all user and admin functions.

### 2. Product Scope

#### Key Features

---
# Module: **Auth Module** #
---
### **Functional Requirements**

1. **Login Functionality:**
   - Admin can log in to the admin panel using their registered email and password.
   - The system should verify the credentials against a secure database.
   - Successful login redirects the admin to the admin dashboard.
   - Admin shall update their password as well after Login.

2. **Forgot Password Functionality:**
   - Admin can reset their password via the "Forgot Password" option.
   - Steps for password reset:
     - Admin enters their registered email address.
     - System sends a password reset link to the provided email.
     - Admin clicks on the link and is redirected to a secure password reset page.
     - Admin sets a new password and confirms it.
   - System validates the new password against security policies.

3. **Session Management:**
   - After login, the session remains active for 30 minutes of inactivity.
   - Admin is automatically logged out after 30 minutes of inactivity for security reasons.

4. **Subdomain Hosting:**
   - The admin panel will be hosted on a dedicated subdomain (e.g., admin.homefoodi.com).

5. **Logout Functionality:**
   - Admin can log out at any time using a "Logout" button available on every page of the admin panel.

### **Non-Functional Requirements**

1. **Performance:**
   - Login and password reset actions should complete fast.

2. **Security:**
   - Passwords must be encrypted using industry-standard encryption algorithms (e.g., bcrypt).
   - Secure HTTPS communication must be enforced.
   - Failed login attempts should be capped at 5, after which the account will be temporarily locked for 15 minutes. Upon unlocking the admin will be forced to reset the password (Forgot Password Functionality (Feature) will come into action)

3. **Compatibility:**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18)

### **Validations**

1. **Login:**
   - Email format must be validated.
   - Password must match the stored credentials.

2. **Forgot Password:**
   - Email must be registered in the system.
   - New password must meet the following criteria:
     - At least 8 characters.
     - Contains at least one uppercase letter, one lowercase letter, one number, and one special character.

3. **Session Management:**
   - Inactive sessions are automatically logged out after 30 minutes.

### **Error Messages**

1. **Login Errors:**
   - "Invalid email or password. Please try again."
   - "Your account has been temporarily locked due to multiple failed login attempts. Please try again after 15 minutes."

2. **General Errors:**
   - "An unexpected error occurred. Please refresh the page and try again."

---
# Module: **Tabs Management** #
---

### **Functional Requirements**

1. **CRUD Operations for Vendor Tabs:**
   - There will be 5 tabs:
        - To manage Cuisines
        - To manage Menu
        - To manage Service's Type
        - To manage Preferences
        - To manage the Payment Accept
   - Admin can perform the following actions on each vendor tab item:
     - **Create**:
       - Add new items to the tab (Cuisine, Menu, Services, Preferences, Payment Method).
     - **Read**:
       - View the existing list of items in each tab.
     - **Update**:
       - Modify the details of existing items.
     - **Delete**:
       - Remove items from the tabs.

2. **Immediate Reflection:**
   - Any changes (Create, Update, Delete) made by the admin will be immediately reflected on the vendor's side.
   - Vendors will see added items and can choose to include them in their profile.
   - Deleted items will be removed from all vendor profiles where they were listed.

3. **Tab Items Management:**
   - **Cuisine Tab:**
     - Manage a list of cuisines available for vendors.
     - Changes affect all vendor types (Home Chef, Tiffin Supplier, Caterer).
   - **Menu Tab (Different for all the culinary services):**
     - Manage menus specific to each vendor type:
       - Home Chef
       - Tiffin Supplier
       - Caterer
   - **Services Tab (Different for all the culinary services):**
     - Manage services specific to each vendor type:
       - Home Chef
       - Tiffin Supplier
       - Caterer
   - **Preferences Tab (Same for all the culinary services):**
     - Manage dietary and other preferences (e.g., Vegan, Gluten-Free). 
   - **Payment Method Tab (Same for all the culinary services):**
     - Manage accepted payment methods (e.g., Cash, UPI, Card, Wallet, Net Banking). 
     - Admin can add a logo/icon and the name of the method.

4. **One-by-One CRUD Process:**
   - Admin will perform CRUD actions one item at a time within each tab.
   - Bulk operations will not be allowed to maintain accuracy and control.

5. **Access Control:**
   - Only authorized admin users can access and manage vendor tab items.

6. **Audit Trail:**
   - Every CRUD operation will be logged with the following details:
     - Admin user performing the action.
     - Tab and item affected.
     - Action performed (Create, Read, Update, Delete).
     - Timestamp.

### **Non-Functional Requirements**

1. **Performance:**
   - CRUD operations should reflect changes across the system immediately.

2. **Scalability:**
   - The system must support multiple vendors and their profiles without performance degradation.

3. **Security:**
   - All data changes must be transmitted securely using HTTPS.
   - Only authenticated and authorized admins can access this feature.

4. **Reliability:**
   - The system must ensure high availability and prevent accidental data loss during CRUD operations.

### **Validations**

1. **Create Operation:**
   - Ensure that the new item is unique within its tab.
   - Names should not include special characters. Only "-","'" are allowed.

2. **Update Operation:**
   - Prevent duplicate names or items within the same tab.
   - Validate all updated fields before saving changes.

3. **Delete Operation:**
   - Ensure confirmation from the admin before deletion (e.g., "Are you sure you want to delete this item?").
   - Check for dependencies (e.g., if a menu item is in use by vendors, display a warning).

4. **General:**
   - 50 characters for item names are allowed only.
   - Prevent empty fields from being saved.

5. **Payment Acceptance Management:**
   - Accept png/jpg of max limit 5 mb.
   - Text must not be more than 32 characters. And special characters are not allowed.

### **Error Messages**

1. **Create Errors:**
   - "Item name already exists in this tab. Please use a unique name."
   - "Failed to create a new item. Please try again later."

2. **Update Errors:**
   - "Item name conflicts with an existing item. Please use a unique name."
   - "Failed to save changes. Please try again later."

3. **Delete Errors:**
   - "This item cannot be deleted as it is currently in use by vendors."
   - "Failed to delete the item. Please try again later."

4. **General Errors:**
   - "An unexpected error occurred. Please refresh the page and try again."

---
# Module: **User Management** #
---
### **Functional Requirements**

#### **1. Sub-Menus under User Management**

##### **1.1 User Management**
- Admin shall be able to:
  - View the entire list of users.
- The user list shall display the following details:
  - **User Name**
  - **User Phone Number**
  - **Joining Date & Time**
  - **City** (This will be fetched when the users will allow the location access)
  - **State** (This will be fetched when the users will allow the location access)
  - **Last Login Date & Time**
  - Option to view individual user details.
- **View Individual User Details:**
  - Admin shall be able to view the following information for each user:
    - **Name**
    - **Phone Number**
    - Overall **Counter of WhatsApp, Phone, Directions, and Enquiry** 
    - **Last Login Date & Time**
    - All **Last Actions Performed** with vendors with details like 
       - Vendor Name
       - Activity Timestamp
       - Vendor Type
       - Vendor Phone
       - Vendor Profile URL
       - Interaction Type (Phone, WhatsApp, Enquiry, and Directions)

##### **1.2 Vendor Management**
- Admin shall be able to:
  - View the entire list of vendors.
- The vendor list shall display the following details:
  - **Business Name**
  - **Registered Phone Number**
  - **Vendor Type** (Tiffin Supplier, Home Chef, or Caterer)
  - **Joining Date**
  - **Subscription Status** (Active, Expired)
  - **Profile Completion** (Pending, Completed)
  - **City** & **State**
  - Option to view individual vendor details.
- **View Individual Vendor Details:**
  - Admin shall be able to view the following information for each vendor:
  - Counter of Actions (Call, WhatsApp, Enquiry, and Directions)
  - **Profile Section**
    - **Business Name**
    - **Phone Number**
    - **SMS Number**
    - **WhatsApp Number**
    - **Address**
    - **Uploaded Images**
    - **About Us Section**
    - **Cuisines**
    - **Menu**
    - **Payment Methods**
    - **Services**
    - **Preferences**
- **Subscription Details**
    - **Paid Advance?** (Yes/No)
    - **SUbscription Plan** (1-month, 3-month, and 6-month)
    - **Subscription Start Date**
    - **Subscription End Date**
  - Admin shall have the option to edit the **Profile** section for vendors.
  - Admin shall view the list of actions done by users for the selected vendor. It includes:
    - User Name
    - User Phone Number
    - Action (Call, WhatsApp, Enquiry, Directions)
    - Date and Time of Action

### **Non-Functional Requirements**

1. **Performance:**
   - User and vendor lists must load immediately.
   - View actions must respond fast with the button click.

2. **Security:**
   - All sensitive user and vendor data must be encrypted during transmission and securely stored.
   - Admin actions (view, edit) must be logged for audit purposes.

3. **UI/UX:**
   - The interface must be intuitive, with clear distinctions between user and vendor management.
   - Ensure pagination for large data sets.

### **Validations**

1. **User Details:**
   - Ensure phone numbers are valid (10-digit format).
   - Joining date must be a valid past or present date.

2. **Vendor Details:**
   - Ensure all required fields (e.g., business name, phone number, subscription status) are populated before displaying details.
   - Validate subscription dates to prevent overlaps or inconsistencies.

3. **Edit Vendor Profile Section:**

 a. **Profile Image:**
   - Must be in 400x150 ratio.
   - File size not exceeding 5 MB.
   - Only single image is allowed. (For Caterer, this can 10 images in total)

 b. **Business Name:**
   - Max length: 64 characters.
   - No special characters allowed except -, ", and full stop.
   - This can be alphanumeric.

 c. **Contact Details:**
   - Phone Number, WhatsApp Number, and SMS Number must be 10 digits. +91 must be auto-applied.
   - Postal Code must be 6 digits.
   - All address fields must be filled before proceeding.

 d. **Cuisine/Menu/Service Selection:**
   - At least one option must be selected.

 e. **Payment Methods:**
   - At least one payment method must be selected.

### **Error Messages**

1. **Loading Errors:**
   - "Failed to load user/vendor data. Please refresh and try again."

2. **Invalid Data:**
   - "Invalid phone number. Please ensure the number is 10 digits."
   - "Invalid subscription date. Please check and try again."

3. **Save Action for Vendor About Us:**
   - "Failed to save changes. Please try again."

4. **General Errors:**
   - "An unexpected error occurred. Please contact support."
 
#Search & Filter for User Management (It'll be different for different roles ie. User and Vendor)

### **Functional Requirements**

1. **Search Functionality:**
   - Admin can search for users using the following fields:
     - **User Name** (partial or full name).
     - **Phone Number** (exact match required).
     
           
   - Admin can search for vendors using the following fields:
     - **Vendor Name** (partial or full name).
     - **Vendor Phone Number** (exact match required).

2. **Filter Functionality:**
   - Admin can filter the user list using the following criteria: (Count of users and actions (Call, Directions, Enquiry, and WhatsApp) after applying the filter must be shown - This must be dynamic)
     - **Joining Date:** (For Users Only)
     - **City** (Dropdowns)
     - **State** (Dropdowns)
       - Filter users based on date ranges (e.g., today, last 7 days, this month, custom date range)
       
     - **Vendor Filter**  (Count of vendors after applying the filter must be shown - This must be dynamic)
    - Admin can filter the vendor list using the following criteria:
     - **City** (Dropdowns)
     - **State** (Dropdowns)
     - **Vendor Type:** (Home Chef, Tiffin Supplier, Caterer). (For Vendors - Dropdown)
     - **Subscription Status:** (Dropdown)
       - Active.
       - Expired.
       - No Subscription.

3. **Real-Time Results:**
   - The user list shall update in real time based on the applied search and filter criteria.

4. **Pagination:**
   - Filtered and searched results shall support pagination with a default of 25 users per page.

### **Non-Functional Requirements**

1. **Performance:**
   - Search and filter results shall be displayed immediately.

2. **Scalability:**
   - The system must support multiple users without performance degradation.

3. **Security:**
   - Search and filter functionalities must only be accessible to authorized admin users.
   - Data exported through the system must be encrypted during download.

4. **Responsiveness:**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18)

### **Validations**

1. **Search Input Validation:**
   - User Name: Minimum of 2 characters.
   - Phone Number: Must be a valid 10-digit number.

2. **Filter Validation:**
   - Joining Date filter require valid date ranges.
   - Custom Date Ranges: Start date must not exceed the end date. And today's date.

### **Error Messages**

1. **Search Errors:**
   - "No users found matching the search criteria."
   - "Invalid phone number. Please enter a valid 10-digit number."

2. **Filter Errors:**
   - "No users found for the selected filters."
   - "Invalid date range. Please ensure the start date is earlier than the end date."

---
# Module: **Subscription Management** #
---

### **Functional Requirements**

1. **Subscription Plan Management:**
   - Admin can create, edit, or delete subscription plans.
   - Admin can manage subscription plan details, including:
     - Plan Name.
     - Plan Time Period (Enter value between 1 to 12 months).
     - Amount (base price before GST).
     - GST Amount (Auto-calculated by the program).
     - Total Amount (auto-calculated as Amount + GST).
     - Save button and Cancel button.

2. **Vendor Subscription Management:**
   - Admin can view all subscribed vendors along with the following details:
     - Business Name.
     - Phone Number.
     - Vendor Type
     - Plan Name.
     - Subscription Status (Active/Expired).
     - Advance Payment Status (Yes/No).
     - Subscription Start Date.
     - Subscription End Date.

3. **First-Time Vendor Extension Offer:**
   - Vendors who purchase any subscription plan for the first time will receive one extra month added to the plan duration.

4. **Real-Time Updates:**
   - Any changes to subscription plans or vendor subscriptions will reflect immediately across the platform.

### **Non-Functional Requirements**

1. **Performance:**
   - The subscription management interface must load and display vendor details quickly.
   - Any updates made to subscription plans or vendor data should process fast.

2. **Scalability:**
   - Support concurrent vendor subscriptions.

3. **Security:**
   - Admin access should be protected by role-based authentication.
   - Subscription details and vendor data must be securely stored and encrypted during transmission.

4. **Compatibility:**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18)

### **Validations**

1. **Subscription Plan Details:**
   - Plan Time Period must be an integer between 1 and 12 months.
   - Amount must be a positive numerical value.
   - Plan name must be Alphanumeric having 62 characters with special characters allowed like "-,fullstop)
   - GST Amount must be a positive numerical value and calculated automatically.
   - Total Amount must equal the sum of Amount and GST Amount.

2. **Vendor Subscription Details:**
   - Business Name and Phone Number must be non-empty.
   - Subscription Start Date must precede the End Date.

3. **One-Month Extension Offer:**
   - Only apply the extension for vendors who are subscribing for the first time.

### **Error Messages**

1. **Subscription Plan Management:**
   - "Plan Name cannot be empty."
   - "Plan Time Period must be between 1 and 12 months."
   - "Amount and GST Amount must be positive numbers."
   - "Total Amount calculation error. Please recheck inputs."

2. **Vendor Subscription Management:**
   - "Subscription Start Date must be before the End Date."
   - "Failed to update subscription details. Please try again."

3. **General:**
   - "An unexpected error occurred. Please contact support if the issue persists."

### **User Journey Summary**

1. **Subscription Plan Management:**
   - Admin accesses the "Subscription Management" section from the admin panel.
   - Admin creates, updates, or deletes subscription plans with all required details.
   - Any changes made are immediately reflected in the vendor portal.

2. **Vendor Subscription View:**
   - Admin navigates to the list of subscribed vendors.
   - Admin views vendor subscription details.

3. **First-Time Vendor Offer:**
   - Admin ensures that the system applies the additional one-month extension for first-time vendors automatically.

---
# Module: **Transaction Management** #
---

### **Functional Requirements**

1. **View Transactions:**
   - Admin shall be able to view all transactions performed on the platform in a structured list view.
   - Each transaction shall display the following details:
     - Business Name
     - Complete Address
     - Phone Number
     - Vendor Type (Home Chef, Tiffin Supplier, Caterer)
     - Plan Name (e.g., 1-month, 3-months, 6-months)
     - Total Amount (Plan Amount + GST)
     - Transaction ID
     - Payment Method (Card, UPI, Netbanking)
     - Status (Success, Failed, Pending)
     - Transaction Date

2. **Filter and Search Transactions:**
   - Admin shall be able to filter transactions based on the following:
     - Vendor Type
     - Transaction Status (Success, Failed, Pending)
     - Date Range (using a calendar selector)
   - Admin shall be able to search transactions using:
     - Business Name
     - Phone Number
     - Transaction IDculinary services

3. **Invoice Management:**
   - Admin shall be able to download invoices for individual transactions.
   - The invoice shall include:
     - HomeFoodi Logo and Address
     - Vendor details (Business Name, Address, Phone Number, Vendor Type)
     - Transaction details (Plan Name, Amount, Calculated GST, Total Amount, Payment Method, Transaction ID, Date)
   - Invoices shall be generated dynamically and downloadable as PDFs.

4. **Pagination:**
   - Admin shall have access to paginated transaction lists with a default view of 25 transactions per page.
   
5. **Export Transaction Data:**
   - Admin shall have the ability to export transaction data as a CSV file.
   - Export can be performed:
     - **With Filters:** Only transactions matching the applied filters will be included.
     - **Without Filters:** All transaction data will be included in the export.

3. **CSV File Format:**
   - The exported CSV file shall include the following fields:
     - Business Name.
     - Phone Number.
     - Vendor Address.
     - Vendor Type (Home Chef, Tiffin Supplier, Caterer).
     - Plan Name (e.g., 1-month, 3-months, 6-months).
     - Total Amount (Plan Amount + Calculated GST).
     - Transaction ID.
     - Payment Method (Card, UPI, Netbanking).
     - Status (Success, Failed, Pending).
     - Transaction Date.

5. **Real-Time Processing:**
   - The system shall process and generate the CSV file in real time, ensuring minimal delay.

### **Non-Functional Requirements**

1. **Performance:**
   - Transaction data shall load quickly.
   - Invoice generation and download shall occur immediately.

2. **Scalability:**
   - The system shall support multiple concurrent transactions without performance degradation.

3. **Security:**
   - All transaction data shall be encrypted during transmission and storage.
   - Admin access shall be secured with role-based access control (RBAC).

4. **Compatibility:**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18)

### **Validations**

1. **Transaction Data:**
   - Business Name and Phone Number fields must not be empty.
   - Total Amount must be positive numeric values.
   - Valid Transaction IDs must be alphanumeric and unique.
   - Payment Method must be one of the predefined values (Card, UPI, Netbanking).
   - Status must be one of the predefined values (Success, Failed, Pending).

2. **Date Filters:**
   - Start Date must be earlier than or equal to End Date.
   - Date Range cannot exceed 1 year from the current date.
   - Future Dates must not be shown.

3. **Invoice Generation:**
   - Ensure all mandatory fields are populated before generating an invoice.

### **Error Messages**

1. **Data Loading Errors:**
   - "Unable to load transactions. Please refresh the page or try again later."

2. **Invoice Download Errors:**
   - "Failed to generate the invoice. Please try again."

3. **Filter Errors:**
   - "Invalid date range. Please ensure the start date is earlier than the end date."
   - "No transactions found for the selected filters."
   
4. **Export Errors:**
   - "No transactions found for the selected filters."
   - "Failed to export CSV. Please try again later."
   
### **User Journey Summary**

1. **Access Transaction Export Module:**
   - Admin navigates to the Transaction Management section.
   - Selects the "Export as CSV" option.

2. **Apply Filters (Optional):**
   - Admin applies filters for Vendor Type, Transaction Status, or Date Range.
   - Preview of filtered data is displayed on the screen.

3. **Export Data:**
   - Admin initiates the export.
   - CSV file is generated and downloaded in real time.

4. **Without Filters:**
   - Admin skips the filters and exports all transaction data directly.

### **Edge Cases**

1. **Empty Transactions List:**
   - Display a message: "No transactions available" when there are no transactions to display.

2. **Failed Transactions:**
   - Highlight failed transactions in a distinct color button (red) for easier identification.

---
# Module: **Dashboard** #
---

### **Functional Requirements**

1. **Dashboard Overview:**
   - The admin dashboard provides a summary of key platform metrics.

2. **Dashboard Metrics:**
   - **Total Number of Users:**
     - Displays the combined count of all users, including both vendors and general users.
   - **Total Vendors:**
     - Displays the total number of registered vendors on the platform.
   - **Active Subscribers:**
     - Displays the count of vendors with an active subscription.
   - **Profiles Under Completion:**
     - Displays the number of vendor profiles that are incomplete (e.g., missing mandatory details like Business Name, About Us, or Menu).

3. **Real-Time Updates:**
   - All dashboard metrics shall update in real-time to reflect the most current data.

4. **Navigation:**
   - Clicking on any metric shall navigate the admin to a detailed view:
     - **Total Users:** Redirects to the User Management page.
     - **Total Vendors:** Redirects to the Vendor Management page.
     - **Active Subscribers:** Redirects to the subscription page with filter of active subscribers applied.
     - **Profiles Under Completion:** Redirects to the vendor management page with filter applied of profile under completion (Pending Profile).

### **Non-Functional Requirements**

1. **Performance:**
   - Dashboard metrics should load immediately.

2. **Scalability:**
   - The system should handle multiple users and vendors without performance degradation.

3. **Security:**
   - Dashboard data must only be accessible to authenticated and authorized admin users.

4. **Responsiveness:**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18)

### **Validations**

1. **Total Users Metric:**
   - Includes all users (vendors and general users).
   - Exclude inactive users from the count.

2. **Total Vendors Metric:**
   - Only counts vendors who have completed registration.

3. **Active Subscribers Metric:**
   - Includes vendors with an active subscription.
   - Excludes expired or pending subscriptions.

4. **Profiles Under Completion Metric:**
   - Counts vendor profiles missing mandatory fields or sections (e.g., Business Name, Menu, Services). Or vendors who registered and left off at any of the profile setup page.

### **Error Messages**

1. **Loading Errors:**
   - "Failed to load dashboard data. Please refresh the page."

2. **Navigation Errors:**
   - "Unable to navigate to detailed view. Please try again."

---
# Module: **CMS** #
---

### **Functional Requirements**

1. **Page Management:**
   - Admin shall be able to manage the content of the following pages:
     - **About:**
       - Update the "About Us" content, including text only.
     - **Contact:**
       - View a list of users/vendors who have contacted the admin via the "Contact Us" page.
       - Details visible include:
         - Name.
         - Email Address.
         - Phone Number.
         - Message.
         - Date and Time of the contact form submission.
     - **Terms & Conditions:**
       - Update the text content of the Terms & Conditions page.
     - **Privacy Policy:**
       - Update the text content of the Privacy Policy page.
     - **Feedback:**
       - Admin shall be able to update the email address where feedback submissions are sent.

2. **Real-Time Updates:**
   - Any changes made by the admin to the content of these pages shall reflect immediately on the platform.

3. **Rich Text Editor:** (TinyMCE or WYSIWYG (editing library) will be finalized during the development)
   - Admin shall have access to a rich text editor to format the content for:
     - About Us.
     - Terms & Conditions.
     - Privacy Policy.
   - The editor shall support the items like (we'll use the default editing items provided by the selected library.): 
     - Bold, Italics, Underline.
     - Bulleted and Numbered Lists.
     - Hyperlinks.
     - Text Alignment.

### **Non-Functional Requirements**

1. **Performance:**
   - CMS content updates should reflect immediately after saving.
   - Contact form submission data should load quickly.

2. **Scalability:**
   - The system must handle multiple contact form submissions without performance degradation.

3. **Security:**
   - Only authorized admin users shall have access to CMS functionalities.
   - Contact form submissions must be encrypted during transmission and securely stored.

4. **Compatibility:**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18)

### **Validations**

1. **About, Terms & Conditions, and Privacy Policy Content:**
   - Text fields should not be empty.
   - Ensure no unsupported HTML tags are used in the rich text editor.

2. **Contact Form Submissions:**
   - All required fields (Name, Email, Phone, Message) must be populated.
   - Email addresses must follow valid formatting rules.

3. **Feedback Email Update:**
   - Ensure the email address entered is valid.

### **Error Messages**

1. **Content Update Errors:**
   - "Failed to save content updates. Please try again."

2. **Contact Submissions Load Errors:**
   - "Unable to load contact form submissions. Please refresh the page."

3. **Feedback Email Update Errors:**
   - "Invalid email address. Please enter a valid email."

### **User Journey Summary**

1. **Page Content Update:**
   - Admin accesses the CMS section from the admin panel.
   - Selects a page (About, Terms & Conditions, Privacy Policy) and updates the content using the rich text editor.
   - Saves the changes, which are immediately reflected on the platform.

2. **Contact Form Management:**
   - Admin views the list of users/vendors who submitted the contact form.

3. **Feedback Email Update:**
   - Admin navigates to the Feedback section in the CMS.
   - Updates the email address where feedback is directed.
   - Saves the change, ensuring future feedback emails are sent to the updated address.
   
---
# Module: **Review Management** #
---

### **Functional Requirements**

1. **Review Flag Management:**
   - Admin shall be able to view all flagged reviews submitted by vendors.
   - Each flagged review shall display the following details:
     - **Vendor Name**
     - **Vendor Type** (Home Chef, Tiffin Supplier, Caterer)
     - **Vendor Phone Number**
     - **Vendor Profile URL** (Clickable link to the vendor's public profile)
     - **Vendor Message** (Reason for flagging the review)

2. **Admin Actions:**
   - Admin can take the following actions on flagged reviews:
     - **View** (Opens detailed review information)
     - **Delete** (Removes the review and rating from the vendor’s profile)
     - **Reject** (Keeps the review and notifies the vendor with a reason)
   
3. **View Review Details:**
   - Upon clicking the **View** button, the admin shall be able to see:
     - **User Name** who reviewed the vendor
     - **User Phone Number**
     - **Review Content**
     - **Action Options:**
       - **Delete** (Removes the review permanently)
       - **Reject** (Retains the review and requires an admin message for rejection explanation)

4. **Pagination:**
   - The flagged review list shall support pagination with a default of 25 records per page.

5. **Real-Time Counter:**
   - Display the total number of flagged reviews.
   - Display the count of pending flagged reviews (reviews awaiting admin action). Upon deletion/rejection, the flag will be marked as closed.

6. **Immediate Action Reflection:**
   - If a review is deleted, it shall be immediately removed from the vendor's profile.
   - If a review is rejected, the vendor shall receive an in-platform notification along with the admin’s rejection message.

### **Non-Functional Requirements**

1. **Performance:**
   - The flagged review list must load fast.
   - Review deletion or rejection must process instantly.

2. **Scalability:**
   - The system must support up to multiple reviews across all vendors.

3. **Security:**
   - Only authorized admin users shall have access to review management.
   - Sensitive data (e.g., reviewer phone numbers) must be encrypted during transmission.

### **Validations**

1. **Review Actions:**
   - Ensure that only flagged reviews can be processed by the admin.
   - If rejecting a review, the rejection message must be at least 10 characters long and maximum 100 characters.

2. **Pagination Handling:**
   - Prevent page navigation if no more records are available.

3. **Vendor Flagging Data:**
   - Ensure vendor-provided messages do not exceed 300 characters.

### **Error Messages**

1. **Data Loading Errors:**
   - "Failed to load flagged reviews. Please refresh the page."

2. **Review Actions Errors:**
   - "Unable to process the action. Please try again."
   - "Rejection message must be at least 10 characters long."

3. **General Errors:**
   - "An unexpected error occurred. Please contact support."

### **User Journey Summary**

1. **Admin Accesses Review Management:**
   - Navigates to the "Review Management" module.
   - Views a list of flagged reviews with relevant vendor details.

2. **Admin Reviews Flagged Content:**
   - Clicks "View" to check reviewer details and review content.
   - Decides to **Delete** or **Reject** the review.

3. **Admin Takes Action:**
   - If **Deleted**, the review and rating are immediately removed from the vendor profile.
   - If **Rejected**, the review remains, and the vendor receives a notification with the admin’s rejection message.

4. **Dashboard Updates:**

- The pending flagged review counter updates in real time after an action is taken.

Note: All the specified non-functional requirements and time-sensitive features are subject to third-party and server response dependencies.
---
End of the document
---








