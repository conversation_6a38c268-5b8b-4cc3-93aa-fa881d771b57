/**
 * ServiceListing.tsx
 * 
 * This component displays a list of vendors based on the vendor type (home-chef, tiffin_supplier, or caters).
 * It uses the useVendors hook to fetch vendor data dynamically from Supabase.
 * The component includes filtering, pagination, and loading states.
 * 
 * Updated to handle tiffin_supplier parameter in URL and localStorage.
 * Fixed to properly refresh data when switching between vendor types.
 * Updated to properly reset filter components when Clear All button is clicked.
 */

import React, { useState, useEffect } from "react";
import { Layout } from "@/components/layout/Layout";
import { Button } from "@/components/ui/button";
import { Filter, Star, MapPin, ChevronDown } from "lucide-react";
import { ActionButtons } from "@/components/ui/action-buttons";
import FilterDialog, { FilterState, FilterCategory } from "@/components/filters/FilterDialog";
import { cn } from "@/lib/utils";
import { Link, useSearchParams, useNavigate, useParams } from "react-router-dom";
import { getCurrentVendorType, getCurrentVendorTypeDisplay } from "@/utils/vendorTypeUtils";
import { useVendors } from "@/hooks/useVendors";
import { BaseVendor } from "@/services/vendorService";
import { PageMeta } from "@/components/seo/PageMeta";
import {
  generateServiceListingTitle,
  generateServiceListingDescription,
  generateKeywords,
  generateCanonicalUrl
} from "@/utils/metaUtils";
import { calculateDistance } from "@/utils/distanceUtils";

/**
 * We no longer use static fallback data.
 * If no vendors are found with valid coordinates, we'll show a "No data found" message instead.
 */

// Filter buttons component
interface FilterButtonProps {
  label: string;
  onClick: () => void;
  isActive?: boolean;
}

const FilterButton: React.FC<FilterButtonProps> = ({ label, onClick, isActive = false }) => {
  return (
    <Button 
      variant="outline" 
      className="bg-[#E6E6E6] text-[#2C2F24] hover:bg-[#D9D9D9] border-0 font-normal rounded-md h-auto relative"
      onClick={onClick}
    >
      {isActive && (
        <span className="absolute top-1 right-1 h-2 w-2 rounded-full bg-[#E87616]"></span>
      )}
      <span className="mr-1">{label}</span>
      <ChevronDown className="h-4 w-4" />
    </Button>
  );
};

const ServiceListing: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [currentFilter, setCurrentFilter] = useState<FilterCategory | undefined>(undefined);
  const {city} = useParams();
  console.log("city",city)
  
  // Function to navigate to vendor details page
  const handleVendorClick = (vendorId: string) => {
    navigate(`/vendor/${vendorId}`);
  };
  const [appliedFilters, setAppliedFilters] = useState<FilterState>({
    vegType: [],
    cuisines: [],
    services: [],
    rating: "",
    price: "",
    distance: 1, // Default value for UI display, but not applied initially
    changedFilters: new Set<string>() // Initialize with empty set to prevent distance filter from being applied initially
  });
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10; // Number of vendors to show per page
  
  // Vendor type from URL parameter or localStorage
  const vendorTypeFromUrl = searchParams.get('type');
  const [vendorType, setVendorType] = useState<string>(
    vendorTypeFromUrl || getCurrentVendorType()
  );
  
  // Update localStorage when vendor type changes from URL
  useEffect(() => {
    // Log the current URL parameters for debugging
    console.log('URL parameters:', { vendorTypeFromUrl, searchParams: Object.fromEntries(searchParams.entries()) });
    
    if (vendorTypeFromUrl) {
      // Always update localStorage when URL changes to ensure consistency
      console.log('Updating vendor_type in localStorage from URL:', vendorTypeFromUrl);
      localStorage.setItem('vendor_type', vendorTypeFromUrl);
      setVendorType(vendorTypeFromUrl);
      
      // Reset pagination to page 1 when vendor type changes
      setCurrentPage(1);
      
      // Reset filters when vendor type changes
      setAppliedFilters({
        vegType: [],
        cuisines: [],
        services: [],
        rating: "",
        price: "",
        distance: 1,
        changedFilters: new Set<string>()
      });
      
      // Update the vendor type display name based on the vendor type
      let vendorTypeDisplay = 'Home Chefs';
      if (vendorTypeFromUrl === 'tiffin_supplier') {
        vendorTypeDisplay = 'Tiffin Suppliers';
      } else if (vendorTypeFromUrl === 'caterer') {
        vendorTypeDisplay = 'Caterer';
      }
      localStorage.setItem('vendor_type_display', vendorTypeDisplay);
      
      // Force a window event to trigger data refresh
      window.dispatchEvent(new Event('vendorTypeChanged'));
    } else {
      // If no type in URL, default to home-chef
      const defaultType = 'home-chef';
      if (localStorage.getItem('vendor_type') !== defaultType) {
        console.log('Setting default vendor_type in localStorage:', defaultType);
        localStorage.setItem('vendor_type', defaultType);
        localStorage.setItem('vendor_type_display', 'Home Chefs');
        setVendorType(defaultType);
        
        // Reset filters when vendor type changes
        setAppliedFilters({
          vegType: [],
          cuisines: [],
          services: [],
          rating: "",
          price: "",
          distance: 1,
          changedFilters: new Set<string>()
        });
        
        // Force a window event to trigger data refresh
        window.dispatchEvent(new Event('vendorTypeChanged'));
      }
    }
  }, [vendorTypeFromUrl, searchParams]);
  
  // Check and use user coordinates for location-based filtering
  useEffect(() => {
    // Try to get user coordinates from localStorage
    const checkUserCoordinates = () => {
      // Check if we have coordinates in userCoordinates object
      try {
        const storedCoordinates = localStorage.getItem('userCoordinates');
        if (storedCoordinates) {
          const userCoordinates = JSON.parse(storedCoordinates);
          if (userCoordinates && userCoordinates.lat && userCoordinates.lng) {
            // Store coordinates in the expected format for the vendor service
            localStorage.setItem('homefoodi_user_latitude', userCoordinates.lat.toString());
            localStorage.setItem('homefoodi_user_longitude', userCoordinates.lng.toString());
            console.log('Set user coordinates from userCoordinates:', userCoordinates);
            return true;
          }
        }
      } catch (error) {
        console.error('Error parsing userCoordinates:', error);
      }
      
      // Check if we have coordinates in nearbyLocations format (from screenshot)
      try {
        const nearbyLocations = localStorage.getItem('nearbyLocations');
        if (nearbyLocations) {
          const locationData = JSON.parse(nearbyLocations);
          if (locationData && locationData.lat && locationData.lng) {
            // Store coordinates in the expected format for the vendor service
            localStorage.setItem('homefoodi_user_latitude', locationData.lat.toString());
            localStorage.setItem('homefoodi_user_longitude', locationData.lng.toString());
            console.log('Set user coordinates from nearbyLocations:', locationData);
            return true;
          }
        }
      } catch (error) {
        console.error('Error parsing nearbyLocations:', error);
      }
      
      return false;
    };
    
    // Check if we already have the coordinates in the expected format
    const hasExistingCoordinates = () => {
      const lat = localStorage.getItem('homefoodi_user_latitude');
      const lng = localStorage.getItem('homefoodi_user_longitude');
      return !!(lat && lng);
    };
    
    // If we don't have coordinates in either format, try to get them from geolocation API
    const getGeolocation = () => {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const { latitude, longitude } = position.coords;
            localStorage.setItem('homefoodi_user_latitude', latitude.toString());
            localStorage.setItem('homefoodi_user_longitude', longitude.toString());
            
            // Also store in userCoordinates format for consistency
            localStorage.setItem('userCoordinates', JSON.stringify({ lat: latitude, lng: longitude }));
            
            console.log('Set user coordinates from geolocation:', { latitude, longitude });
            
            // Refetch vendors with the new coordinates
            if (appliedFilters.changedFilters?.has('distance')) {
              const filters: Record<string, any> = {};
              filters.distance = appliedFilters.distance;
              refetch(filters);
            }
          },
          (error) => {
            console.error('Error getting geolocation:', error);
          }
        );
      }
    };
    
    // Check for coordinates in order of preference
    if (!hasExistingCoordinates()) {
      if (!checkUserCoordinates()) {
        getGeolocation();
      }
    }
  }, []);
  

  // Use the useVendors hook to fetch vendor data
  const {
    vendors: fetchedVendors,
    loading,
    error,
    refetch,
    currentVendorType,
    currentVendorTypeDisplay: vendorTypeDisplay,
    totalVendors
  } = useVendors({
    initialFilters: {},
    autoFetch: true,
    location: city
  });
  
  // Reset pagination and filters when vendor type changes
  useEffect(() => {
    console.log('Vendor type changed to:', currentVendorType, 'resetting pagination to page 1');
    setCurrentPage(1);
    
    // Reset all filters when vendor type changes
    setAppliedFilters({
      vegType: [],
      cuisines: [],
      services: [],
      rating: "",
      price: "",
      distance: 1,
      changedFilters: new Set<string>()
    });
  }, [currentVendorType]);

  // Set page title aggressively
  useEffect(() => {
    const setTitle = () => {
      document.title = "abc";
      console.log('ServiceListing: Setting title to: abc');
    };

    // Set immediately
    setTitle();

    // Set with multiple delays to override any other title changes
    const timers = [
      setTimeout(setTitle, 50),
      setTimeout(setTitle, 100),
      setTimeout(setTitle, 200),
      setTimeout(setTitle, 500),
      setTimeout(setTitle, 1000)
    ];

    // Also set on any title change
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.target === document.head) {
          const titleElement = document.querySelector('title');
          if (titleElement && titleElement.textContent !== 'abc') {
            console.log('Title changed to:', titleElement.textContent, 'changing back to abc');
            setTitle();
          }
        }
      });
    });

    observer.observe(document.head, { childList: true, subtree: true });

    return () => {
      timers.forEach(clearTimeout);
      observer.disconnect();
    };
  }, []);

  // Filter out vendors without valid coordinates
  const filteredVendors = fetchedVendors.filter(vendor => 
    vendor.coordinates && 
    vendor.coordinates.latitude && 
    vendor.coordinates.longitude && 
    !isNaN(vendor.coordinates.latitude) && 
    !isNaN(vendor.coordinates.longitude)
  );
  
  // Use filtered vendors or empty array if loading - no more static fallback data
  const vendors = loading ? [] : filteredVendors;
  
  // Mock login state - in a real app, this would come from your auth system
  const [isLoggedIn, setIsLoggedIn] = useState(true);

  const handleFilterClick = (filterType: FilterCategory) => {
    setCurrentFilter(filterType);
    setIsFilterOpen(true);
  };

  const handleApplyFilters = (filters: FilterState) => {
    setAppliedFilters(filters);
    setIsFilterOpen(false);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    const changedFilters = appliedFilters.changedFilters || new Set<string>();
    
    // Only count filters that have been explicitly changed by the user
    if (changedFilters.has('vegType') && appliedFilters.vegType.length > 0) count++;
    if (changedFilters.has('cuisines') && appliedFilters.cuisines.length > 0) count++;
    if (changedFilters.has('services') && appliedFilters.services.length > 0) count++;
    if (changedFilters.has('rating') && appliedFilters.rating && appliedFilters.rating !== 'any') count++;
    if (changedFilters.has('price') && appliedFilters.price && appliedFilters.price !== '') count++;
    if (changedFilters.has('distance')) count++;
    
    return count;
  };

  // Mock function to handle login - in a real app, this would open your auth modal
  const handleLogin = () => {
    // This would typically open a login modal
    setIsLoggedIn(true);
  };

  // Apply filters when they change
  useEffect(() => {
    const filters: Record<string, any> = {};
    const changedFilters = appliedFilters.changedFilters || new Set<string>();
    
    // Only add filters that have been explicitly changed by the user
    if (changedFilters.has('vegType') && appliedFilters.vegType.length > 0) {
      filters.veg_type = appliedFilters.vegType.join(',');
    }
    
    if (changedFilters.has('cuisines') && appliedFilters.cuisines.length > 0) {
      filters.cuisines = appliedFilters.cuisines.join(',');
    }
    
    if (changedFilters.has('services') && appliedFilters.services.length > 0) {
      filters.services = appliedFilters.services.join(',');
    }
    
    // Only apply rating filter if it's been explicitly changed by the user
    if (changedFilters.has('rating') && appliedFilters.rating && appliedFilters.rating !== 'any') {
      // Use 'rating' instead of 'min_rating' to match what the vendor service expects
      filters.rating = appliedFilters.rating;
      // Add a sort parameter to prioritize rating-based sorting
      filters.sort_by = 'rating';
      console.log('Applying rating filter:', appliedFilters.rating, 'with sort by rating');
    }
    
    // Only apply price filter if it's been explicitly changed by the user
    if (changedFilters.has('price') && appliedFilters.price && appliedFilters.price !== '') {
      console.log('Applying price filter:', appliedFilters.price);
      // Use 'price' instead of 'price_sort' to match what the vendor service expects
      filters.price = appliedFilters.price;
    }
    
    // Only apply distance filter if it's been explicitly changed by the user
    // This ensures the distance filter is not applied on initial load
    if (changedFilters.has('distance') && changedFilters.size > 0) {
      console.log('Applying distance filter:', appliedFilters.distance);
      filters.distance = appliedFilters.distance;
    } else {
      console.log('Distance filter not applied initially');
    }
    
    console.log('Applied filters:', filters);
    console.log('Changed filters:', Array.from(changedFilters));
    
    // Refetch vendors with the new filters
    refetch(filters);
    
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(appliedFilters)]);
  
  // Update vendor type when it changes in URL or localStorage
  useEffect(() => {
    if (vendorTypeFromUrl && vendorTypeFromUrl !== currentVendorType) {
      localStorage.setItem('vendor_type', vendorTypeFromUrl);
      setVendorType(vendorTypeFromUrl);
      // Reset pagination to page 1 when vendor type changes
      setCurrentPage(1);
      // The useVendors hook will automatically refetch when localStorage changes
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [vendorTypeFromUrl]);

  // Calculate paginated data
  const currentVendors = vendors.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Calculate total pages based on actual vendor count
  const totalPages = Math.ceil((fetchedVendors.length > 0 ? fetchedVendors.length : vendors.length) / itemsPerPage);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Scroll to top of results
    window.scrollTo({
      top: document.getElementById('results-heading')?.offsetTop || 0,
      behavior: 'smooth'
    });
    
    // Always load data when changing pages to ensure we have complete data
    loadMoreData();
  };
  
  // Function to load more data with all necessary data including cuisines
  const loadMoreData = () => {
    // Include all necessary parameters for complete vendor data
    const baseParams: Record<string, any> = {
      // Don't set loadMore flag to true, so we get complete data including cuisines
      // This ensures we get the same data as the initial load
    };
    
    // Get user coordinates if available
    const getUserCoordinates = () => {
      // Try different localStorage keys for coordinates
      try {
        // Check for homefoodi_user_latitude and homefoodi_user_longitude
        const lat = localStorage.getItem('homefoodi_user_latitude');
        const lng = localStorage.getItem('homefoodi_user_longitude');
        
        if (lat && lng) {
          return { lat: parseFloat(lat), lng: parseFloat(lng) };
        }
        
        // Check for userCoordinates
        const userCoords = localStorage.getItem('userCoordinates');
        if (userCoords) {
          const coords = JSON.parse(userCoords);
          if (coords && coords.lat && coords.lng) {
            return { lat: coords.lat, lng: coords.lng };
          }
        }
        
        // Check for nearbyLocations
        const nearbyLocations = localStorage.getItem('nearbyLocations');
        if (nearbyLocations) {
          const locations = JSON.parse(nearbyLocations);
          if (locations && locations.lat && locations.lng) {
            return { lat: locations.lat, lng: locations.lng };
          }
        }
      } catch (error) {
        console.error('Error getting user coordinates:', error);
      }
      
      return null;
    };
    
    // Add coordinates to params if available
    const coordinates = getUserCoordinates();
    if (coordinates) {
      baseParams.userLatitude = coordinates.lat;
      baseParams.userLongitude = coordinates.lng;
    }
    
    // Add any applied filters to ensure consistent data
    if (appliedFilters) {
      // Only add filters that have been explicitly changed
      const changedFilters = appliedFilters.changedFilters || new Set<string>();
      
      if (changedFilters.has('vegType') && appliedFilters.vegType.length > 0) {
        baseParams.vegType = appliedFilters.vegType;
      }
      
      if (changedFilters.has('cuisines') && appliedFilters.cuisines.length > 0) {
        baseParams.cuisines = appliedFilters.cuisines;
      }
      
      if (changedFilters.has('services') && appliedFilters.services.length > 0) {
        baseParams.services = appliedFilters.services;
      }
      
      if (changedFilters.has('rating') && appliedFilters.rating) {
        baseParams.rating = appliedFilters.rating;
      }
      
      if (changedFilters.has('price') && appliedFilters.price) {
        baseParams.price = appliedFilters.price;
      }
      
      if (changedFilters.has('distance') && appliedFilters.distance) {
        baseParams.distance = appliedFilters.distance;
      }
    }
    
    console.log('Loading more data with complete parameters:', baseParams);
    refetch(baseParams);
  };

  console.log("currentVendors",currentVendors)

  // Generate dynamic meta data
  const metaVendorType = vendorTypeFromUrl || getCurrentVendorType();
  const userCity = city || localStorage.getItem("homefoodi_user_city") || '';
  const userState = localStorage.getItem("homefoodi_user_state") || '';

  const pageTitle = "abc";
  const pageDescription = generateServiceListingDescription(metaVendorType, userCity, userState);
  const pageKeywords = generateKeywords(metaVendorType, userCity);
  const canonicalUrl = generateCanonicalUrl(window.location.pathname + window.location.search);



  return (
    <>
      {/* <PageMeta
        title={pageTitle}
        description={pageDescription}
        keywords={pageKeywords}
        canonical={canonicalUrl}
        ogTitle={pageTitle}
        ogDescription={pageDescription}
      /> */}

      <Layout>
        <div className="w-full mb-8">
          {/* Filter Bar */}
          <div className="max-w-[1120px] mx-auto mb-5 pb-5 flex gap-5 flex-wrap pt-[69px] px-4 md:px-5 xl:px-0">
            <Button
              variant="outline"
              className="p-0 bg-transparent hover:bg-transparent bg-[#E6E6E6] text-[#2C2F24] hover:bg-[#D9D9D9] border-0 font-normal rounded-md px-7 py-1 h-auto relative"
              onClick={() => setIsFilterOpen(true)}
            >
              {getActiveFiltersCount() > 0 && (
                <div className="absolute -top-2 -right-2 bg-[#E87616] text-white text-xs font-bold rounded-full min-w-[18px] h-[18px] flex items-center justify-center p-[2px] border border-white">
                  {getActiveFiltersCount()}
                </div>
              )}

              <svg width="30" height="30" viewBox="0 0 44 38" fill="none" xmlns="http://www.w3.org/2000/svg">
                <mask id="mask0_135_7753" style={{maskType: "alpha"}} maskUnits="userSpaceOnUse" x="0" y="25" width="42" height="13">
                  <path fillRule="evenodd" clipRule="evenodd" d="M20.8741 31.4228C20.8741 34.97 17.9985 37.8456 14.4513 37.8456C11.4586 37.8456 8.94386 35.7987 8.23086 33.0285H1.6057C0.718898 33.0285 0 32.3096 0 31.4228C0 30.536 0.718898 29.8171 1.6057 29.8171H8.23086C8.94386 27.0469 11.4586 25 14.4513 25C17.9985 25 20.8741 27.8756 20.8741 31.4228ZM27.2969 29.8171C26.4101 29.8171 25.6912 30.536 25.6912 31.4228C25.6912 32.3096 26.4101 33.0285 27.2969 33.0285H40.1426C41.0294 33.0285 41.7483 32.3096 41.7483 31.4228C41.7483 30.536 41.0294 29.8171 40.1426 29.8171H27.2969Z" fill="#E87616"/>
                </mask>
                <g mask="url(#mask0_135_7753)">
                  <rect x="-2" y="18" width="44" height="26" fill="#E87616"/>
                </g>
                <mask id="mask1_135_7753" style={{maskType: "alpha"}} maskUnits="userSpaceOnUse" x="0" y="0" width="44" height="26">
                  <rect width="44" height="26" fill="#008001"/>
                </mask>
                <g mask="url(#mask1_135_7753)">
                  <path fillRule="evenodd" clipRule="evenodd" d="M28.2969 19.8456C31.2897 19.8456 33.8044 17.7987 34.5174 15.0285H41.1426C42.0294 15.0285 42.7483 14.3096 42.7483 13.4228C42.7483 12.536 42.0294 11.8171 41.1426 11.8171H34.5174C33.8044 9.04691 31.2897 7 28.2969 7C24.7497 7 21.8741 9.87559 21.8741 13.4228C21.8741 16.97 24.7497 19.8456 28.2969 19.8456ZM2.6057 11.8171C1.7189 11.8171 1 12.536 1 13.4228C1 14.3096 1.7189 15.0285 2.6057 15.0285H15.4513C16.3381 15.0285 17.057 14.3096 17.057 13.4228C17.057 12.536 16.3381 11.8171 15.4513 11.8171H2.6057Z" fill="#008001"/>
                </g>
              </svg>

              {/* <Filter className="h-6 w-6" /> */}
            </Button>
            <div className="flex gap-2 overflow-x-auto pb-[10px]">
              <FilterButton 
                label="Veg | Non-Veg" 
                onClick={() => handleFilterClick("Veg | Non-Veg")} 
                isActive={appliedFilters.vegType.length > 0} 
              />
              <FilterButton 
                label="Cuisines" 
                onClick={() => handleFilterClick("Cuisines")} 
                isActive={appliedFilters.cuisines.length > 0} 
              />
              <FilterButton 
                label="Services" 
                onClick={() => handleFilterClick("Services")} 
                isActive={appliedFilters.services.length > 0} 
              />
              <FilterButton 
                label="Rating" 
                onClick={() => handleFilterClick("Rating")} 
                isActive={appliedFilters.changedFilters?.has('rating') && !!appliedFilters.rating && appliedFilters.rating !== 'any'} 
              />
              <FilterButton 
                label="Price" 
                onClick={() => handleFilterClick("Price")} 
                isActive={appliedFilters.changedFilters?.has('price') && !!appliedFilters.price && appliedFilters.price !== ''} 
              />
              <FilterButton 
                label="By Distance" 
                onClick={() => handleFilterClick("By Distance")} 
                isActive={appliedFilters.changedFilters?.has('distance')} 
              />
              {getActiveFiltersCount() > 0 && (
                <Button
                  variant="outline"
                  className="p-0 bg-transparent hover:bg-transparent  text-[#2C2F24] hover:text-[red] border-0 font-normal rounded-md px-7 py-1 h-auto underline"
                  onClick={() => {
                    setAppliedFilters({
                      vegType: [],
                      cuisines: [],
                      services: [],
                      rating: "",
                      price: "",
                      distance: 1,
                      changedFilters: new Set<string>(), // Reset changed filters tracking
                    });
                  }}
                >
                  Clear all
                </Button>
              )}
            </div>
          </div>

          <div className="max-w-[1120px] mx-auto px-4 md:px-5 xl:px-0">
            <h1 id="results-heading" className="text-xl md:text-2xl font-bold text-[#2C2F24] mb-4 md:mb-8">
              {loading ? (
                <span>Loading {vendorTypeDisplay}...</span>
              ) : error ? (
                <span>Error loading vendors</span>
              ) : vendors.length > 0 ? (
                <span>
                  Showing  {(currentPage - 1) * itemsPerPage + 1}-{Math.min(currentPage * itemsPerPage, vendors.length)} &nbsp;of                  {totalVendors || vendors.length} {vendorTypeDisplay.replace('Home Chefs', 'Home Chef').replace('Tiffin Suppliers', 'Tiffin Supplier')} {city ? ` in ${city}` : localStorage.getItem("homefoodi_user_city") ? ` in ${localStorage.getItem("homefoodi_user_city")}` : ""}
                </span>
              ) : (
                <span>No {vendorTypeDisplay} found</span>
              )}
            </h1>
            

            {loading ? (
              <div className="flex justify-center items-center p-8">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#E87616]"></div>
              </div>
            ) : error ? (
              <div className="text-red-500 p-4 text-center">
                Error loading vendors. Please try again.
              </div>
            ) : vendors.length === 0 ? (
              <div className="text-center p-8 flex flex-col items-center">
                <div className="text-xl font-semibold mb-2">No {vendorTypeDisplay} found</div>
                <div className="text-gray-600 mb-4">
                  {appliedFilters.distance !== 1 ? 
                    `No vendors available within ${appliedFilters.distance}km of your location.` : 
                    "No vendors found matching your search criteria."}
                </div>
                {getActiveFiltersCount() > 0 && (
                  <Button
                    variant="outline"
                    className="bg-[#E87616] text-white hover:bg-[#d06813] border-0 font-normal rounded-md px-7 py-2 h-auto"
                    onClick={() => {
                      setAppliedFilters({
                        vegType: [],
                        cuisines: [],
                        services: [],
                        rating: "",
                        price: "",
                        distance: 1,
                        changedFilters: new Set<string>(), // Reset changed filters tracking
                      });
                    }}
                  >
                    Clear Filters
                  </Button>
                )}
              </div>
            ) : (
              <div className="flex flex-col gap-4 md:gap-6">
                {currentVendors.map((vendor) => (
                <Link 
                  to={`/service/${vendor.city}/${vendor?.business_name}/${vendor.id}`}
                  key={vendor.id} 
                  className="rounded-md p-4 md:p-6 flex flex-col md:flex-row gap-4 md:gap-8 overflow-hidden cursor-pointer hover:bg-gray-50 transition-all"
                >
                  <div className="w-full md:w-[374px]">
                    <img 
                      src={vendor.profile_url || '/lovable-uploads/image (1).png'} 
                      alt={vendor.business_name || 'Vendor'}
                      className="w-full h-[180px] md:h-[238px] object-cover rounded-[15px]"
                    />
                  </div>

                  <div className="flex-1 flex flex-col">
                    <h2 className="text-xl md:text-2xl font-bold text-[#2C2F24] mb-2 hover:text-[#E87616] transition-colors">{vendor.business_name || 'Unnamed Vendor'}</h2>
                    {/* Vendor details section */}
                    {/* <div className="flex flex-col gap-1 mb-2">
                      <p className="text-sm text-[#9B9B9B]">
                        {vendor.cuisineNames && vendor.cuisineNames.length > 0 
                          ? vendor.cuisineNames.join(', ') 
                          : 'No Cuisine'}
                      </p>
                      <p className="text-sm text-[#9B9B9B]">{vendor.address || 'No address available'}</p>
                    </div> */}
                  
                    <div className="flex flex-col flex-grow">
                      <div className="flex flex-col sm:flex-row mb-4 flex-wrap">
                        <div className="pr-[30px] pb-3 sm:pb-0 md:pb-3 sm:border-r sm:border-[#9B9B9B80] md:border-[transparent] lg:border-[#9B9B9B80] sm:w-[50%] md:w-[100%] lg:w-[50%]" >
                        <div className="flex flex-col gap-1 mb-2">
                      <p className="text-sm text-[#9B9B9B]">
                        {vendor.cuisineNames && vendor.cuisineNames.length > 0 
                          ? vendor.cuisineNames.join(', ') 
                          : 'No Cuisine'}
                      </p>
                      <p className="text-sm text-[black] font-[800] ">
                        {vendor.address ? (
                          (() => {
                            const addressParts = vendor.address.split(',');
                            return addressParts.slice(0, 3).join(', ');
                          })()
                        ) : 'No address available'}
                      </p>
                    </div>
                          <div className="flex items-center gap-1 font-bold">
                            {vendor.rating === 0 ? (
                              <>
                              <span>No ratings yet</span>
                              <Star className="h-4 w-4 fill-[#FFC529] text-[#FFC529]" />
                              </>

                            ) : (
                              <>
                                <span>{vendor.rating}</span>
                                <Star className="h-4 w-4 fill-[#FFC529] text-[#FFC529]" />
                              </>
                            )}
                          </div>
                        </div>

                        {/* ------------------------ */}
                        
                        <div className="pl-0 sm:pl-[30px] md:pl-[0] lg:pl-[30px] pt-3 sm:pt-0 sm:w-[50%] md:w-[100%] lg:w-[50%]">
                          <div className="flex items-center gap-1 font-bold text-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                              <path fillRule="evenodd" clipRule="evenodd" d="M8 14.6668C10.25 14.6668 14 10.5322 14 7.25942C14 3.98662 11.3137 1.3335 8 1.3335C4.68629 1.3335 2 3.98662 2 7.25942C2 10.5322 5.75 14.6668 8 14.6668ZM8 9.3335C9.10457 9.3335 10 8.43807 10 7.3335C10 6.22893 9.10457 5.3335 8 5.3335C6.89543 5.3335 6 6.22893 6 7.3335C6 8.43807 6.89543 9.3335 8 9.3335Z" fill="#008001"/>
                            </svg>
                            <span>
                              {(() => {
                                // Debug logging
                                console.log(`Vendor in UI: ${vendor.business_name}`, vendor);
                                console.log(`Delivery radius in UI for ${vendor.business_name}:`, vendor.delivery_radius);
                                
                                // Always show the exact calculated distance for all vendors
                                if (vendor.distance !== undefined && vendor.distance !== null) {
                                  // Display the distance with exactly 2 decimal places
                                  return `${vendor.distance.toFixed(2)}km`;
                                } else if (vendor.distanceUnavailable) {
                                  // If distance calculation is unavailable due to missing coordinates
                                  return 'Distance unavailable';
                                } else {
                                  // Fallback if distance is not available
                                  return 'Distance unavailable';
                                }
                              })()}
                            </span>
                          </div>
                        
                          <div className="flex items-center gap-2">
                            {vendor.isMixed ? (
                              <span className="italic font-bold text-sm flex-none">
                                <span className="text-[#008001]">Veg</span>
                                <span className="text-[#000000]"> / </span>
                                <span className="text-[#FF0000]">Non-Veg</span>
                              </span>
                            ) : (
                              <span className={cn(
                                "italic font-bold text-sm",
                                vendor.isVeg ? "text-[#008001]" : "text-[#FF0000]"
                              )}>
                                {vendor.isVeg ? "Veg" : "Non-Veg"}
                              </span>
                            )}
                            <span className={cn(
                              vendor.isMixed ? "text-[#FF0000]" :
                              vendor.isVeg ? "text-[#008001]" : "text-[#FF0000]"
                            )}>|</span>
                            {/* Display menu items in the same format as VendorDetails page */}
                            <div className="flex flex-col ml-1">
                              {/* Veg Menu Items */}
                              {vendor.vegMenuItems && vendor.vegMenuItems.length > 0 && (
                                <div className="flex items-center flex-wrap">
                                  <span className="text-[#008001] text-xs font-semibold mr-1" style={{whiteSpace: 'nowrap'}}>Veg Items:</span>
                                  <span className="italic text-xs text-[#2C2F24] truncate max-w-[150px] " style={{paddingRight: '10px'}}>
                                    {vendor.vegMenuItems
                                      .filter(item => typeof item === 'string' && item.trim() !== '')
                                      .slice(0, 2)
                                      .map(item => item.trim())
                                      .join(', ')}
                                    {vendor.vegMenuItems.length > 2 ? '...' : ''}
                                  </span>
                                </div>
                              )}
                              
                              {/* Non-Veg Menu Items */}
                              {vendor.nonVegMenuItems && vendor.nonVegMenuItems.length > 0 && (
                                <div className="flex items-center flex-wrap">
                                  <span className="text-[#FF0000] text-xs font-semibold mr-1" style={{whiteSpace: 'nowrap'}}>Non-Veg Items:</span>
                                  <span className="italic text-xs text-[#2C2F24] truncate max-w-[150px]" style={{paddingRight: '10px'}}>
                                    {vendor.nonVegMenuItems
                                      .filter(item => typeof item === 'string' && item.trim() !== '')
                                      .slice(0, 2)
                                      .map(item => item.trim())
                                      .join(', ')}
                                    {vendor.nonVegMenuItems.length > 2 ? '...' : ''}
                                  </span>
                                </div>
                              )}
                              
                              {/* Show timings if no menu items available */}
                              {(!vendor.vegMenuItems || vendor.vegMenuItems.length === 0) && 
                               (!vendor.nonVegMenuItems || vendor.nonVegMenuItems.length === 0) && (
                                <span className="italic font-bold text-sm text-[#2C2F24]">
                                  {vendor.timings}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="mt-auto w-full">
                        {isLoggedIn ? (
                          <ActionButtons 
                            phoneNumber={vendor.phone_number}
                            whatsappNumber={vendor.whatsapp_number || vendor.phone_number}
                            smsNumber={vendor.sms_number || vendor.phone_number} // Using dedicated sms_number for enquiry if available
                            coordinates={vendor.coordinates || { latitude: 28.6139, longitude: 77.2090 }}
                            vendorName={vendor.business_name || 'Vendor'}
                            address={vendor.address}
                            vendorId={vendor.id}
                          />
                        ) : (
                          <Button
                            className="w-full md:w-auto bg-[#E87616] hover:bg-[#D56A14] text-white"
                            onClick={handleLogin}
                          >
                            Login to Contact
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
              </div>
            )}

            {/* Pagination Controls */}
            {!loading && !error && totalPages > 1 && (
              <div className="flex justify-center mt-6 md:mt-8 mb-4 px-2">
                <div className="flex items-center gap-1 md:gap-2 flex-wrap justify-center">
                  <Button
                    variant="outline"
                    className="h-8 w-8 sm:h-10 sm:w-10 p-0 rounded-md text-sm sm:text-base flex items-center justify-center"
                    onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    aria-label="Previous page"
                  >
                    &lt;
                  </Button>
                  
                  {/* Dynamic pagination that shows fewer buttons on mobile */}
                  {(() => {
                    // For small screens or many pages, show limited page buttons
                    const maxVisiblePages = window.innerWidth < 640 ? 3 : 5;
                    
                    if (totalPages <= maxVisiblePages) {
                      // If we have fewer pages than our limit, show all pages
                      return Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                        <Button
                          key={page}
                          variant={currentPage === page ? "default" : "outline"}
                          className={cn(
                            "h-8 w-8 sm:h-10 sm:w-10 p-0 rounded-md text-sm sm:text-base flex items-center justify-center",
                            currentPage === page ? "bg-[#E87616] hover:bg-[#E87616]" : ""
                          )}
                          onClick={() => handlePageChange(page)}
                        >
                          {page}
                        </Button>
                      ));
                    } else {
                      // For many pages, show a subset with ellipsis
                      const pages = [];
                      
                      // Always show first page
                      pages.push(
                        <Button
                          key={1}
                          variant={currentPage === 1 ? "default" : "outline"}
                          className={cn(
                            "h-8 w-8 sm:h-10 sm:w-10 p-0 rounded-md text-sm sm:text-base flex items-center justify-center",
                            currentPage === 1 ? "bg-[#E87616] hover:bg-[#E87616]" : ""
                          )}
                          onClick={() => handlePageChange(1)}
                        >
                          1
                        </Button>
                      );
                      
                      // Calculate range of pages to show around current page
                      let startPage = Math.max(2, currentPage - 1);
                      let endPage = Math.min(totalPages - 1, currentPage + 1);
                      
                      // Adjust if we're at the start or end
                      if (currentPage <= 2) {
                        endPage = Math.min(totalPages - 1, 3);
                      } else if (currentPage >= totalPages - 1) {
                        startPage = Math.max(2, totalPages - 2);
                      }
                      
                      // Show ellipsis if needed before middle pages
                      if (startPage > 2) {
                        pages.push(
                          <span key="ellipsis1" className="mx-1 text-gray-500 flex items-center justify-center h-8 w-8 sm:h-10 sm:w-10">
                            ...
                          </span>
                        );
                      }
                      
                      // Add middle pages
                      for (let i = startPage; i <= endPage; i++) {
                        pages.push(
                          <Button
                            key={i}
                            variant={currentPage === i ? "default" : "outline"}
                            className={cn(
                              "h-8 w-8 sm:h-10 sm:w-10 p-0 rounded-md text-sm sm:text-base flex items-center justify-center",
                              currentPage === i ? "bg-[#E87616] hover:bg-[#E87616]" : ""
                            )}
                            onClick={() => handlePageChange(i)}
                          >
                            {i}
                          </Button>
                        );
                      }
                      
                      // Show ellipsis if needed after middle pages
                      if (endPage < totalPages - 1) {
                        pages.push(
                          <span key="ellipsis2" className="mx-1 text-gray-500 flex items-center justify-center h-8 w-8 sm:h-10 sm:w-10">
                            ...
                          </span>
                        );
                      }
                      
                      // Always show last page
                      if (totalPages > 1) {
                        pages.push(
                          <Button
                            key={totalPages}
                            variant={currentPage === totalPages ? "default" : "outline"}
                            className={cn(
                              "h-8 w-8 sm:h-10 sm:w-10 p-0 rounded-md text-sm sm:text-base flex items-center justify-center",
                              currentPage === totalPages ? "bg-[#E87616] hover:bg-[#E87616]" : ""
                            )}
                            onClick={() => handlePageChange(totalPages)}
                          >
                            {totalPages}
                          </Button>
                        );
                      }
                      
                      return pages;
                    }
                  })()}
                  
                  <Button
                    variant="outline"
                    className="h-8 w-8 sm:h-10 sm:w-10 p-0 rounded-md text-sm sm:text-base flex items-center justify-center"
                    onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    aria-label="Next page"
                  >
                    &gt;
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>

        <FilterDialog 
          open={isFilterOpen} 
          onOpenChange={setIsFilterOpen} 
          onApplyFilters={handleApplyFilters}
          initialFilter={currentFilter}
          externalFilters={appliedFilters}
        />

        {/* Sticky action buttons for the first vendor when scrolling (visible on mobile) */}
        {/* <div className="block md:hidden">
          {mockVendors.length > 0 && (
            <ActionButtons 
              vendor={mapVendorToActionButtonsProps(mockVendors[0])}
              isLoggedIn={isLoggedIn}
              onLogin={handleLogin}
              variant="sticky"
              className="px-4 py-3"
            />
          )}
        </div> */}
      </Layout>
    </>
  );
};  

export default ServiceListing;
