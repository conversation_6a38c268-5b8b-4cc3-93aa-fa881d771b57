
/**
 * FilterDialog.tsx
 * 
 * This component displays a dialog with various filter options for vendors.
 * Updated to properly handle filter reset when parent component clears filters.
 */

import { useState, useEffect } from "react";
import { X } from "lucide-react";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import VegNonVegFilter from "./VegNonVegFilter";
import CuisinesFilter from "./CuisinesFilter";
import ServicesFilter from "./ServicesFilter";
import RatingFilter from "./RatingFilter";
import PriceFilter from "./PriceFilter";
import DistanceFilter from "./DistanceFilter";
import { Sheet, SheetContent } from "@/components/ui/sheet";

interface FilterDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onApplyFilters: (filters: FilterState) => void;
  initialFilter?: FilterCategory;
  /**
   * External filters state passed from parent component.
   * Used to sync filter dialog state with parent component state.
   */
  externalFilters?: FilterState;
}

export type FilterCategory = 
  | "Veg | Non-Veg"
  | "Cuisines"
  | "Services"
  | "Rating"
  | "Price"
  | "By Distance";

export interface FilterState {
  vegType: ("Veg" | "Non-Veg")[];
  cuisines: string[];
  services: string[];
  rating: string;
  price: string;
  distance: number;
  // Track which filters have been explicitly changed by the user
  changedFilters?: Set<string>;
}

const FilterDialog = ({ open, onOpenChange, onApplyFilters, initialFilter, externalFilters }: FilterDialogProps) => {
  const [activeFilter, setActiveFilter] = useState<FilterCategory>("Veg | Non-Veg");
  const [filters, setFilters] = useState<FilterState>({
    vegType: [],
    cuisines: [],
    services: [],
    rating: "any", // Initialize rating filter with 'any'
    price: "",
    distance: 1, // Set default distance to 1km to show vendors with small delivery radius
    changedFilters: new Set<string>(), // Track which filters have been explicitly changed
  });

  // Set active filter when dialog opens with initialFilter prop
  useEffect(() => {
    if (open && initialFilter) {
      console.log('Setting active filter to:', initialFilter);
      setActiveFilter(initialFilter);
    }
  }, [open, initialFilter]);
  
  // Initialize filters from localStorage if available
  useEffect(() => {
    if (open) {
      console.log('Filter dialog opened with filters:', filters);
    }
  }, [open, filters]);

  // Sync filters with external filters from parent component
  useEffect(() => {
    if (externalFilters) {
      console.log('Syncing with external filters:', externalFilters);
      setFilters({
        vegType: externalFilters.vegType || [],
        cuisines: externalFilters.cuisines || [],
        services: externalFilters.services || [],
        rating: externalFilters.rating || "any",
        price: externalFilters.price || "",
        distance: externalFilters.distance || 1, // Always default to 1km
        changedFilters: externalFilters.changedFilters || new Set<string>(),
      });
    }
  }, [externalFilters]);

  const handleClearAll = () => {
    setFilters({
      vegType: [],
      cuisines: [],
      services: [],
      rating: "any", // Reset rating to 'any'
      price: "",
      distance: 1, // Set minimum distance to 1km
      changedFilters: new Set<string>(), // Reset changed filters tracking
    });
  };

  const handleApplyFilters = () => {
    onApplyFilters(filters);
    onOpenChange(false);
  };

  const isGreenCategory = (filter: FilterCategory) => {
    return filter === "Veg | Non-Veg" || filter === "Cuisines" || filter === "Services" || filter === "By Distance";
  };

  const renderFilterContent = () => {
    switch (activeFilter) {
      case "Veg | Non-Veg":
        return <VegNonVegFilter 
          selectedOptions={filters.vegType} 
          onChange={(vegType) => {
            const changedFilters = new Set(filters.changedFilters);
            changedFilters.add('vegType');
            setFilters({ ...filters, vegType, changedFilters });
          }} 
        />;
      case "Cuisines":
        return <CuisinesFilter 
          selectedCuisines={filters.cuisines} 
          onChange={(cuisines) => {
            const changedFilters = new Set(filters.changedFilters);
            changedFilters.add('cuisines');
            setFilters({ ...filters, cuisines, changedFilters });
          }} 
        />;
      case "Services":
        return <ServicesFilter 
          selectedServices={filters.services} 
          onChange={(services) => {
            const changedFilters = new Set(filters.changedFilters);
            changedFilters.add('services');
            setFilters({ ...filters, services, changedFilters });
          }} 
        />;
      case "Rating":
        return <RatingFilter 
          selectedRating={filters.rating} 
          onChange={(rating) => {
            const changedFilters = new Set(filters.changedFilters);
            changedFilters.add('rating');
            setFilters({ ...filters, rating, changedFilters });
          }} 
        />;
      case "Price":
        return <PriceFilter 
          selectedPrice={filters.price} 
          onChange={(price) => {
            const changedFilters = new Set(filters.changedFilters);
            changedFilters.add('price');
            setFilters({ ...filters, price, changedFilters });
          }} 
        />;
      case "By Distance":
        return <DistanceFilter 
          distance={filters.distance} 
          onChange={(distance) => {
            const changedFilters = new Set(filters.changedFilters);
            changedFilters.add('distance');
            setFilters({ ...filters, distance, changedFilters });
          }} 
        />;
      default:
        return null;
    }
  };

  const FilterComponent = (

    
    <div className="min-h-[438px] w-full relative p-[30px] pl-[0] flex flex-col pr-[0]">

      <div className="flex pl-[30px] gap-[121px] positon-relative">
        <div 
          className="text-[#9B9B9B] font-lato text-sm cursor-pointer mb-2.5 relative z-10"
          onClick={handleClearAll}
        >
          Clear All
        </div>
        <div className="text-[#9B9B9B] font-lato text-xl font-semibold tracking-wider text-center mb-10 absolute top-[26px] left-0 right-0 bottom-0 margin-auto md:static">
          FILTERS
        </div>
      </div>

      <div className="flex md:min-h-[350px] w-full max-md:flex-col relative overflow-hidden flex-1 mb-[25px] pr-[30px]">
        <div className="w-[151px] p-5 border-r border-[#DEDEDE] max-md:w-full max-md:border-b max-md:border-r-0 max-md:border-[#DEDEDE] relative">
          
        <div
  className="flex flex-col gap-5 relative max-md:flex-row max-md:overflow-x-auto max-md:pb-2"
  style={{
    scrollbarWidth: 'none',             // Firefox
    msOverflowStyle: 'none',            // IE 10+
    // Note: scrollbarColor must be camelCase
    scrollbarColor: 'transparent transparent' // Firefox
  }}
>
            {(["Veg | Non-Veg", "Cuisines", "Services", "Rating", "Price", "By Distance"] as FilterCategory[]).map((filter) => {
              const isActive = activeFilter === filter;
              const isGreen = isGreenCategory(filter);
              
              const textColor = isActive
                ? isGreen 
                  ? "text-[#008001]" 
                  : "text-[#008001]"
                : "text-[#9B9B9B]";
              
              const indicatorColor = isActive
                ? isGreen
                  ? "bg-[#008001]"
                  : "bg-[#008001]"
                : "";
              
              return (
                <div
                  key={filter}
                  className={cn(
                    "font-lato text-sm font-semibold cursor-pointer relative max-md:whitespace-nowrap " ,
                    textColor
                  )}
                  onClick={() => setActiveFilter(filter)}
                >
                  {isActive && (
                    <div className={cn( 
                      "absolute -bottom-1 left-0 w-full h-[2px] bg-gray-600 md:hidden",
                      indicatorColor
                    )} />
                  )}
                  {filter}
                </div>
              );
            })}
          </div>
          
        </div>

        <div className="flex-1 p-5 relative overflow-y-auto pb-[45px] md:pb-[30px]" style={{ maxHeight: "calc(100vh - 300px)" }}>
          {renderFilterContent()}
        </div>
      </div>

      {/* Fixed position for Apply button on mobile */}
      <div className="mt-4 md:mt-0 sticky md:static bottom-4 z-10 pl-[30px] pr-[30px]">
        <Button 
          variant="orange" 
          className="w-full md:w-auto md:min-w-[175px] h-[55px] rounded-md font-bold text-base"
          onClick={handleApplyFilters}
        >
          Apply
        </Button>
      </div>
      
      <button 
        className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
        onClick={() => handleDialogClose(false)}
      >
        <X className="h-4 w-4" />
        <span className="sr-only">Close</span>
      </button>
    </div>
  );

  // Handle dialog close event
  const handleDialogClose = (isOpen: boolean) => {
    // If dialog is closing without applying filters, reset to external filters
    if (!isOpen) {
      if (externalFilters) {
        setFilters({
          vegType: externalFilters.vegType || [],
          cuisines: externalFilters.cuisines || [],
          services: externalFilters.services || [],
          rating: externalFilters.rating || "any",
          price: externalFilters.price || "",
          distance: externalFilters.distance || 1,
          changedFilters: externalFilters.changedFilters || new Set<string>(),
        });
      } else {
        // If no external filters, reset to defaults
        setFilters({
          vegType: [],
          cuisines: [],
          services: [],
          rating: "any",
          price: "",
          distance: 1,
          changedFilters: new Set<string>(),
        });
      }
    }
    // Call the original onOpenChange handler
    onOpenChange(isOpen);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleDialogClose}>
        <DialogContent className="p-0 gap-0 sm:max-w-[775px] min-h-[551px]">
          <DialogTitle className="sr-only">Filters</DialogTitle>
          {FilterComponent}
        </DialogContent>
      </Dialog>

      <Sheet open={open && window.innerWidth < 768} onOpenChange={handleDialogClose}>
        <SheetContent side="bottom" className="h-[90vh] p-0">
          {FilterComponent}
        </SheetContent>
      </Sheet>
    </>
  );
};

export default FilterDialog;
