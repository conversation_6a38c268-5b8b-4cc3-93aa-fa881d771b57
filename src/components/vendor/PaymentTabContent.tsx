
/**
 * PaymentTabContent Component
 * 
 * Updated to fetch and display dynamic payment methods data based on vendor ID.
 * Retrieves payment methods from the payment_methods table in the database.
 * Displays payment methods based on the payment_methods array in vendor_other_details table.
 */

import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { CreditCard, Wallet, BanknoteIcon } from "lucide-react";
import { fetchVendorPaymentMethods } from "@/services/vendorService";
import { useParams } from "react-router-dom";

interface PaymentMethod {
  id: string;
  method_name: string;
  description: string | null;
  image_icon: string | null;
  selected: boolean;
}

interface PaymentTabContentProps {
  className?: string;
  vendorId?: string;
}

const PaymentTabContent = ({ className, vendorId }: PaymentTabContentProps) => {
  const { id } = useParams<{ id: string }>();
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Function to get the appropriate icon for a payment method
  const getPaymentIcon = (method: PaymentMethod) => {
    // If the method has an image_icon URL, use it
    if (method.image_icon) {
      return (
        <img 
          src={method.image_icon} 
          alt={`${method.method_name} icon`} 
          className="h-5 w-5 object-contain"
          onError={(e) => {
            // If image fails to load, fall back to default icon
            console.error(`Failed to load image for ${method.method_name}`, e);
            e.currentTarget.style.display = 'none';
            // We'll show the fallback icon in this case
            const fallbackIcon = document.createElement('span');
            fallbackIcon.className = 'fallback-icon';
            e.currentTarget.parentNode?.appendChild(fallbackIcon);
          }}
        />
      );
    }
    
    // Otherwise, use the default icons based on method name or ID
    const methodIdentifier = (method.id || method.method_name).toLowerCase();
    
    switch (methodIdentifier) {
      case 'cash':
        return <BanknoteIcon className="h-5 w-5 text-black" />;
      case 'card':
        return <CreditCard className="h-5 w-5 text-[#E87616]" />;
      case 'wallet':
        return <Wallet className="h-5 w-5 text-black" />;
      case 'upi':
        return (
          <svg width="10" height="18" viewBox="0 0 10 18" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-5 w-5">
            <path d="M2.88879 15.0454L0.696321 17.8901C0.495289 18.118 0.0538498 17.9653 0.******** 17.6542C-0.0268192 17.3924 0.0506487 16.9026 0.0762579 16.6163C0.540105 11.4247 1.20466 6.25067 1.67651 1.05904C1.73093 0.717642 1.93037 0.587995 2.23095 0.642735C2.4099 0.675147 2.55075 0.987739 2.64614 1.14764C3.09943 1.90823 3.51462 2.88418 3.99543 3.59112C4.01496 3.61993 4.03928 3.66062 4.0729 3.66566L4.40101 0.343107C4.55787 -0.108135 5.04989 -0.112457 5.23876 0.319339C6.60405 2.95945 8.24464 5.49152 9.59232 8.13415C9.71717 8.37904 9.9166 8.66822 9.75366 8.95777C9.60193 9.22787 9.22195 9.6611 9.01932 9.93228C7.17673 12.3927 5.27141 14.7955 3.43107 17.2584C3.15193 17.5224 2.77771 17.3463 2.70344 16.9627L2.88847 15.0454H2.88879Z" fill="black"/>
          </svg>
        );
      case 'netbanking':
        return (
          <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg" className="h-5 w-5">
            <path d="M1 6.41743L9.5 0.760742L18 6.41743" stroke="black" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M2.88892 17.2168H16.1111" stroke="black" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M7.61108 5.90332H11.3889" stroke="black" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M3.83325 14.1312V8.98877" stroke="black" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M7.61108 14.1312V8.98877" stroke="black" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M11.3889 14.1312V8.98877" stroke="black" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M15.1667 14.1312V8.98877" stroke="black" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        );
      default:
        return <BanknoteIcon className="h-5 w-5 text-black" />;
    }
  };
  
  
  useEffect(() => {
    const fetchPaymentMethods = async () => {
      try {
        setLoading(true);
        // Use the vendorId from props or from the URL params
        const effectiveVendorId = vendorId || id;
        
        if (!effectiveVendorId) {
          throw new Error('Vendor ID is required');
        }
        
        // Fetch payment methods data
        const paymentMethodsData = await fetchVendorPaymentMethods(effectiveVendorId);
        console.log('Fetched payment methods data:', paymentMethodsData);
        
        if (paymentMethodsData && paymentMethodsData.length > 0) {
          setPaymentMethods(paymentMethodsData);
        } else {
          // Fallback to default payment methods if none found
          setPaymentMethods([
            {
              id: "cash",
              method_name: "Cash",
              description: "Pay cash or ask for QR code",
              image_icon: null,
              selected: true
            },
            {
              id: "card",
              method_name: "Card",
              description: "PhonePe, Amazon Pay & more",
              image_icon: null,
              selected: true
            },
            {
              id: "wallet",
              method_name: "Wallet",
              description: "PhonePe, Amazon Pay & more",
              image_icon: null,
              selected: false
            },
            {
              id: "upi",
              method_name: "UPI",
              description: "Select from a list of banks",
              image_icon: null,
              selected: false
            },
            {
              id: "netbanking",
              method_name: "Net Banking",
              description: "Select from a list of banks",
              image_icon: null,
              selected: false
            }
          ]);
        }
      } catch (err: any) {
        console.error('Error fetching payment methods data:', err);
        setError('Failed to load payment methods data');
        // Fallback to default payment methods
        setPaymentMethods([
          {
            id: "cash",
            method_name: "Cash",
            description: "Pay cash or ask for QR code",
            image_icon: null,
            selected: true
          },
          {
            id: "card",
            method_name: "Card",
            description: "PhonePe, Amazon Pay & more",
            image_icon: null,
            selected: true
          },
          {
            id: "wallet",
            method_name: "Wallet",
            description: "PhonePe, Amazon Pay & more",
            image_icon: null,
            selected: false
          },
          {
            id: "upi",
            method_name: "UPI",
            description: "Select from a list of banks",
            image_icon: null,
            selected: false
          },
          {
            id: "netbanking",
            method_name: "Net Banking",
            description: "Select from a list of banks",
            image_icon: null,
            selected: false
          }
        ]);
      } finally {
        setLoading(false);
      }
    };
    
    fetchPaymentMethods();
  // Only run once on mount
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  
  return (
    <div className={cn("mb-10 md:mb-20 mt-[20px] md:mt-[30px] border-t border-[#E6E6E6] px-3 md:px-0", className)}>
      <h2 className="text-lg md:text-xl font-bold text-[#2C2F24] mb-4 md:mb-8 mt-[20px] md:mt-[30px]">Mode of Payment Options</h2>
      
      {loading && (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#008001]"></div>
        </div>
      )}
      
      {error && (
        <div className="text-red-500 mb-4">{error}</div>
      )}
      
      <div className="flex flex-col gap-5">
        {/* Only display payment methods that are selected */}
        {paymentMethods.filter(method => method.selected).map((method) => (
          <div 
            key={method.id} 
            className="flex items-start gap-2 pb-5 border-b border-[#D9D9D9]"
          >
            <div className="w-9 h-9 border rounded-md flex items-center justify-center border-[#008001] border-opacity-50">
              {getPaymentIcon(method)}
            </div>
            <div className="ml-2">
              <h3 className="text-base font-semibold mb-1 text-[#2C2F24]">
                {method.method_name}
              </h3>
              {/* <p className="text-sm text-[#9B9B9B]">{method.description || ""}</p> */}
            </div>
          </div>
        ))}
        
        {/* Show a message if no payment methods are selected */}
        {paymentMethods.filter(method => method.selected).length === 0 && (
          <div className="text-gray-500 py-4">No payment methods selected by this vendor.</div>
        )}
      </div>
    </div>
  );
};

export default PaymentTabContent;
