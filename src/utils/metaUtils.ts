/**
 * Utility functions for generating SEO-friendly meta titles and descriptions
 */

export interface VendorMetaData {
  businessName?: string;
  city?: string;
  state?: string;
  vendorType?: string;
  cuisines?: string[];
  rating?: number;
}

/**
 * Generate dynamic title for Service Listing page
 */
export const generateServiceListingTitle = (
  vendorType: string,
  city?: string,
  state?: string
): string => {
  const baseTitle = "Homefoodi";
  let title = "";

  // Map vendor types to user-friendly names
  const vendorTypeMap: { [key: string]: string } = {
    'home-chef': 'Home Chefs',
    'tiffin_supplier': 'Tiffin Suppliers',
    'caters': 'Caterers'
  };

  const displayVendorType = vendorTypeMap[vendorType] || vendorType;

  if (city && state) {
    title = `${displayVendorType} in ${city}, ${state} | ${baseTitle}`;
  } else if (city) {
    title = `${displayVendorType} in ${city} | ${baseTitle}`;
  } else {
    title = `${displayVendorType} | ${baseTitle}`;
  }

  return title;
};

/**
 * Generate dynamic description for Service Listing page
 */
export const generateServiceListingDescription = (
  vendorType: string,
  city?: string,
  state?: string
): string => {
  const vendorTypeMap: { [key: string]: string } = {
    'home-chef': 'home chefs offering delicious homemade meals',
    'tiffin_supplier': 'tiffin suppliers providing fresh daily meals',
    'caters': 'caterers for your special events and occasions'
  };

  const vendorDescription = vendorTypeMap[vendorType] || 'food service providers';
  const location = city && state ? `in ${city}, ${state}` : city ? `in ${city}` : '';

  return `Find the best ${vendorDescription} ${location}. Order fresh, healthy, and customized meals from verified vendors on Homefoodi.`;
};

/**
 * Generate dynamic title for Vendor Details page
 */
export const generateVendorDetailsTitle = (vendor: VendorMetaData): string => {
  const baseTitle = "Homefoodi";
  let title = "";

  if (vendor.businessName && vendor.city && vendor.state) {
    title = `${vendor.businessName} - ${vendor.city}, ${vendor.state} | ${baseTitle}`;
  } else if (vendor.businessName && vendor.city) {
    title = `${vendor.businessName} - ${vendor.city} | ${baseTitle}`;
  } else if (vendor.businessName) {
    title = `${vendor.businessName} | ${baseTitle}`;
  } else {
    title = `Vendor Details | ${baseTitle}`;
  }

  return title;
};

/**
 * Generate dynamic description for Vendor Details page
 */
export const generateVendorDetailsDescription = (vendor: VendorMetaData): string => {
  let description = "";

  if (vendor.businessName) {
    description = `Order from ${vendor.businessName}`;
    
    if (vendor.city && vendor.state) {
      description += ` in ${vendor.city}, ${vendor.state}`;
    } else if (vendor.city) {
      description += ` in ${vendor.city}`;
    }

    if (vendor.cuisines && vendor.cuisines.length > 0) {
      description += `. Specializing in ${vendor.cuisines.slice(0, 3).join(', ')}`;
    }

    if (vendor.rating && vendor.rating > 0) {
      description += `. Rated ${vendor.rating}/5 stars`;
    }

    description += ". Fresh, homemade meals delivered to your doorstep.";
  } else {
    description = "Order fresh, healthy, and customized meals from verified vendors on Homefoodi.";
  }

  return description;
};

/**
 * Generate keywords for SEO
 */
export const generateKeywords = (
  vendorType: string,
  city?: string,
  businessName?: string,
  cuisines?: string[]
): string => {
  const baseKeywords = ["homefoodi", "food delivery", "homemade food", "healthy meals"];
  
  const vendorTypeKeywords: { [key: string]: string[] } = {
    'home-chef': ["home chef", "home cooked meals", "homemade food"],
    'tiffin_supplier': ["tiffin service", "daily meals", "lunch delivery"],
    'caters': ["catering service", "event catering", "party food"]
  };

  let keywords = [...baseKeywords];

  // Add vendor type specific keywords
  if (vendorTypeKeywords[vendorType]) {
    keywords.push(...vendorTypeKeywords[vendorType]);
  }

  // Add location keywords
  if (city) {
    keywords.push(`food delivery ${city}`, `${vendorType} ${city}`);
  }

  // Add business name
  if (businessName) {
    keywords.push(businessName.toLowerCase());
  }

  // Add cuisine keywords
  if (cuisines && cuisines.length > 0) {
    keywords.push(...cuisines.map(cuisine => cuisine.toLowerCase()));
  }

  return keywords.join(', ');
};

/**
 * Generate canonical URL
 */
export const generateCanonicalUrl = (path: string): string => {
  const baseUrl = window.location.origin;
  return `${baseUrl}${path}`;
};
