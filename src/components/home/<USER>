import { SearchBar } from "@/components/ui/SearchBar";
import { ServiceCard } from "./ServiceCard";
import homechef1 from "../../assets/images/homechef3.jpeg";
import tiffin1 from "../../assets/images/tiffin3.jpeg";
import caterer1 from "../../assets/images/caterer3.jpeg";
import background  from "../../assets/images/background.jpeg";

export const Hero = () => {
  return (
    <section
      className="relative py-[60px] overflow-hidden"
      style={{
        backgroundImage:  `url(${background})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
      }}
      
    >
      {/* Green overlay with gradient matching Figma design */}
      <div 
        className="absolute inset-0 z-0"
        style={{
          background: "linear-gradient(178deg, rgba(0, 128, 1, 0.85) 39.9%, rgba(255, 255, 255, 0.85) 79.34%)",
        }}
      />

      {/* Green shadow effect in the upper part - minimized height */}
      <div 
        className="absolute top-0 left-0 right-0 h-[80px] z-0"
        style={{
          background: "radial-gradient(circle at center top, rgba(0, 128, 1, 1) 0%, rgba(0, 128, 1, 0.8) 50%, transparent 100%)",
          boxShadow: "0 0 30px 10px rgba(0, 128, 1, 0.6)",
        }}
      />

      {/* Content with gradient overlay */}
      <div className="relative z-10 max-w-[1120px] text-center mx-auto px-5">
        <h1 className="text-white text-[50px] font-black mb-6 md:mb-10 max-sm:text-3xl md:text-[40px] lg:text-[50px]">
          Order Healthy and Delicious Home Food
        </h1>

        <div className="max-w-[100%] mx-auto">
          <SearchBar />
        </div>

        <div className="flex justify-center 2xl:justify-between gap-5 md:gap-[15px] lg:gap-[30px] mt-8 md:mt-10 md:flex-row max-md:flex-col md:flex-wrap">
          <ServiceCard
            title="Home Chefs"
            subtitle="Customized home made food"
            backgroundImage={homechef1}
            iconColor="#E87616"
            shadowColor="#008001"
            link="/service-listing"
            vendorType="home-chef"
            className="md:w-[calc(50%-10px)] lg:w-[316px] max-md:mx-auto"
          />

          <ServiceCard
            title="Tiffin Suppliers"
            subtitle="Fresh Tiffins, Delivered Daily"
            backgroundImage={tiffin1}
            iconColor="#008001"
            shadowColor="#E87616"
            link="/service-listing?type=tiffin_supplier"
            vendorType="tiffin_supplier"
            className="md:w-[calc(50%-10px)] lg:w-[316px] max-md:mx-auto"
          />

          <ServiceCard
            title="Caterer"
            subtitle="Making Every Event Flavorful"
            backgroundImage={caterer1}
            iconColor="#E87616"
            shadowColor="#008001"
            link="/service-listing?type=caterer"
            vendorType="caterer"
            className="md:w-[calc(50%-10px)] lg:w-[316px] max-md:mx-auto md:mt-5 lg:mt-0 md:mx-auto lg:mx-0"
          />
        </div>
      </div>

      {/* Shadow effect at the bottom with darker grey */}
      <div 
        className="absolute bottom-0 left-0 right-0 h-[120px] z-0"
        style={{
          background: "linear-gradient(to bottom, transparent 0%, rgba(255, 255, 255, 0.85) 100%)",
          boxShadow: "0 8px 12px rgba(0, 0, 0, 0.5)",
        }}
      />
      
      {/* Additional darker grey shadow at the very bottom */}
      <div 
        className="absolute bottom-0 left-0 right-0 h-[40px] z-1"
        style={{
          background: "linear-gradient(to bottom, transparent 0%, rgba(50, 50, 50, 0.3) 100%)",
        }}
      />
    </section>
  );
};

export default Hero;
