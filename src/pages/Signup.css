/* Import Inter font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');

/* Signup page styles */
.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Apply Inter font to the entire app */
body {
  font-family: 'Inter', sans-serif;
}

/* Name dialog styles */
.name-dialog {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  max-width: 400px;
  margin: 0 auto;
}

/* Input placeholder styling */
input::placeholder {
  color: #9CA3AF;
}

/* Custom styles for the phone input to match the design */
input[type="tel"] {
  position: relative;
  font-size: 16px;
  color: #111827;
  height: 52px;
  border-color: #D1D5DB;
  border-radius: 8px;
  box-shadow: none;
  outline: none;
}

input[type="tel"]:focus {
  /* border-color: #D1D5DB; */
  box-shadow: none;
  outline: none;
}

/* Button hover effects */
button:hover {
  opacity: 0.9;
}

/* Checkbox styling */
.checkbox-custom {
  width: 16px;
  height: 16px;
  border: 1px solid #E5E7EB;
  border-radius: 4px;
}

/* Dialog animation */
.dialog-content {
  animation: dialogFadeIn 0.3s ease-out;
}

@keyframes dialogFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
