/**
 * RatingLoader Component
 * 
 * This component fetches the vendor reviews and updates the rating context
 * when the vendor detail page loads, ensuring the correct rating is displayed
 * in the header without requiring the user to click on the review tab.
 */

import { useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { fetchVendorReviews } from '@/services/reviewService';
import { useRating } from '@/contexts/RatingContext';

interface RatingLoaderProps {
  vendorId?: string;
}


export const RatingLoader: React.FC<RatingLoaderProps> = ({ vendorId }) => {
  const { id } = useParams<{ id: string }>();
  const { setVendorRating } = useRating();
  const hasLoaded = useRef(false);
  
  useEffect(() => {
    // Only fetch once per component mount
    if (hasLoaded.current) return;
    
    const effectiveVendorId = vendorId || id;
    if (!effectiveVendorId) return;
    
    const loadRating = async () => {
      try {
        const reviews = await fetchVendorReviews(effectiveVendorId);
        
        if (reviews && reviews.length > 0) {
          // Calculate average rating
          const totalRating = reviews.reduce((sum, review) => sum + Number(review.rating), 0);
          const avgRating = totalRating / reviews.length;
          const calculatedRating = parseFloat(avgRating.toFixed(1));
          
          // Update the rating in context
          setVendorRating(calculatedRating);
        }
        
        hasLoaded.current = true;
      } catch (error) {
        console.error('Error loading vendor rating:', error);
      }
    };
    
    loadRating();
  }, [id, vendorId, setVendorRating]);
  
  // This component doesn't render anything
  return null;
};

export default RatingLoader;
