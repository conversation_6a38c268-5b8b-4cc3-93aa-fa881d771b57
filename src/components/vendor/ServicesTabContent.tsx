/**
 * ServicesTabContent Component
 * 
 * Updated to fetch and display dynamic services data based on vendor ID.
 * Retrieves services from the vendor_other_details table in the database.
 */

import React, { useState, useEffect } from "react";
import { CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { fetchVendorServices } from "@/services/vendorService";
import { useParams } from "react-router-dom";

interface ServicesTabContentProps {
  className?: string;
  services?: string[];
  vendorId?: string;
}

const ServicesTabContent = ({ className, services: initialServices = [], vendorId }: ServicesTabContentProps) => {
  const { id } = useParams<{ id: string }>();
  const [services, setServices] = useState<string[]>(initialServices);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchServices = async () => {
      // If services are already provided as props, use them
      if (initialServices && initialServices.length > 0) {
        setServices(initialServices);
        return;
      }
      
      // Otherwise fetch from the database
      try {
        setLoading(true);
        // Use the vendorId from props or from the URL params
        const effectiveVendorId = vendorId || id;
        
        if (!effectiveVendorId) {
          throw new Error('Vendor ID is required');
        }
        
        // Fetch services data
        const servicesData = await fetchVendorServices(effectiveVendorId);
        console.log('Fetched services data:', servicesData);
        
        if (servicesData && servicesData.length > 0) {
          setServices(servicesData);
        }
      } catch (err) {
        console.error('Error fetching services data:', err);
        setError('Failed to load services data');
      } finally {
        setLoading(false);
      }
    };
    
    fetchServices();
  // Only run once on mount or when vendorId changes
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  
  // Split services into two columns
  const halfLength = Math.ceil(services.length / 2);
  const firstColumnServices = services.slice(0, halfLength);
  const secondColumnServices = services.slice(halfLength);

  return (
    <div className={cn("mb-10 md:mb-20 mt-[20px] md:mt-[30px] border-t border-[#E6E6E6] px-3 md:px-0", className)}>
      <h2 className="text-lg md:text-xl font-bold text-[#2C2F24] mb-4 md:mb-6 mt-[20px] md:mt-[30px]">Services</h2>
      
      {loading && (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#008001]"></div>
        </div>
      )}
      
      {error && (
        <div className="text-red-500 mb-4">{error}</div>
      )}
      
      <div className="flex flex-col md:flex-row gap-5 md:gap-[30px]">
        {/* First column */}
        <div className="space-y-4 md:space-y-5 border-b md:border-b-0 pb-5 md:pb-0 md:pr-[40px] md:border-r border-[#D9D9D9] w-full md:max-w-[50%]">
          {firstColumnServices.map((service, index) => (
            <div key={index} className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 md:h-5 md:w-5 text-[#008001] min-w-[16px] md:min-w-[20px]" />
              <span className="text-[#000] text-[13px] md:text-[14px] font-medium">{service}</span>
            </div>
          ))}
        </div>
        
        {/* Second column */}
        <div className="space-y-4 md:space-y-5 pt-0 md:pt-0 md:pl-[40px] w-full md:max-w-[50%]">
          {secondColumnServices.map((service, index) => (
            <div key={index} className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 md:h-5 md:w-5 text-[#008001] min-w-[16px] md:min-w-[20px]" />
              <span className="text-[#000] text-[13px] md:text-[14px] font-medium">{service}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ServicesTabContent;
