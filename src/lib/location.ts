/**
 * Location Service
 * Created: 2025-03-20
 * Updated: 2025-04-30
 * This module handles location services using browser's native permission system
 * and provides reverse geocoding to get address from coordinates
 * 
 * Geolocation usage justification:
 * - Essential for the core functionality of HomeFoodi to show nearby food services
 * - Required to determine which vendors can deliver to the user's location
 * - Necessary for calculating accurate distances between users and vendors
 * - Enables filtering of vendors based on delivery radius
 * - Location data is stored only locally in the user's browser for privacy
 */

// Keys for storing location-related data
const LOCATION_REQUESTED_KEY = 'homefoodi_location_requested';
const LOCATION_ADDRESS_KEY = 'homefoodi_user_address';
const LOCATION_CITY_KEY = 'homefoodi_user_city';

/**
 * Check if we should request location permission
 * Returns true if we haven't requested permission before
 */
export const shouldRequestLocationPermission = (): boolean => {
  // Always return true to ensure the browser's native permission dialog appears
  return true;
};

/**
 * Request location permission using browser's native permission system
 * This will directly trigger the browser's built-in permission dialog
 * Returns a promise that resolves to true if permission was granted, false otherwise
 * 
 * Geolocation is necessary for the core functionality of HomeFoodi:
 * - To show users nearby food services within their area
 * - To determine which vendors can deliver to the user's location
 * - To provide accurate distance information between users and vendors
 * - To enhance user experience by automatically detecting their location
 */
export const requestLocationPermission = async (): Promise<boolean> => {
  try {
    // First check if we already have a saved address - if so, don't request location
    const savedAddress = getUserAddress();
    if (savedAddress) {
      console.log('Already have a saved address, skipping location permission request:', savedAddress);
      return true; // Return true to indicate we have location data (from saved address)
    }
    
    // This will trigger the browser's native permission dialog directly
    if (navigator.geolocation) {
      console.log('Requesting geolocation permission...');
      // Get current position - this will prompt the browser's native permission dialog
      // This geolocation request is essential for the core functionality of HomeFoodi
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            // Success callback - location access granted
            // We need this location data to show nearby food services and calculate delivery availability
            console.log('Permission granted, got position:', position);
            resolve(position);
          },
          (error) => {
            console.error('Permission denied or error:', error);
            // Clear any existing location data when permission is denied
            clearLocationData();
            // Dispatch an event to notify components that permission was denied
            window.dispatchEvent(new CustomEvent('locationPermissionDenied'));
            reject(error);
          },
          {
            // High accuracy is necessary for precise vendor distance calculations
            enableHighAccuracy: true,
            // Reasonable timeout to prevent hanging while waiting for location
            timeout: 10000,
            // Always get fresh location data for accurate vendor availability
            maximumAge: 0
          }
        );
      });
      
      // Store the location data
      const { latitude, longitude } = position.coords;
      console.log('Storing coordinates:', { latitude, longitude });
      localStorage.setItem('homefoodi_user_latitude', latitude.toString());
      localStorage.setItem('homefoodi_user_longitude', longitude.toString());
      
      // Also store in userCoordinates format for consistency with other components
      localStorage.setItem('userCoordinates', JSON.stringify({ lat: latitude, lng: longitude }));
      
      // Try to get address from coordinates
      const address = await reverseGeocode(latitude, longitude);
      console.log('Got address from coordinates:', address);
      
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error requesting location:', error);
    // Clear any existing location data when there's an error
    clearLocationData();
    return false;
  }
};

/**
 * Clear all location data from localStorage
 */
export const clearLocationData = (): void => {
  console.log('Clearing all location data');
  localStorage.removeItem('homefoodi_user_latitude');
  localStorage.removeItem('homefoodi_user_longitude');
  localStorage.removeItem('userCoordinates');
  localStorage.removeItem(LOCATION_ADDRESS_KEY);
  localStorage.removeItem('homefoodi_selected_location');
  
  // Dispatch an event to notify components that location data was cleared
  window.dispatchEvent(new CustomEvent('locationDataCleared'));
};

/**
 * Reverse geocode coordinates to get address
 * Uses the Google Maps Geocoding API
 */
export const reverseGeocode = async (latitude: number, longitude: number): Promise<string | null> => {
  try {
    console.log('Reverse geocoding coordinates:', latitude, longitude);
    
    // Use Google Maps API key from environment variables
    const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
    console.log('Using Google Maps API Key:', apiKey ? 'Available' : 'Not available');
    
    if (!apiKey) {
      console.error('Google Maps API Key not found');
      return null;
    }
    
    // Use Google Maps Geocoding API to get address from coordinates
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${apiKey}`;
    console.log('Geocoding URL:', url);
    
    const response = await fetch(url);
    const data = await response.json();
    console.log('Geocoding response:', data);
    
    if (data.status === 'OK' && data.results && data.results.length > 0) {
      // Get the formatted address from the first result
      const address = data.results[0].formatted_address;
      console.log('Found address:', address);
      
      // Store the address in localStorage
      localStorage.setItem(LOCATION_ADDRESS_KEY, address);
      localStorage.setItem('homefoodi_selected_location', address);
      
      // Extract and store the city
      const city = extractCityFromAddress(address);
      console.log('Extracted city from geocoded address:', city);
      if (city) {
        localStorage.setItem(LOCATION_CITY_KEY, city);
        console.log('Extracted and stored city from geocoded address:', city);
      }
      
      // Dispatch a custom event to notify components that the address has been updated
      window.dispatchEvent(new CustomEvent('addressUpdated', { detail: address }));
      
      return address;
    } else {
      console.error('Geocoding failed or returned no results:', data.status);
      return null;
    }
  } catch (error) {
    console.error('Error reverse geocoding:', error);
    return null;
  }
};

/**
 * Get the user's current location if permission is granted
 * Returns null if permission is denied or location is unavailable
 * 
 * Geolocation is necessary for core HomeFoodi functionality:
 * - Required to show nearby food services within delivery radius
 * - Essential for determining which vendors can deliver to the user
 * - Needed to calculate accurate distances between users and vendors
 * - Improves user experience by automatically detecting location
 */
export const getUserLocation = async (): Promise<{ latitude: number; longitude: number; address?: string } | null> => {
  try {
    // Check if we have cached location data
    const cachedLat = localStorage.getItem('homefoodi_user_latitude');
    const cachedLng = localStorage.getItem('homefoodi_user_longitude');
    const cachedAddress = localStorage.getItem(LOCATION_ADDRESS_KEY);
    
    if (cachedLat && cachedLng) {
      return {
        latitude: parseFloat(cachedLat),
        longitude: parseFloat(cachedLng),
        address: cachedAddress || undefined
      };
    }
    
    // If no cached data, try to get current position
    // This geolocation request is essential for showing nearby food services
    if (navigator.geolocation) {
      return await new Promise((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          // Success callback - process user location to show relevant nearby services
          // This location data is necessary to determine which vendors can deliver to the user
          async (position) => {
            const latitude = position.coords.latitude;
            const longitude = position.coords.longitude;
            
            // Cache the location data
            localStorage.setItem('homefoodi_user_latitude', latitude.toString());
            localStorage.setItem('homefoodi_user_longitude', longitude.toString());
            localStorage.setItem('userCoordinates', JSON.stringify({ lat: latitude, lng: longitude }));
            
            // Try to get address from coordinates
            const address = await reverseGeocode(latitude, longitude);
            
            resolve({
              latitude,
              longitude,
              address: address || undefined
            });
          },
          (error) => {
            console.log('Error getting location:', error.message);
            resolve(null);
          },
          {
            // High accuracy is necessary for precise vendor distance calculations
            enableHighAccuracy: true,
            // Reasonable timeout to prevent hanging while waiting for location
            timeout: 10000,
            // Always get fresh location data for accurate vendor availability
            maximumAge: 0
          }
        );
      });
    }
  } catch (error) {
    console.error('Error getting user location:', error);
  }
  
  return null;
};

/**
 * Get the user's saved address from localStorage
 * Returns null if no address is saved
 */
export const getUserAddress = (): string | null => {
  return localStorage.getItem(LOCATION_ADDRESS_KEY);
};

/**
 * Extract city from a full address string
 * This function attempts to extract the city from a comma-separated address
 * @param address Full address string (e.g., "Fatehpur, Uttar Pradesh, India" or "PAC Road, Ganga Nagar Colony, Police Lines, Fatehpur, Uttar Pradesh, India")
 * @returns The extracted city or null if extraction fails
 */
export const extractCityFromAddress = (address: string): string | null => {
  if (!address) return null;
  
  try {
    console.log('Attempting to extract city from address:', address);
    
    // Extract city from address patterns
    const extractPatterns = [
      { pattern: /Sector.*?Noida/i, city: 'Noida' },
      { pattern: /Police Lines.*?Fatehpur/i, city: 'Fatehpur' },
      { pattern: /PAC Road.*?Fatehpur/i, city: 'Fatehpur' },
      { pattern: /New Delhi/i, city: 'New Delhi' }
    ];
    
    // Check for known patterns
    for (const { pattern, city } of extractPatterns) {
      if (pattern.test(address)) {
        console.log(`Found pattern match for city: ${city}`);
        return city;
      }
    }
    
    // Split the address by commas
    const parts = address.split(',').map(part => part.trim());
    console.log('Address parts:', parts);
    
    // Map of Indian cities and their states - dynamically constructed
    const cityStateMap: Record<string, string[]> = {};
    
    // Major cities in India with their states
    const cityStateData = [
       ['New Delhi', ['Delhi']],
      ['Delhi', ['Delhi']],
      ['Mumbai', ['Maharashtra']],
      ['Kolkata', ['West Bengal']],
      ['Chennai', ['Tamil Nadu']],
      ['Bangalore', ['Karnataka']],
      ['Hyderabad', ['Telangana', 'Andhra Pradesh']],
      ['Ahmedabad', ['Gujarat']],
      ['Pune', ['Maharashtra']],
      ['Jaipur', ['Rajasthan']],
      ['Lucknow', ['Uttar Pradesh']],
      ['Kanpur', ['Uttar Pradesh']],
      ['Nagpur', ['Maharashtra']],
      ['Indore', ['Madhya Pradesh']],
      ['Thane', ['Maharashtra']],
      ['Bhopal', ['Madhya Pradesh']],
      ['Patna', ['Bihar']],
      ['Agra', ['Uttar Pradesh']],
      ['Noida', ['Uttar Pradesh']],
      ['Fatehpur', ['Uttar Pradesh']]
    ];
    
    // Populate the map
    cityStateData.forEach(([city, states]) => {
      cityStateMap[city as string] = states as string[];
    });
    
    // Extract state from address
    let detectedState = '';
    for (const part of parts) {
      if (part.includes('Pradesh') || 
          part.includes('Maharashtra') || 
          part.includes('Bengal') || 
          part.includes('Karnataka') || 
          part.includes('Gujarat') || 
          part.includes('Rajasthan') || 
          part.includes('Bihar') ||
          part.includes('Delhi')) {
        detectedState = part;
        break;
      }
    }
    
    // If we found a state, look for matching cities
    if (detectedState) {
      for (const [city, states] of Object.entries(cityStateMap)) {
        if (states.some(state => detectedState.includes(state))) {
          // Check if the city is in the address
          if (address.includes(city)) {
            console.log(`Found city ${city} matching state ${detectedState}`);
            return city;
          }
        }
      }
    }
    
    // Get the list of cities from the keys of the cityStateMap
    const commonCities = Object.keys(cityStateMap);
    
    // Add more cities that might not be in the state map
    const additionalCities = [
      'Visakhapatnam', 'Vadodara', 'Ghaziabad', 'Ludhiana', 'Nashik', 'Faridabad', 'Meerut', 'Rajkot', 'Varanasi', 'Srinagar', 
      'Aurangabad', 'Dhanbad', 'Amritsar', 'Allahabad', 'Ranchi', 'Howrah', 'Coimbatore', 'Jabalpur', 'Gwalior', 'Vijayawada', 
      'Jodhpur', 'Madurai', 'Raipur', 'Kota', 'Chandigarh', 'Guwahati', 'Solapur', 'Hubli', 'Dharwad', 'Bareilly', 'Moradabad', 
      'Mysore', 'Gurgaon', 'Aligarh', 'Jalandhar', 'Tiruchirappalli', 'Bhubaneswar', 'Salem', 'Mira-Bhayandar', 'Warangal', 'Jalgaon', 
      'Guntur', 'Bhiwandi', 'Saharanpur', 'Gorakhpur', 'Bikaner', 'Amravati', 'Jamshedpur', 'Bhilai', 'Cuttack', 'Firozabad', 
      'Kochi', 'Nellore', 'Bhavnagar', 'Dehradun', 'Durgapur', 'Asansol', 'Rourkela', 'Nanded', 'Kolhapur', 'Ajmer', 'Akola', 
      'Gulbarga', 'Jamnagar', 'Ujjain', 'Loni', 'Siliguri', 'Jhansi', 'Ulhasnagar', 'Jammu', 'Sangli', 'Erode', 'Belgaum', 
      'Mangalore', 'Ambattur', 'Tirunelveli', 'Malegaon', 'Gaya', 'Udaipur', 'Maheshtala'
    ];
    
    // Combine all cities, ensuring no duplicates
    additionalCities.forEach(city => {
      if (!commonCities.includes(city)) {
        commonCities.push(city);
      }
    });
    
    // First check for exact city match in the address parts
    for (const part of parts) {
      // Special case for New Delhi which should take precedence over Delhi
      if (part === 'New Delhi') {
        console.log('Found exact match for New Delhi');
        return 'New Delhi';
      }
      
      if (commonCities.includes(part)) {
        console.log('Found exact city match:', part);
        return part;
      }
    }
    
    // If no exact match, check for city names that might be part of a longer string
    for (const part of parts) {
      for (const city of commonCities) {
        if (part.includes(city)) {
          console.log('Found city within part:', city, 'in', part);
          return city;
        }
      }
    }
    
    // Specific check for addresses with "Police Lines" which often precedes the city
    for (let i = 0; i < parts.length; i++) {
      if (parts[i].includes('Police Lines') && i + 1 < parts.length) {
        console.log('Found Police Lines, extracting next part as city:', parts[i + 1]);
        return parts[i + 1];
      }
    }
    
    // If still no match found, use the third-to-last part as it's often the city in Indian addresses
    // Format: street, area, landmark, city, state, country
    if (parts.length >= 3) {
      const cityPart = parts[parts.length - 3]; // Usually city is before state and country
      console.log('Using third-to-last part as city:', cityPart);
      return cityPart;
    }
    
    // If all else fails, return the first part as a fallback
    if (parts.length >= 1) {
      console.log('Falling back to first part as city:', parts[0]);
      return parts[0];
    }
    
    return null;
  } catch (error) {
    console.error('Error extracting city from address:', error);
    return null;
  }
};

/**
 * Set the user's address in localStorage
 * Also extracts and stores the city component
 */
export const setUserAddress = (address: string): void => {
  localStorage.setItem(LOCATION_ADDRESS_KEY, address);
  
  // Extract and store the city
  const city = extractCityFromAddress(address);
  if (city) {
    localStorage.setItem(LOCATION_CITY_KEY, city);
    console.log('Extracted and stored city:', city);
  }
};
