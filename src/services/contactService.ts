/**
 * Contact Service
 * 
 * This file contains functions for handling contact form submissions.
 * It provides methods to submit contact form data to the Supabase database.
 * Updated to use contact_inquiries table without subject field.
 */

import { supabase } from '../lib/supabase';

/**
 * Interface for contact form submission data
 */
export interface ContactInquiry {
  id?: string;
  name: string;
  email: string;
  phone_number: string;
  message: string;
  submitted_at?: string;
  created_at?: string;
  updated_at?: string;
}

/**
 * Submit a contact form to the database
 * @param submission - The contact form submission data
 * @returns Promise with the submission result or error
 */
export const submitContactForm = async (submission: Omit<ContactInquiry, 'id' | 'submitted_at' | 'created_at' | 'updated_at'>): Promise<{ data: any; error: any }> => {
  try {
    console.log('ContactService: Submitting contact form:', submission);
    
    // Validate required fields
    if (!submission.name || !submission.email || !submission.phone_number || !submission.message) {
      console.error('ContactService: Missing required fields');
      return { 
        data: null, 
        error: { message: 'All fields are required' } 
      };
    }
    
    // Prepare data for contact_inquiries table
    const inquiryData = {
      name: submission.name,
      email: submission.email,
      phone_number: submission.phone_number,
      message: submission.message,
      submitted_at: new Date().toISOString()
    };
    
    // Submit to Supabase
    const { data, error } = await supabase
      .from('contact_inquiries')
      .insert([inquiryData])
      .select();
    
    if (error) {
      console.error('Error submitting contact form:', error);
      return { data: null, error };
    }

    console.log('ContactService: Contact form submitted successfully:', data);
    return { data, error: null };
  } catch (error) {
    console.error('Unexpected error in submitContactForm:', error);
    return { 
      data: null, 
      error: { message: 'An unexpected error occurred' } 
    };
  }
};
