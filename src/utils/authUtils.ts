/**
 * Authentication Utility Functions
 * 
 * This file contains utility functions for checking authentication status
 * and handling authentication-related operations across the application.
 */

import { toast } from "react-hot-toast";

/**
 * Checks if the user is authenticated by looking for a token in localStorage
 * @returns {boolean} True if authenticated, false otherwise
 */
export const isAuthenticated = (): boolean => {
  const token = localStorage.getItem('homefoodi_auth_token');
  const userData = localStorage.getItem('homefoodi_user');
  
  return !!token && !!userData;
};

/**
 * Gets the current user ID from localStorage
 * @returns {string|null} User ID if authenticated, null otherwise
 */
export const getUserId = (): string | null => {
  try {
    // First check if we're authenticated
    if (!isAuthenticated()) return null;
    
    const userData = localStorage.getItem('homefoodi_user');
    if (!userData) return null;
    
    const user = JSON.parse(userData);
    return user.id || user.userId || null;
  } catch (error) {
    console.error('Error parsing user data:', error);
    return null;
  }
};

/**
 * Performs an action only if the user is authenticated
 * Otherwise shows a login message and returns false
 * 
 * @param {Function} callback - The function to execute if authenticated
 * @param {string} actionName - Name of the action (for error message)
 * @returns {boolean} Whether the action was performed
 */
export const requireAuth = (callback: Function, actionName: string = 'perform this action'): boolean => {
  if (!isAuthenticated()) {
    toast.error(`Please log in to ${actionName}`);
    return false;
  }
  
  callback();
  return true;
};
