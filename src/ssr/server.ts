/**
 * SSR Server Configuration
 * 
 * This file sets up a basic SSR server for the Homefoodi application.
 * It's prepared for future SSR implementation when needed.
 * 
 * To enable SSR:
 * 1. Install additional dependencies: express, @vitejs/plugin-react-ssr
 * 2. Update vite.config.ts to include SSR configuration
 * 3. Create entry-server.tsx for server-side rendering
 * 4. Update package.json scripts for SSR build and serve
 */

import express from 'express';
import { ViteDevServer } from 'vite';

const isProduction = process.env.NODE_ENV === 'production';

async function createServer() {
  const app = express();

  // Create Vite server in middleware mode
  let vite: ViteDevServer;
  
  if (!isProduction) {
    // Development mode - use Vite dev server
    const { createServer: createViteServer } = await import('vite');
    vite = await createViteServer({
      server: { middlewareMode: true },
      appType: 'custom'
    });
    app.use(vite.ssrLoadModule);
  } else {
    // Production mode - serve static files
    app.use(express.static('dist/client'));
  }

  // Handle all routes for SSR
  app.use('*', async (req, res, next) => {
    const url = req.originalUrl;

    try {
      let template: string;
      let render: (url: string) => Promise<{ html: string; head: string }>;

      if (!isProduction) {
        // Development: read template from index.html and transform via Vite
        template = await vite.transformIndexHtml(url, 
          await import('fs').then(fs => fs.readFileSync('index.html', 'utf-8'))
        );
        render = (await vite.ssrLoadModule('/src/entry-server.tsx')).render;
      } else {
        // Production: use pre-built template and server bundle
        template = await import('fs').then(fs => 
          fs.readFileSync('dist/client/index.html', 'utf-8')
        );
        render = (await import('./dist/server/entry-server.js')).render;
      }

      // Render the app HTML
      const { html: appHtml, head } = await render(url);

      // Replace placeholders in template
      const html = template
        .replace('<!--ssr-outlet-->', appHtml)
        .replace('<!--ssr-head-->', head);

      res.status(200).set({ 'Content-Type': 'text/html' }).end(html);
    } catch (e) {
      // If an error is caught, let Vite fix the stack trace so it maps back to
      // your actual source code.
      if (!isProduction) {
        vite.ssrFixStacktrace(e as Error);
      }
      next(e);
    }
  });

  return app;
}

// Start server only if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  createServer().then(app => {
    const port = process.env.PORT || 3000;
    app.listen(port, () => {
      console.log(`Server running at http://localhost:${port}`);
    });
  });
}

export { createServer };
