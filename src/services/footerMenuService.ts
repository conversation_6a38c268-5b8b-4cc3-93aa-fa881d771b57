/**
 * Footer Menu Service
 * 
 * Service for fetching footer menu items from the database
 */

import { supabase } from "@/lib/supabase";

export interface FooterMenuItem {
  id: string;
  menu_name: string;
  menu_url: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Fetches all active footer menu items from the database
 * @returns Array of footer menu items
 */
export const getFooterMenuItems = async (): Promise<FooterMenuItem[]> => {
  try {
    const { data, error } = await supabase
      .from("footer_menu")
      .select("*")
      .eq("is_active", true)
      .order("id", { ascending: true });

    if (error) {
      console.error("Error fetching footer menu items:", error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error("Unexpected error fetching footer menu items:", error);
    return [];
  }
};
