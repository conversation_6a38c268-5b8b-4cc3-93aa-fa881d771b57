/**
 * Service Service
 * 
 * This file contains functions for fetching service data from the database.
 * It provides methods to get all services and find services by ID.
 */

import { supabase } from '@/lib/supabase';

export interface Service {
  id: string;
  name: string;
  vendor_type: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  from_day: string | null;
  end_day: string | null;
  start_time: string | null;
  end_time: string | null;
  distance: number | null;
}

/**
 * Fetch all services from the database
 * @returns Promise with service data
 */
export const fetchAllServices = async (): Promise<Service[]> => {
  try {
    const { data, error } = await supabase
      .from('services')
      .select('*')
      .eq('is_active', true)
      .order('name');

    if (error) {
      console.error('Error fetching services:', error);
      return [];
    }

    return data || [];
  } catch (err) {
    console.error('Exception in fetchAllServices:', err);
    return [];
  }
};

/**
 * Fetch services by vendor type
 * @param vendorType - The vendor type to filter services by
 * @returns Promise with service data
 */
export const fetchServicesByVendorType = async (vendorType: string): Promise<Service[]> => {
  try {
    let query = supabase
      .from('services')
      .select('*')
      .eq('is_active', true);
    
    // Handle different vendor type variations
    if (vendorType === 'caters') {
      query = query.or('vendor_type.eq.caters,vendor_type.eq.caterer,vendor_type.eq.catering');
    } 
    else if (vendorType === 'tiffin_supplier') {
      query = query.or('vendor_type.eq.tiffin_supplier,vendor_type.eq.tiffin_service,vendor_type.eq.tiffin');
    }
    else {
      query = query.eq('vendor_type', vendorType);
    }
    
    const { data, error } = await query.order('name');

    if (error) {
      console.error('Error fetching services by vendor type:', error);
      return [];
    }

    return data || [];
  } catch (err) {
    console.error('Exception in fetchServicesByVendorType:', err);
    return [];
  }
};

/**
 * Fetch services by IDs
 * @param ids - Array of service IDs
 * @returns Promise with service data
 */
export const fetchServicesByIds = async (ids: string[]): Promise<Service[]> => {
  if (!ids.length) return [];
  
  try {
    const { data, error } = await supabase
      .from('services')
      .select('*')
      .in('id', ids)
      .eq('is_active', true)
      .order('name');

    if (error) {
      console.error('Error fetching services by IDs:', error);
      return [];
    }

    return data || [];
  } catch (err) {
    console.error('Exception in fetchServicesByIds:', err);
    return [];
  }
};

/**
 * Get service names by IDs
 * @param ids - Array of service IDs
 * @returns Promise with array of service names
 */
export const getServiceNamesByIds = async (ids: string[]): Promise<string[]> => {
  const services = await fetchServicesByIds(ids);
  return services.map(service => service.name);
};
