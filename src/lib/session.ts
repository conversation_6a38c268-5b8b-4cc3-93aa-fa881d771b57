/**
 * Session management utilities
 * Created: 2025-03-20
 * This file contains functions for managing user sessions, including automatic logout
 */

import { getToken, removeToken, verifyToken } from './jwt';

// Session duration in milliseconds (1 hour)
export const SESSION_DURATION = 60 * 60 * 1000; // 1 hour

// Last activity timestamp key
const LAST_ACTIVITY_KEY = 'homefoodi_last_activity';

/**
 * Initialize session tracking
 * Sets up event listeners and interval checks for session expiration
 */
export const initializeSession = (): void => {
  // Set initial last activity timestamp
  updateLastActivity();
  
  // Set up activity listeners
  window.addEventListener('click', updateLastActivity);
  window.addEventListener('keypress', updateLastActivity);
  window.addEventListener('scroll', updateLastActivity);
  window.addEventListener('mousemove', updateLastActivity);
  
  // Check session expiration every minute
  setInterval(checkSessionExpiration, 60 * 1000); // Check every minute
  
  // Also check when the tab becomes visible again
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
      checkSessionExpiration();
    }
  });
};

/**
 * Update the last activity timestamp
 */
export const updateLastActivity = (): void => {
  localStorage.setItem(LAST_ACTIVITY_KEY, Date.now().toString());
};

/**
 * Check if the session has expired
 * If expired, logs the user out
 */
export const checkSessionExpiration = (): void => {
  const token = getToken();
  
  // If no token, user is not logged in
  if (!token) return;
  
  // Check if token is valid
  const payload = verifyToken(token);
  if (!payload) {
    logout();
    return;
  }
  
  // Check last activity
  const lastActivityStr = localStorage.getItem(LAST_ACTIVITY_KEY);
  if (!lastActivityStr) {
    // No last activity recorded, update it now
    updateLastActivity();
    return;
  }
  
  const lastActivity = parseInt(lastActivityStr, 10);
  const currentTime = Date.now();
  
  // If inactive for more than the session duration, log out
  if (currentTime - lastActivity > SESSION_DURATION) {
    logout();
    
    // Show session expired message
    alert('Your session has expired. Please log in again.');
    
    // Redirect to login page
    window.location.href = '/signup';
  }
};

/**
 * Log the user out
 * Clears all authentication data and redirects to login
 */
export const logout = (): void => {
  // Remove token
  removeToken();
  
  // Clear user data from localStorage
  localStorage.removeItem('homefoodi_user');
  localStorage.removeItem(LAST_ACTIVITY_KEY);
  
  // Any other cleanup needed
};
