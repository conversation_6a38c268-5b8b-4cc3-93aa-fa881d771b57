/**
 * useVendors Hook
 * 
 * This hook provides functionality to fetch and manage vendor data
 * based on the vendor type stored in localStorage and user coordinates.
 * 
 * Updated to:
 * 1. Properly handle vendor type changes when switching between tabs
 * 2. Send user coordinates with API requests to match with vendor coordinates
 */

import { useState, useEffect } from 'react';
import { fetchVendors, fetchVendorMenu, BaseVendor } from '@/services/vendorService';
import { getCurrentVendorType, getCurrentVendorTypeDisplay } from '@/utils/vendorTypeUtils';

interface UseVendorsOptions {
  initialFilters?: Record<string, any>;
  autoFetch?: boolean;
  location?: string;
}

interface UseVendorsReturn {
  vendors: BaseVendor[];
  loading: boolean;
  error: Error | null;
  refetch: (filters?: Record<string, any>) => Promise<void>;
  currentVendorType: string;
  currentVendorTypeDisplay: string;
  totalVendors: number;
}

/**
 * Hook for fetching and managing vendor data
 * @param options - Configuration options
 * @returns Vendor data and utility functions
 */
export const useVendors = (options: UseVendorsOptions = {}): UseVendorsReturn => {
  const { initialFilters = {}, autoFetch = true, location = "" } = options;
  
  const [vendors, setVendors] = useState<BaseVendor[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [currentFilters, setCurrentFilters] = useState<Record<string, any>>(initialFilters);
  
  // Track vendor type as state so it can trigger re-renders
  const [currentVendorType, setCurrentVendorType] = useState<string>(getCurrentVendorType());
  const [currentVendorTypeDisplay, setCurrentVendorTypeDisplay] = useState<string>(getCurrentVendorTypeDisplay());
  
  /**
   * Fetch vendors with optional filters and user coordinates
   */
  const fetchVendorsData = async (filters?: Record<string, any>) => {
    try {
      setLoading(true);
      setError(null);
      
      // Check for user coordinates in localStorage
      let userCoordinates = null;
      try {
        const userCoordinatesStr = localStorage.getItem('userCoordinates');
        if (userCoordinatesStr) {
          userCoordinates = JSON.parse(userCoordinatesStr);
          console.log('User coordinates found in localStorage:', userCoordinates);
        } else {
          console.log('No user coordinates found in localStorage');
        }
      } catch (coordError) {
        console.error('Error parsing user coordinates:', coordError);
      }
      
      // Use provided filters or current filters
      const filtersToUse = filters || currentFilters;
      if (filters) {
        setCurrentFilters(filters);
      }
      
      // For this implementation, we're only sending vendor_type and coordinates
      // No additional filters should be applied unless explicitly requested
      console.log('Fetching vendors with vendor type:', currentVendorType);
      if (userCoordinates) {
        console.log('Including user coordinates in request:', userCoordinates);
      }
      
      const data = await fetchVendors({...filtersToUse, location: location});
      console.log('Fetched vendors:', data.length);
      setVendors(data);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('An unknown error occurred'));
      console.error('Error in useVendors hook:', err);
    } finally {
      setLoading(false);
    }
  };
  
  // Listen for vendor type changes
  useEffect(() => {
    // Function to check and update vendor type from localStorage
    const checkAndUpdateVendorType = () => {
      const storedVendorType = localStorage.getItem('vendor_type') || 'home-chef';
      if (storedVendorType !== currentVendorType) {
        console.log('Vendor type changed from', currentVendorType, 'to', storedVendorType);
        setCurrentVendorType(storedVendorType);
        setCurrentVendorTypeDisplay(getCurrentVendorTypeDisplay());
      }
    };
    
    // Listen for custom vendorTypeChanged event
    const handleVendorTypeChanged = () => {
      console.log('Vendor type change event detected');
      checkAndUpdateVendorType();
    };
    
    // Check immediately
    checkAndUpdateVendorType();
    
    // Set up event listeners
    window.addEventListener('vendorTypeChanged', handleVendorTypeChanged);
    
    // Also poll periodically as a fallback
    const checkVendorTypeInterval = setInterval(checkAndUpdateVendorType, 300);
    
    return () => {
      clearInterval(checkVendorTypeInterval);
      window.removeEventListener('vendorTypeChanged', handleVendorTypeChanged);
    };
  }, [currentVendorType]);

  // Fetch vendors when vendor type changes
  useEffect(() => {
    if (autoFetch) {
      console.log('Fetching vendors for vendor type:', currentVendorType);
      // Force a fresh fetch from Supabase
      fetchVendorsData();
    }
    
    // Listen for vendor type changes in localStorage from other tabs
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'vendor_type' && autoFetch) {
        const newVendorType = e.newValue || 'home-chef';
        setCurrentVendorType(newVendorType);
        setCurrentVendorTypeDisplay(getCurrentVendorTypeDisplay());
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [currentVendorType, autoFetch]);
  
  // Force a re-fetch when filters change
  useEffect(() => {
    if (autoFetch) {
      fetchVendorsData(currentFilters);
    }
  }, [currentFilters, autoFetch]);
  
  return {
    vendors,
    loading,
    error,
    refetch: fetchVendorsData,
    currentVendorType,
    currentVendorTypeDisplay,
    totalVendors: vendors.length,
  };
};

/**
 * Hook for fetching and managing vendor menu data
 * @param vendorId - ID of the vendor
 * @returns Menu data and loading state
 */
export const useVendorMenu = (vendorId: string) => {
  const [menuData, setMenuData] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  
  const fetchMenuData = async () => {
    if (!vendorId) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const data = await fetchVendorMenu(vendorId);
      setMenuData(data);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('An unknown error occurred'));
      console.error('Error in useVendorMenu hook:', err);
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    fetchMenuData();
    
    // Listen for vendor type changes in localStorage
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'vendor_type') {
        fetchMenuData();
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [vendorId]);
  
  return {
    menuData,
    loading,
    error,
    refetch: fetchMenuData,
  };
};
