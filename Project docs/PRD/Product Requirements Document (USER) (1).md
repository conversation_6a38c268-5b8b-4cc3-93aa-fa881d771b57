## Product Requirements Document (PRD) for HomeFoodi Project

### 1. Introduction

#### Overview
HomeFoodi is a local search engine providing information about three culinary services. It is a dynamic web-based platform designed to connect food enthusiasts with three types of culinary experts including home chefs, tiffin suppliers, and catering services. By offering a user-friendly interface and comprehensive functionality, HomeFoodi aims to simplify the process of finding and contacting culinary services, making it easy for users to locate a variety of home-cooked meals, tiffin services, and catering services at their convenience.

#### User Roles and Terminologies
- **Vendors** - Culinary service providers that is Home Chefs, Tiffin Service, and Caterers.
- **Users** - Who will reach out/search/Get in touch with the Vendors.
- **Admin Panel** - The administrator who will manage the platform.

#### Objectives and Key Goals
The primary objectives of the HomeFoodi website are:
- **User Engagement:** To create an engaging and seamless experience for users seeking culinary services.
- **Service Diversity:** To cater to a wide range of culinary preferences and needs by offering services from home chefs, tiffin suppliers, and caterers.
- **Ease of Access:** To provide a straightforward platform where users can easily browse, filter, and interact with service listings.
- **Vendor Support:** To empower vendors with tools for managing their services and get leads for their business.
- **Reliability and Performance:** To ensure the website is reliable and performs well across all user and admin functions.

### 2. Product Scope

#### Key Features

---
# Module: **Home Page** #
---

#### **Functional Requirements**
1. **Location Detection**
   - The system should request the user's location using the browser’s default location detector.
   - If the user grants permission, the detected location must be saved and displayed in the location dropdown.
   - If permission is denied, the system should fetch the user's approximate location using their IP address.
   
2. **Navigation Options**
   - Provide navigation buttons for:
     - **Home Chefs**: Redirects users to the Home Chefs section.
     - **Tiffin Suppliers**: Redirects users to the Tiffin Suppliers section.
     - **Caterer**: Redirects users to the Caterers section.
   - Ensure these buttons are present both on the hero banner and within the respective sections of the page.

3. **Call-to-Action Buttons**
   - "View More" buttons must redirect users to the respective service's listing pages:
     - Home Chefs: Listing of Home Chefs services.
     - Tiffin Suppliers: Listing of Tiffin Suppliers services.
     - Caterer: Listing of Catering services.

4. **Vendor Registration**
   - Include a "Join Us" button for vendors to register as Home Chefs, Tiffin Suppliers, or Caterers.
   - Redirects to a vendor registration page. 

5. **Footer Links**
   - Include the following sections in the footer:
     - Quick Links: About Us (Static Page - Managed through Admin Panel), Join Us (Vendor Registration Page), Blog (Static Blog - Managed through Admin Panel), Privacy (Static Page - Managed through Admin Panel), Terms and Conditions (Static Page - Managed through Admin Panel).
     - Support: Contact (A pop-up), FAQ (Static Page - Managed through Admin) Panel, Feedback (Default Device Emailer).
     - Social Media Icons: Facebook, Instagram, YouTube, LinkedIn, Pinterest, and X.
   - Provide clickable links for all sections.

6. **Hero Section**
   - Display the tagline: “Order Healthy and Delicious Home Food.”
   - Include a dropdown for location (Populated through API) and Vendor selection.

7. **Content Display**
   - Highlight the following services with images and descriptions:
     - **Home Chefs**: "Customized Home Food."
     - **Tiffin Suppliers**: "Weekly & Monthly Meals."
     - **Caterer**: "Party & Bulk Orders."
   - Images must align with the descriptions.

#### **Non-Functional Requirements**
1. **Performance**
   - Location detection and service filtering must occur quickly.
   - Images and buttons must load seamlessly without delay.

2. **Accessibility**
   - Ensure all buttons, dropdowns, and links are accessible via keyboard navigation.
   - Use alt text for all images.

3. **Responsiveness**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18)

4. **Security**
   - Use HTTPS for secure communication.
   - Validate and sanitize all user inputs, especially during location detection and vendor registration.

#### **Validations**
1. **Location Detection**
   - Validate if location data is accurately fetched and displayed in the dropdown.
   - Check if location permissions are either allowed or denied explicitly by the user.

2. **Navigation Buttons**
   - Ensure all buttons redirect users to the correct sections.

3. **Search Bar**
   - Validate that category filters (e.g., Home Chef, Tiffin Supplier) correctly filter services.

#### **Error Messages**
1. **Location Errors**
   - If location detection fails, display: “Unable to detect your location. Please select manually.”

2. **Navigation Errors**
   - If a section fails to load, display: “We’re sorry, this section is currently unavailable. Please try again later.”

3. **General Errors**
   - For unexpected issues, display: “Something went wrong. Please refresh the page or contact support.”

---
# Module: **Service Listing Page** #
---

#### **Functional Requirements**
1. **Service Listing**
   - Display a list of vendors based on proximity to the user’s location (nearest services listed first). And the service delivery offering by the vendors. (Let's say a vendor has opted for 5 kms of delivery range of Noida Sector-63, and the user is searching (from Noida Sector-62) within his range of delivery, the vendor will be listed and presented to the user)
   - The listing of vendors will be based on service type and user's search. 
   - Each listing card must include:
     - Vendor Name.
     - Cuisine Type.
     - Service Area (Address Line 1 and Area).
     - Distance from the user (in kilometers).
     - Vegetarian/Non-Vegetarian Indicator.
     - Available Meals (e.g., Lunch, Dinner).
     - Rating (Stars and number of reviews).

2. **Filters and Sorting Options**
   - Filters must include:
     - Veg/Non-Veg toggle.
     - Cuisines (dropdown for cuisine selection, Multiselect).
     - Services (e.g., Home Chef, Tiffin Supplier, Caterer) - Single select.
     - Rating (Dropdown)
     - Price range (Single Select)
     - Distance (scroller)
   - Users must be able to apply multiple filters simultaneously.
   - Sorting options include:
     - Distance (default).
     - Rating.
     - Price (Low to High, High to Low).

3. **Navigation and Switching**
   - Users can switch between different vendor categories (Home Chefs, Tiffin Suppliers, Caterers) via the header navigation.
   - Clicking on a vendor card redirects the user to the respective Vendor Detail Page.

4. **Action Buttons**
   - Each vendor card must include the following buttons:
     - **Call Now**: Initiates a phone call to the vendor.
     - **Send Inquiry**: An SMS will be sent to the vendor with the user's request including details like User's Phone Number, Service the user is interested in (Home Chef, Tiffin Service or Caterer).
     - To the user, a pop-up will be displayed stating that (Information is sent to the concerned business)
     - **Chat**: Redirects the user to the WhatsApp (either app if the user is using Phone or web.whatsapp if the user is on desktop)
     - **Direction**: Opens default device map application showing directions to the vendor's location. 

5. **Results Counter**
   - Display the total number of results available for the selected service (e.g., “Showing Results for 4.3k Results for your search”).

6. **Footer Links**
   - Include the footer items.

#### **Non-Functional Requirements**
1. **Performance**
   - Service listing and filter application must load immediately.
   - Action buttons (e.g., Call, Send Enquiry, Chat, Direction) should respond fast.

2. **Accessibility**
   - Ensure all filters, buttons, and links are accessible via keyboard navigation.
   - Provide alt text for all images.

3. **Responsiveness**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18)

4. **Scalability**
   - The system must handle a large number of listings without performance degradation.

5. **Security**
   - Ensure secure communication for sensitive actions like initiating a chat or inquiry.
   - Validate and sanitize all user inputs.

#### **Validations**
1. **Listing Data**
   - Ensure vendor details (e.g., name, distance, rating) are accurate and up-to-date.
   - Verify that filters return correct results.

2. **Action Buttons**
   - Validate that the Call Now button triggers device's default phone call or on desktop browser, browser's default pop-up stating the Vendor's phone number will show up.
   - Ensure the Send Inquiry button triggers An SMS will be sent to the vendor with the user's request including details like User's Phone Number, Service the user is interested in (Home Chef, Tiffin Service or Caterer).
     - To the user, a pop-up will be displayed stating that (Information is sent to the concerned business)
   - Verify the Chat button opens a functional WhatsApp interface/App.
   - Check the Direction button redirects to the correct map application with proper coordinates.

3. **Filters**
   - Ensure multiple filters work together without conflict.
   - Verify sorting options apply accurately.

#### **Error Messages**
1. **Listing Errors**
   - If no results are found, display: “No matches, no worries. Tweak your filters and let’s find something awesome!”

2. **Action Errors**
   - If an action button (Call, Chat, Direction) fails, display: “Action couldn’t go through. Give it another shot soon!”

3. **General Errors**
   - For unexpected issues, display: “Something went wrong. Please refresh the page or contact support.”

---
# Module: **Selected Service Listing Page** #
---

#### **Functional Requirements**

1. **Vendor Details Display**
   - Display the following vendor details:
     - Vendor Name.
     - Distance from the user’s location (in kilometers).
     - Cuisine type (e.g., North Indian | Fast Food) 
     - Available Meals (e.g., Breakfast, Lunch, Dinner) and (In case of Caterer, it will be their Service Type ie. House Party, Marriage)
     - Vegetarian/Non-Vegetarian Indicator.
     - **Ratings and Reviews:**
     - Average rating (e.g., 4.9 ★ from 25+ reviews).
     - **Address and Delivery Information:**
   - Full address with landmarks (e.g., A/12 Bhadr Girnar Height 1, Joshipura, Junagadh-362001).
   - Delivery details (e.g., Free delivery up to 5 km).
   - Operating timings (e.g., Mon-Sun, 9:00 AM to 10:30 PM).

2. **Image Display**
   - **Caterer:**
     - Display 10 images in the first fold on both desktop and mobile versions.
     - Clicking on any image will open a scroller to view all 10 images.
   - **Tiffin Service and Home Chefs:**
     - **Mobile:** Display a single image.
     - **Desktop:** Do not display images in the first fold.

4. **Action Buttons**
   - Refer to the "Action Buttons Module" of this PRD for more details.

5. **Sticky Action Bar**
   - The "Call Now", "Enquiry" and "Chat on WhatsApp" buttons must remain sticky at the bottom of the screen as the user scrolls down until they reach the start of the footer.

6. **Tabs for Additional Information:**
   - **Menu:** Displays food options.
   - The menu item will be different for all three service providers.
   - For Home Chef, it will be their cuisines:
     - Dal
     - Seasonal Vegetables, etc.
    -  For Tiffin Supplier it will be:
        - Veg/Non-veg and the starting prices.
    -  For Caterer it will be their cuisines:
         - Raita, Starters, etc.
   - **Review:** Section to read and write reviews. (Refer to the Review Module of this PRD for more details.)
   - **Preferences:** User preferences for food customization.
   - **Payment:** Payment modes and options for all three service Providers they accept.
   - **About Us:** Kitchen history and highlights for all three vendors. Shared by the Vendors or managed by the Admin. 
   - **Service:** The services the vendors provide.

7. **Navigation Links**
   - Header navigation allows users to return to the selected service listing page (Home Chefs, Tiffin Suppliers, Caterers).

#### **Non-Functional Requirements**

1. **Performance**
   - Images and data must load immediately.
   - Sticky action buttons must respond fast upon interaction.

2. **Accessibility**
   - Ensure all tabs, buttons, and links are accessible via keyboard navigation.
   - Use alt text for all images.

3. **Responsiveness**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18) 

4. **Scalability**
   - The system must support a large number of vendor details without performance issues.

5. **Security**
   - Secure communication for sensitive actions like chat and inquiries.
   - Validate and sanitize all user inputs.

#### **Validations**

1. **Vendor Data**
   - Ensure vendor details (e.g., name, distance, menu, pricing) are accurate and updated.
   - Verify that meal categories and prices match the vendor’s input.

2. **Action Buttons**
   - Refer to the "Action Button Module of this PRD".

3. **Tabs Functionality**
   - Ensure all tabs display the correct information.
   - Validate that switching between tabs occurs without delay.

#### **Error Messages**

1. **Image Loading Errors**
   - If images fail to load, display: “Oops, images didn’t load. Refresh and try again!”

2. **Action Button Errors**
   - If an action button (Call, Chat, etc.) fails, display: “Action couldn’t go through. Give it another shot soon!”

3. **General Errors**
   - For unexpected issues, display: “Something went wrong. Please refresh the page or contact support.”


---
# Module: **Filters Module** #
---

### Functional Requirements:

1. **Filters as Pop-Up**
   - The filters will be displayed as a pop-up overlaying the Service Listing page.
   - Users can apply filters to narrow their search or clear them to reset the listing.

2. **Filter Categories**
- The entire filter will be managed by the admin and will be different for vendors based on their selection during their registration process.
   - **Veg | Non-Veg:**
     - Users can select either "Veg" or "Non-Veg" options.
     - Selection states should be visually distinct (e.g., "Veg" in green and "Non-Veg" in red).
   - **Cuisines:**
     - Multiple cuisine options, such as Andhra, Awadhi, Bengali, Chinese, Desserts, etc., will be available. 
     - Users can select multiple cuisine at a time.
   - **Services:**
     - Services listed by the admin.
     - No predefined selectable options in the design.
   - **Rating:**
     - Users can choose rating thresholds: Any, 3.5+, 4+, or 4.5+.
   - **Price:**
     - Two sorting options: Price-Low to High and Price-High to Low. This will be filtered based on the vendor's starting price of their cuisines.
   - **By Distance:**
     - Slider allowing users to select a range for service providers within a specific distance 
     - - Slider control:
       - Range: < 2 kms, 2 to 5 kms, 5 kms - 10 kms, and 10 kms - 50 kms.
       - Default: <2 km.
       - Adjustable with a visual indicator.

3. **Clear All Option**
   - Users can reset all applied filters to default values with a single click.

4. **Apply Button**
   - An "Apply" button will execute the selected filters and update the Service Listing page accordingly.

5. **Default Behavior**
   - If filters are cleared, the service listings will be sorted based on the user’s location and nearest service providers.

6. **Filter Navigation**
   - Each filter category can be accessed via a vertical tab menu on the left side of the pop-up.

---

### Non-Functional Requirements:

1. **Performance:**
   - Filters should update the Service Listing page fast after the "Apply" button is clicked.

2. **Responsiveness:**
   - Filters pop-up should be optimized for the defined screens.
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18) 

3. **Visual Feedback:**
   - Selected options must provide clear visual feedback to indicate active filters (e.g., color coding for Veg/Non-Veg, bolding, or highlighting selected cuisines). Follow the design.

4. **Accessibility:**
   - The filter interface must comply with accessibility standards (e.g., keyboard navigability, screen reader support).

5. **Error Handling:**
   - The interface should handle cases of invalid or missing data gracefully (e.g., a cuisine filter selected but no services available for that cuisine).

---

### Validations:

1. **Veg | Non-Veg:**
   - Only one of the two options can be selected at any given time.

2. **Cuisines:**
   - Allow selection of multiple cuisine at a time.

3. **Rating:**
   - Validate that only one rating filter (e.g., 3.5+, 4+, or 4.5+) is active at any time.

4. **Price:**
   - Ensure that only one price sorting option (Low to High or High to Low) can be selected.

5. **Distance Slider:**
   - Validate slider input to ensure it falls within the allowed range.

6. **Apply Button:**
   - Ensure at least one filter is selected before enabling the "Apply" button (based on design).

---

### Error Messages:

1. **No Results Found:**
   - "No matches found for your filters. Try tweaking them to discover more options!"

2. **Network Issues:**
   - "Network issue. Check your connection and try again."

3. **Invalid Input:**
   - "Invalid filter option. Refresh and retry."

4. **Empty Distance Slider:**
   - "Set a distance range to continue."

---

---
# Module: **Action Buttons** #
---

### Functional Requirements:

1. **Action Buttons**
   - Only logged in users will have the accessibility to the buttons.
   - If the user is unregistered, they will be asked for the phone number and auth module will come in action.
   - Four primary buttons will be available for user interaction:
     - **Call Now**
     - **Chat (WhatsApp)**
     - **Enquiry**
     - **Direction**

2. **Availability**
   - The buttons will be displayed on:
     - **Services Listing Page:** Each vendor/service card will have these buttons.
     - **Vendor Detail Page:** Buttons will remain visible for the selected vendor.

3. **Sticky Action Bar**
   - When users scroll down the page, the "Call Now," "Chat," and "Enquiry" buttons will stick to the bottom of the screen until the user reaches the footer section.

4. **Button Functionalities**
   - **Call Now:**
     - Verify that a valid phone number of the vendor is linked.
     - **Phone Usage:** Opens the device’s dialer app with the vendor’s phone number pre-filled.
     - **Desktop Usage:** Opens the browser’s default dialer (e.g., `tel:+91xxxx`).
   - **Send Enquiry:**
     - On click, an SMS will be sent to the vendor containing:
       1. User’s phone number.
       2. The service the user is interested in.
     - A success pop-up will confirm: “Thank you! Your enquiry was sent successfully.”
   - **Chat (WhatsApp):**
     - **Phone Usage:** Opens the WhatsApp app with the vendor’s chat pre-filled.
     - **Desktop Usage:** Opens a new browser tab for WhatsApp Web with the vendor’s chat pre-filled.
   - **Direction:**
     - Opens the default map application (e.g., Google Maps) with the vendor’s address or coordinates preloaded.

---

### Non-Functional Requirements:

1. **Performance:**
   - Buttons should execute their actions promptly. 

2. **Responsiveness:**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18)

3. **Visual Design:**
   - Buttons must have clear, distinct colors and icons:
     - **Call Now:** Orange with a phone icon.
     - **Chat:** Green with a WhatsApp icon.
     - **Enquiry:** Black with a chat bubble icon.
     - **Direction:** Blue with a location pin icon.

4. **Accessibility:**
   - Buttons must include descriptive alt text for screen readers (e.g., "Call Now button to contact the vendor").

5. **Security:**
   - Ensure no unauthorized actions can be triggered via button clicks (e.g., spamming SMS or WhatsApp links).

---

### Validations:

1. **Call Now:**
   - Ensure the phone number follows the correct format and is clickable.

2. **Send Enquiry:**
   - Validate that the user’s phone number is included in the SMS payload.
   - Check that the service details are correctly fetched and sent.

3. **Chat (WhatsApp):**
   - Confirm that the vendor’s WhatsApp number is valid and pre-filled in the chat.

4. **Direction:**
   - Validate that the correct address or geolocation data is preloaded into the map application.

---

### Error Messages:

1. **Call Now:**
   - "Can’t fetch the number right now. Try again soon!"

2. **Send Enquiry:**
   - "Your enquiry didn’t go through. Check your connection and give it another shot!"

3. **Chat (WhatsApp):**
   - "WhatsApp’s not playing nice. Make sure it’s installed and try again!"

4. **Direction:**
   - "Can’t find the location. Check your GPS and let’s try again!"

---
---
# Module: **Reviews** #
---

### Functional Requirements:

1. **Review Submission**
   - Users will be able to submit reviews for vendors.
   - A text box will be provided to input comments (maximum 300 characters).
   - Users will also assign a star rating (1 to 5 stars).

2. **Review Display**
   - Reviews will be visible on the vendor detail page.
   - Each review will display:
     - User’s name.
     - Date and time of review submission.
     - Star rating.
     - Review content.

3. **Flagging System**
   - Vendors will have the ability to flag inappropriate reviews or ratings.
   - Flagged reviews will be sent to the admin panel for further action (delete or reject).

4. **Pagination**
   - Reviews will be displayed in a paginated format.
   - A maximum of 10 reviews will be shown per page.
   - Users can navigate to other pages using pagination controls.

5. **Overall Rating Display**
   - An aggregate rating (e.g., 4.6) will be shown based on all reviews.
   - The aggregate rating will be updated dynamically as new reviews are added.

---

### Non-Functional Requirements:

1. **Performance:**
   - Loading reviews and submitting feedback should be fast.

2. **Responsiveness:**
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18)

3. **Accessibility:**
   - All review elements must be accessible via keyboard navigation and screen readers.

4. **Data Integrity:**
   - Ensure star ratings and comments are saved correctly to the database.
   - Prevent duplicate reviews from being submitted by the same user for the same vendor.

5. **Moderation:**
   - Flagged reviews must be queued for admin review immediately.

---

### Validations:

1. **Review Submission:**
   - Ensure the text input does not exceed 300 characters.
   - Validate star ratings are between 1 and 5.
   - Prevent submission if both comment and star rating are empty.

2. **Pagination:**
   - Verify correct reviews are loaded for the respective page number.

3. **Flagging:**
   - Ensure flagged reviews are marked appropriately in the system and sent to the admin.

---

### Error Messages:

1. **Review Submission:**
   - "Your review could not be submitted. Please try again."

2. **Flagging Review:**
   - "Unable to flag this review. Please check your connection and try again."

3. **Pagination:**
   - "Unable to load reviews. Please refresh the page."

4. **Data Fetching:**
   - "No reviews available for this vendor. Be the first to leave a review!"

---
---
# Module: **Auth Module** #
---

### Functional Requirements:

1. **Phone Number Input**
   - Users will be able to enter their phone number for both login and sign-up purposes.

2. **OTP Authentication**
   - Users will receive a One-Time Password (OTP) on their entered phone number.
   - OTP will be required to verify the user's identity for both login and sign-up.

3. **Sign-Up Flow**
   - New users will be prompted to provide their name after OTP verification.
   - A default placeholder profile image will be applied for every new profile created.

4. **Login Flow**
   - Existing users can log in using their phone number and OTP.
   - Only Logged-in users will have access to action buttons feature.

5. **Logout Option**
   - Logged-in users will have an option to log out from their account.

6. **Phone Number in Browser Forms**
   - Users can save their phone number in their browser for convenience during login.
   - Even with a saved phone number, OTP authentication will be required for each login attempt.

---

### Non-Functional Requirements:

1. **Performance:**
   - The OTP should be sent to the user right after entering the phone number.

2. **Security:**
   - OTPs should expire within 5 minutes to enhance security.
   - Ensure that phone numbers and OTPs are encrypted during transmission and storage.

3. **User Experience:**
   - Provide clear error messages and success notifications during the authentication process.
   - The page layout should immediately adapt to varying screen sizes, including desktop, tablet, and mobile devices. 
   - **Chrome-** For Windows, macOS, and Android the supported version is Chrome v132, and For iOS & iPadOS the supported version is Chrome v133.
   - **Mozilla-** For Windows (v134.0.2 (ARM64)), macOS (v134.0.2), Android (v134.0.2 (x64))
   - **Safari-** For macOS (v18.1.1), and iOS & iPadOS (v17 and v18)

4. **Scalability:**
   - The system should handle high volumes of concurrent OTP requests during peak times.

5. **Accessibility:**
   - The form should be compatible with screen readers and allow for keyboard navigation.

---

### Validations:

1. **Phone Number Validation:**
   - Ensure the entered phone number follows a valid format that is 10 digits (Indian country code +91 number shall be shown on the screen).
   - Don't proceed with invalid formats or missing digits.

2. **OTP Validation:**
   - Ensure the entered OTP is a 6-digit numeric value.
   - Deny access for incorrect OTPs with an error message.

3. **Name Validation (Sign-Up):**
   - Check that the name field is not empty and does not exceed 50 characters.

4. **Session Management:**
   - Prevent unauthorized access by ensuring users must verify OTP every time, even if the phone number is saved in the browser.

---

### Error Messages:

1. **Phone Number Input:**
   - "Please enter a valid phone number."

2. **OTP Issues:**
   - "Invalid OTP. Please try again."
   - "OTP expired. Please request a new OTP."

3. **Name Field (Sign-Up):**
   - "Name cannot be empty."

4. **Network Issues:**
   - "Unable to connect. Please check your network and try again."

5. **System Error:**
   - "An unexpected error occurred. Please try again later."

---

### Notifications:

1. **OTP Sent:**
   - "OTP sent to your phone number. Please check your messages."

2. **Login Success:**
   - "Welcome back! You have successfully logged in."

3. **Sign-Up Success:**
   - "Account created successfully! Welcome to HomeFoodi."

4. **Logout Success:**
   - "You have successfully logged out."

Note: All the specified non-functional requirements and time-sensitive features are subject to third-party and server response dependencies.
---
End of the document
---




