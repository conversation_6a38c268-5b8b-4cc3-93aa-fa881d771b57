
/**
 * AboutUsTabContent Component
 * 
 * This component displays the vendor's about_us information from the vendors table.
 * It uses the vendorService to retrieve the vendor's about_us content.
 * Updated to fetch vendor's about_us information directly from the vendors table.
 */

import React, { useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import { supabase } from "@/lib/supabase";

interface AboutUsTabContentProps {
  className?: string;
  vendorId?: string;
}

const AboutUsTabContent = ({ className, vendorId }: AboutUsTabContentProps) => {
  const [aboutContent, setAboutContent] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const getVendorAboutUs = async () => {
      if (!vendorId) {
        setError('Vendor ID is required');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        // Fetch the vendor's about_us information from the vendors table
        const { data, error } = await supabase
          .from('vendors')
          .select('about_us')
          .eq('id', vendorId)
          .single();

        if (error) {
          console.error('Error fetching vendor about_us:', error);
          setError('Failed to load About Us content');
          return;
        }

        if (data && data.about_us) {
          setAboutContent(data.about_us);
        } else {
          // No about_us content found for this vendor
          setAboutContent(null);
        }
      } catch (err) {
        console.error('Error fetching vendor about_us:', err);
        setError('Failed to load About Us content');
      } finally {
        setLoading(false);
      }
    };

    getVendorAboutUs();
  }, [vendorId]);

  return (
    <div className={cn("mb-20 mt-[30px] border-t border-[#E6E6E6]", className)}>
      {loading ? (
        <div className="flex justify-center items-center py-10">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : error ? (
        <div className="text-red-500 py-6">{error}</div>
      ) : aboutContent ? (
        <div>
          <h2 className="text-xl font-bold text-[#2C2F24] mb-6 mt-[30px]">About Us</h2>
          <div 
            className="text-[#9B9B9B] text-base"
            dangerouslySetInnerHTML={{ __html: aboutContent }}
          />
        </div>
      ) : (
        <div className="py-6 text-[#9B9B9B]">
          <p>No about us information available for this vendor.</p>
        </div>
      )}
    </div>
  );
};

export default AboutUsTabContent;
