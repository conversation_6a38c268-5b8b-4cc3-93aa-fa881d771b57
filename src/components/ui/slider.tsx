import * as React from "react";
import * as SliderPrimitive from "@radix-ui/react-slider";
import { cn } from "@/lib/utils";

const Slider = React.forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>
>(({ className, ...props }, ref) => {
  const [value, setValue] = React.useState(props.value || props.defaultValue || [0]);
  const sliderRef = React.useRef<HTMLDivElement>(null);
  const max = props.max || 100;
  const percentage = (value[0] / max) * 100;

  // Use a ref to avoid re-creating handlers on every render
  const valueRef = React.useRef(value);
  valueRef.current = value;

  // Sync with external value changes
  React.useEffect(() => {
    if (props.value && JSON.stringify(props.value) !== JSON.stringify(value)) {
      setValue(props.value);
    }
  }, [props.value]);

  const handleValueChange = (newValue: number[]) => {
    setValue(newValue);
    props.onValueChange?.(newValue);
  };

  // Smooth drag handler
  const handlePinInteraction = (e: React.MouseEvent | React.TouchEvent) => {
    if (!sliderRef.current) return;
    const slider = sliderRef.current;
    const rect = slider.getBoundingClientRect();

    const getClientX = (
      event: React.MouseEvent | React.TouchEvent | MouseEvent | TouchEvent
    ): number => {
      if ("touches" in event) return event.touches[0].clientX;
      return (event as MouseEvent).clientX;
    };

    const updateValue = (clientX: number) => {
      const offsetX = clientX - rect.left;
      const pct = Math.min(Math.max(offsetX / rect.width, 0), 1);
      const newValue = Math.round(pct * max);
      setValue([newValue]);
      props.onValueChange?.([newValue]);
    };

    // Initial update
    updateValue(getClientX(e));

    // Use animation frame for smoothness
    let rafId: number | null = null;
    const isTouch = "touches" in e;

    const handleMove = (event: MouseEvent | TouchEvent) => {
      event.preventDefault();
      if (rafId) cancelAnimationFrame(rafId);
      rafId = requestAnimationFrame(() => {
        updateValue(getClientX(event));
      });
    };

    const handleEnd = () => {
      if (isTouch) {
        document.removeEventListener("touchmove", handleMove);
        document.removeEventListener("touchend", handleEnd);
      } else {
        document.removeEventListener("mousemove", handleMove);
        document.removeEventListener("mouseup", handleEnd);
      }
      if (rafId) cancelAnimationFrame(rafId);
    };

    if (isTouch) {
      document.addEventListener("touchmove", handleMove, { passive: false });
      document.addEventListener("touchend", handleEnd);
    } else {
      document.addEventListener("mousemove", handleMove);
      document.addEventListener("mouseup", handleEnd);
    }
  };

  return (
    <div
      className="relative touch-none select-none"
      ref={sliderRef}
      style={{ WebkitTapHighlightColor: "transparent" }}
    >
      <SliderPrimitive.Root
        ref={ref}
        className={cn(
          "relative flex w-full touch-none select-none items-center",
          className
        )}
        {...props}
        onValueChange={handleValueChange}
        value={[value[0]]}
      >
        <SliderPrimitive.Track className="relative h-2 w-full grow overflow-hidden rounded-full bg-[#E5E5E5]">
          <SliderPrimitive.Range className="absolute h-full bg-[#E87616]" />
        </SliderPrimitive.Track>
        {/* Use a visible thumb for accessibility */}
        <SliderPrimitive.Thumb
          className="block h-8 w-8 rounded-full bg-transparent focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
          style={{ pointerEvents: "none" }} // Hide default thumb interaction
        />
      </SliderPrimitive.Root>

      {/* Custom Pin */}
      <div
        className="absolute top-0 z-10 cursor-pointer select-none transition-transform duration-150 ease-in-out will-change-transform"
        style={{
          left: `${percentage}%`,
          transform: "translate(-16px, -25px)",
          touchAction: "none",
        }}
        onMouseDown={handlePinInteraction}
        onTouchStart={handlePinInteraction}
        tabIndex={0}
        role="slider"
        aria-valuenow={value[0]}
        aria-valuemin={0}
        aria-valuemax={max}
        aria-label="Slider"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="33" height="33" viewBox="0 0 33 33" fill="none">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M16.499 30.2495C21.1396 30.2495 28.874 21.7219 28.874 14.9717C28.874 8.22159 23.3335 2.74951 16.499 2.74951C9.6645 2.74951 4.12402 8.22159 4.12402 14.9717C4.12402 21.7219 11.8584 30.2495 16.499 30.2495ZM16.499 19.2495C18.7772 19.2495 20.624 17.4027 20.624 15.1245C20.624 12.8463 18.7772 10.9995 16.499 10.9995C14.2208 10.9995 12.374 12.8463 12.374 15.1245C12.374 17.4027 14.2208 19.2495 16.499 19.2495Z"
            fill="#E87616"
          />
        </svg>
      </div>
    </div>
  );
});

Slider.displayName = SliderPrimitive.Root.displayName;

export { Slider };
