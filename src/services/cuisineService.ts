/**
 * Cuisine Service
 * 
 * This file contains functions for fetching cuisine data from the database.
 * It provides methods to get all cuisines and find cuisines by ID.
 */

import { supabase } from '@/lib/supabase';

export interface Cuisine {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

/**
 * Fetch all cuisines from the database
 * @returns Promise with cuisine data
 */
export const fetchAllCuisines = async (): Promise<Cuisine[]> => {
  try {
    const { data, error } = await supabase
      .from('cuisines')
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching cuisines:', error);
      return [];
    }

    return data || [];
  } catch (err) {
    console.error('Exception in fetchAllCuisines:', err);
    return [];
  }
};

/**
 * Fetch cuisines by IDs
 * @param ids - Array of cuisine IDs
 * @returns Promise with cuisine data
 */
export const fetchCuisinesByIds = async (ids: string[]): Promise<Cuisine[]> => {
  if (!ids.length) return [];
  
  try {
    const { data, error } = await supabase
      .from('cuisines')
      .select('*')
      .in('id', ids)
      .order('name');

    if (error) {
      console.error('Error fetching cuisines by IDs:', error);
      return [];
    }

    return data || [];
  } catch (err) {
    console.error('Exception in fetchCuisinesByIds:', err);
    return [];
  }
};

/**
 * Get cuisine names by IDs
 * @param ids - Array of cuisine IDs
 * @returns Promise with array of cuisine names
 */
export const getCuisineNamesByIds = async (ids: string[]): Promise<string[]> => {
  const cuisines = await fetchCuisinesByIds(ids);
  return cuisines.map(cuisine => cuisine.name);
};
