/**
 * Footer Component
 * 
 * Updated to link to content pages from the database, including About Us, Privacy Policy, and Terms & Conditions.
 * When clicked, these links will navigate to the ContentPage component which fetches and displays the content.
 * 
 * The Feedback link now fetches the feedback email address from the database and opens the default email client.
 */

import { images } from "@/assets/images/placeholder";
import { useState } from "react";
import EnquiryForm from "@/components/ui/enquiry-form";
import { Link } from "react-router-dom";
import { openFeedbackEmail } from "@/services/feedbackService";

interface FooterLinkProps {
  children: React.ReactNode;
}

const FooterLink = ({ children }: FooterLinkProps) => {
  return <div className="text-[#323232] cursor-pointer mb-2.5">{children}</div>;
};

interface FooterColumnProps {
  title: string;
  children: React.ReactNode;
}

const FooterColumn = ({ title, children }: FooterColumnProps) => {
  return (
    <div>
      <div className="text-[#323232] text-xl font-bold mb-5">{title}</div>
      {children}
    </div>
  );
};

// Contact button that opens the enquiry form dialog
const ContactWithEnquiryForm = () => {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <>
      <span className="cursor-pointer" onClick={() => setIsOpen(true)}>Contact Us</span>
      <EnquiryForm isOpen={isOpen} onOpenChange={setIsOpen} />
    </>
  );
};

export const Footer = () => {
  return (
    <footer>
      {/* Main Footer Section */}
      <div className="bg-[#F8F8FA] pt-[60px]">
        <div className="max-w-[1120px] flex justify-between mx-auto max-md:flex-col max-md:gap-10 max-md:px-5">
          <div className="max-w-[330px]">
            <img
              src={images.logo}
              alt="Logo"
              className="w-[161px] h-[37px] mb-5"
            />
            <div className="text-[#323232] leading-6">
              Order delicious meals from the comfort of your home with homefoodi.
              Enjoy convenient online ordering and on time home food delivery.
            </div>
          </div>

          <div className="flex gap-[60px] max-md:flex-wrap max-sm:gap-[30px]">
            <FooterColumn title="Quick Link">
              <FooterLink>
                <Link to="/page/about">About Us</Link>
              </FooterLink>
              <FooterLink>
                <a href="https://homefoodi-vendor.uatsparxit.xyz/" target="_blank" rel="noopener noreferrer">Join Us</a>
              </FooterLink>
              
              <FooterLink>
                <Link to="/page/privacy">Privacy Policy</Link>
              </FooterLink>
              <FooterLink>
                <Link to="/page/terms">Term and conditions</Link>
              </FooterLink>
            </FooterColumn>

            <FooterColumn title="Support">
              <FooterLink>
                <ContactWithEnquiryForm />
              </FooterLink>
              {/* <FooterLink>FAQ</FooterLink> */}
              <FooterLink>
                <span className="cursor-pointer" onClick={openFeedbackEmail}>Feedback</span>
              </FooterLink>
            </FooterColumn>

             
            <FooterColumn title="Connect with us">
            <div className="text-[#323232] mb-2.5">
                <a href="mailto:<EMAIL>" className="hover:underline"><EMAIL></a>
              </div>
              <div className="text-[#323232] mb-2.5">
                <a href="tel:+918860357937" className="hover:underline">+91-8860357937</a> | <a href="tel:+917669237937" className="hover:underline">7669237937</a>
              </div>
              <div className="flex gap-2.5 mt-5 mb-2.5">
                <a href="https://www.facebook.com/homefoodiapp" target="_blank" rel="noopener noreferrer">
                  <img src={images.facebook} alt="Facebook" className="w-7 h-7" />
                </a>
                <a href="https://x.com/homefoodi" target="_blank" rel="noopener noreferrer">
                  <img src={images.twitter} alt="Twitter" className="w-7 h-7" />
                </a>
                <a href="https://www.instagram.com/home_foodi/" target="_blank" rel="noopener noreferrer">
                  <img src={images.instagram} alt="Instagram" className="w-7 h-7" />
                </a>
                <a href="https://www.linkedin.com/company/homefoodi/" target="_blank" rel="noopener noreferrer">
                  <img src={images.linkedin} alt="LinkedIn" className="w-7 h-7" />
                </a>
                <a href="https://www.youtube.com/homefoodiapp" target="_blank" rel="noopener noreferrer">
                  <img src={images.youtube} alt="YouTube" className="w-7 h-7" />
                </a>
                <a href="https://in.pinterest.com/homefoodiapp/" target="_blank" rel="noopener noreferrer">
                  <img src={images.pinterest} alt="Pinterest" className="w-7 h-7" />
                </a>
              </div>
            </FooterColumn>
          </div>
        </div>
      </div>

      {/* Simple Footer */}
      <div className="bg-[#E8E8E8] py-[15px] text-center text-sm">
        <div className="max-w-[1120px] mx-auto flex justify-between items-center px-4 max-sm:flex-col max-sm:gap-2">
          <div className="flex gap-8">
            <Link to="/page/privacy" className="text-[#323232] hover:underline">Privacy Policy</Link>
            <Link to="/page/terms" className="text-[#323232] hover:underline">Terms of Services</Link>
          </div>
          <div className="text-[#323232]">
            &copy; 2024 Homefoodi | All rights reserved
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
