
import { cn } from "@/lib/utils";

interface VegNonVegFilterProps {
  selectedOptions: ("Veg" | "Non-Veg")[];
  onChange: (selected: ("Veg" | "Non-Veg")[]) => void;
}

const VegNonVegFilter = ({ selectedOptions, onChange }: VegNonVegFilterProps) => {
  const toggleOption = (option: "Veg" | "Non-Veg") => {
    if (selectedOptions.includes(option)) {
      onChange(selectedOptions.filter((item) => item !== option));
    } else {
      onChange([...selectedOptions, option]);
    }
  };

  return (
    <div className="flex flex-col gap-4">
      <div
  className={cn(
    "flex items-center gap-2 h-[30px] px-2.5 rounded-md w-fit cursor-pointer border",
    selectedOptions.includes("Veg")
      ? "bg-[#F2FCE2] border-[#008001]"
      : "border-[#D9D9D9]"
  )}
  onClick={() => toggleOption("Veg")}
>
        <div className="w-[15px] h-[15px] border border-[#008001] flex items-center justify-center">
          {selectedOptions.includes("Veg") && (
            <div className="w-[9px] h-[9px] bg-[#008001]"></div>
          )}
        </div>
        <span className="text-[#008001] font-lato text-sm font-semibold">Veg</span>
      </div>

      <div
        className={cn(
          "flex items-center gap-2 h-[30px] px-2.5 rounded-md w-fit cursor-pointer border border-[#D9D9D9]",
          selectedOptions.includes("Non-Veg")
          ? "bg-[#FFF2F2] border-[#FF0000]"
          : "border-[#D9D9D9]"
      )}
        
        onClick={() => toggleOption("Non-Veg")}
      >
        <div className="w-[15px] h-[15px] border border-[#FF0000] flex items-center justify-center">
          {selectedOptions.includes("Non-Veg") && (
            <div className="w-0 h-0 border-l-[4.5px] border-l-transparent border-r-[4.5px] border-r-transparent border-b-[7.5px] border-b-[#FF0000]"></div>
          )}
        </div>
        <span className={cn(
          "font-lato text-[#FF0000] text-sm font-semibold",
          selectedOptions.includes("Non-Veg") ? "text-[#FF0000]" : "text-[#FF0000]"
        )}>
          Non-Veg
        </span>
      </div>
    </div>
  );
};

export default VegNonVegFilter;
