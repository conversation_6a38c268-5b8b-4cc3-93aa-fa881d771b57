
# HomeFoodi Vendors Project - Windsurf Rules

## Code Quality
- Use TypeScript for all new code with strict mode
- Follow ESLint and Prettier configurations
- Maximum file size: 500 lines
- Maximum function length: 100 lines
- Naming conventions:
  * Files: PascalCase for components
  * Variables: camelCase
  * Constants: UPPER_SNAKE_CASE
  * Types/Interfaces: PascalCase
  * CSS Classes: kebab-case
- Component Guidelines:
  * Props interface required
  * Error boundary wrapper
  * Loading state handling
  * Form validation handling
  * Proper TypeScript types


    ## Security Practices
- Never expose API keys or sensitive credentials
- Implement proper input validation
-jwt

- Follow security best practices for authentication
- Data encryption standards:
  * At rest: AES-256
  * In transit: TLS 1.3
- Session management:
  * 30-minute session timeout
  * Secure cookie handling
  * Token rotation
- Logging requirements:
  * No sensitive data
  * Structured format

  ## Performance Standards
- Tech Stack:
  * React 18 with TypeScript
  * Vite for build tooling
  * TanStack Query for data management
  * Tailwind CSS for styling # check this point 
  * Shadcn UI for components
  * Framer Motion for animations

- Image optimization:
  * WebP/AVIF formats
  * Lazy loading
  * Responsive images
  * 10MB size limit for uploads
- State management optimization:
  * Memoization
  * Virtual scrolling
  * Debouncing/throttling


  ## Documentation
- Keep README files up to date
- Document all environment variables
- Maintain clear API documentation
- Component documentation:
  * Props interface
  * Usage examples
  * Edge cases
- Architecture Decision Records (ADR)
- API versioning documentation
- Troubleshooting guides

## Protected Modules
Changes require explicit approval:

## Error Handling & Logging
- Error categorization:
  * User errors
  * System errors
  * Network errors
- Logging levels:
  * ERROR: System failures
  * WARN: Potential issues
  * INFO: Important events
  * DEBUG: Development info
- Error recovery:
  * Graceful degradation
  * Retry mechanisms
  * Fallback options
- User feedback:
  * Clear error messages
  * Recovery instructions
  * Support contact

## State Management
- - Local State:
  * Form state
  * UI state
  * Filter state
  * Search parameters
  * Pagination state
- Context:
  * Authentication
  * User preferences
  * Theme
  * Notifications
  * Feature flags

### User & Vendor Management Module
- Image upload handling:
  * 400x150 ratio for profile
  * 10 images for Caterer
  * 5MB size limit
  * WebP/AVIF formats


## Browser Support
- Chrome:
  * v132 for Windows, macOS, Android
  * v133 for iOS & iPadOS
- Mozilla:
  * v134.0.2 for all platforms
- Safari:
  * v18.1.1 for macOS
  * v17/v18 for iOS/iPadOS

## Note
- When you create a new file, make sure to add a comment at the top of the file with the changes you made.

- When you update/make changes to a file, make sure to rewrite the comment at the top of the file with the changes you made. If there is no comment, then add one.



<development>
- Implement  vendor functionality changes without modifying the existing UI.
- Ensure that any vendor changes integrate seamlessly with the UI that has already been developed and is present in the codebase.
- Do not alter, restructure, or redesign any UI components.
-We have all ready designed the UI. So, do not alter or restructure any UI components.
</development>

<process>
- Always refer to the PRD before developing a functionality.
- Update memory with PRD details to ensure accurate implementation in one attempt.
</process>








