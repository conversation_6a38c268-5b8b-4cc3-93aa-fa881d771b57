import { useState } from "react";
import { useNavigate } from "react-router-dom";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { createOrUpdateUser, getUserById, User } from "@/lib/supabase";
import { generateToken, storeToken, JwtPayload } from "@/lib/jwt";
import axios from "axios";
import "./Signup.css";

// API endpoints for OTP
const SEND_OTP_ENDPOINT = "https://homefoodi-payment.uatsparxit.xyz/send-otp";
const VERIFY_OTP_ENDPOINT = "https://homefoodi-payment.uatsparxit.xyz/verify-otp";

const Signup = () => {
  const navigate = useNavigate();
  const [phoneNumber, setPhoneNumber] = useState("");
  const [showOTP, setShowOTP] = useState(false);
  const [otp, setOtp] = useState("");
  const [showNameDialog, setShowNameDialog] = useState(false);
  const [name, setName] = useState("");
  const [isAgreed, setIsAgreed] = useState(false);
  const [isOtpVerified, setIsOtpVerified] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [otpRequestId, setOtpRequestId] = useState("");
  const [isPhoneEditable, setIsPhoneEditable] = useState(false);

  // Handle phone number input
  const handlePhoneNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, "");
    if (value.length <= 10) {
      setPhoneNumber(value);
    }
  };

  // Toggle phone number edit mode
  const togglePhoneEdit = () => {
    // Always set to true when clicked
    setIsPhoneEditable(true);
    // Reset OTP state when editing phone number
    setShowOTP(false);
    setOtp("");
  };

  // Handle OTP input
  const handleOtpChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, "");
    if (value.length <= 6) {
      setOtp(value);
    }
  };

  // Request OTP
  const handleRequestOTP = async () => {
    if (phoneNumber.length !== 10) {
      toast({
        title: "Invalid Phone Number",
        description: "Please enter a valid 10-digit phone number",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);
      
      // Make API call to request OTP
      const response = await axios.post(SEND_OTP_ENDPOINT, {
        phone: phoneNumber
      });
      
      if (response.data && response.data.success) {
        // Store the request ID for verification
        setOtpRequestId(response.data.request_id || "");
        setShowOTP(true);
        toast({
          title: "OTP Sent",
          description: "A verification code has been sent to your phone",
        });
      } else {
        console.log('Send OTP failed:', response.data);
        toast({
          title: "Error",
          description: response.data?.message || "Failed to send OTP. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error("Error sending OTP:", error);
      
      // Show the exact error message from the API response if available
      const errorMessage = error.response?.data?.message || "Failed to send OTP. Please try again later.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Verify OTP
  const handleVerifyOTP = async () => {
    if (otp.length !== 6) {
      toast({
        title: "Invalid OTP",
        description: "Please enter a valid 6-digit OTP",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);
      
      // Make API call to verify OTP
      const response = await axios.post(VERIFY_OTP_ENDPOINT, {
        phone: phoneNumber,
        otp: otp,
        request_id: otpRequestId
      });
      
      if (response.data && response.data.success) {
        try {
          // Get user's address directly from localStorage
          const savedAddress = localStorage.getItem('homefoodi_user_address');
          let address = null;
          let city = null;
          let state = null;
          
          // Parse the address to extract city and state if available
          if (savedAddress) {
            address = savedAddress;
            
            // Split the address by commas to extract city and state
            const addressParts = savedAddress.split(',').map(part => part.trim());
            
            // If we have at least 2 parts, the second part is likely the city
            if (addressParts.length >= 2) {
              city = addressParts[1];
            }
            
            // If we have at least 3 parts, the third part might contain the state
            if (addressParts.length >= 3) {
              // The third part might be "State Name" or "State Name 123456"
              const statePart = addressParts[2];
              // Try to extract just the state name by removing any numbers
              state = statePart.replace(/\d+/g, '').trim();
            }
            
            console.log('Extracted address info:', { address, city, state });
          }
          
          // Save user data to Supabase with address information
          const { error, data } = await createOrUpdateUser(phoneNumber, undefined, address, city, state);
          
          if (error) {
            console.error("Error saving user data:", error);
            toast({
              title: "Database Error",
              description: "There was an error saving your information. Please try again.",
              variant: "destructive",
            });
            return;
          }
          
          // Use the returned user data from the createOrUpdateUser function
          const user = data;
          
          if (user) {
            // Generate JWT token
            const payload: JwtPayload = {
              userId: user.id || '',
              phoneNumber: user.phone_number,
              // Don't include name in the token if it's null
              ...(user.name !== null ? { name: user.name } : {})
            };
            
            const token = generateToken(payload);
            
            // Store token in localStorage
            storeToken(token);
            
            // Save user details to localStorage
            localStorage.setItem("homefoodi_user", JSON.stringify(user));
          }
          
          setIsOtpVerified(true);
          toast({
            title: "OTP Verified",
            description: "Your phone number has been verified successfully",
          });
          
          // If user already has a name, redirect to home page
          // Otherwise show the name dialog
          if (user && user.name) {
            toast({
              title: "Welcome Back",
              description: `Welcome back, ${user.name}!`,
            });
            // Set a flag to indicate user just logged in to prevent location toast
            sessionStorage.setItem('just_logged_in', 'true');
            navigate('/');
          } else {
            // Show name dialog only for new users without a name
            setShowNameDialog(true);
          }
        } catch (error) {
          console.error("Unexpected error during user creation:", error);
          toast({
            title: "Error",
            description: "An unexpected error occurred. Please try again.",
            variant: "destructive",
          });
        }
      } else {
        console.log('OTP verification failed:', response.data);
        toast({
          title: "Error",
          description: response.data?.message || "OTP not valid",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error("Error verifying OTP:", error);
      
      // Show the exact error message from the API response if available
      const errorMessage = error.response?.data?.message || "Failed to verify OTP. Please try again later.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // Resend OTP
  const handleResendOTP = async () => {
    try {
      setIsLoading(true);
      
      // Make API call to resend OTP
      const response = await axios.post(SEND_OTP_ENDPOINT, {
        phone: phoneNumber
      });
      
      if (response.data && response.data.success) {
        // Store the request ID for verification
        setOtpRequestId(response.data.request_id || "");
        toast({
          title: "OTP Resent",
          description: "A new verification code has been sent to your phone",
        });
      } else {
        console.log('Resend OTP failed:', response.data);
        toast({
          title: "Error",
          description: response.data?.message || "Failed to resend OTP. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error: any) {
      console.error("Error resending OTP:", error);
      
      // Show the exact error message from the API response if available
      const errorMessage = error.response?.data?.message || "Failed to resend OTP. Please try again later.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Submit name
  const handleSubmitName = async () => {
    if (!name.trim()) {
      toast({
        title: "Name Required",
        description: "Please enter your name",
        variant: "destructive",
      });
      return;
    }

    // Save name to localStorage
    
    // Get user's address directly from localStorage
    const savedAddress = localStorage.getItem('homefoodi_user_address');
    let address = null;
    let city = null;
    let state = null;
    
    // Parse the address to extract city and state if available
    if (savedAddress) {
      address = savedAddress;
      
      // Split the address by commas to extract city and state
      const addressParts = savedAddress.split(',').map(part => part.trim());
      
      // If we have at least 2 parts, the second part is likely the city
      if (addressParts.length >= 2) {
        city = addressParts[1];
      }
      
      // If we have at least 3 parts, the third part might contain the state
      if (addressParts.length >= 3) {
        // The third part might be "State Name" or "State Name 123456"
        const statePart = addressParts[2];
        // Try to extract just the state name by removing any numbers
        state = statePart.replace(/\d+/g, '').trim();
      }
      
      console.log('Extracted address info:', { address, city, state });
    }
    
    // Update user name and address in Supabase
    try {
      const { error, data } = await createOrUpdateUser(phoneNumber, name, address, city, state);
      
      if (data) {
        // Update user details in localStorage with the updated data from the server
        localStorage.setItem("homefoodi_user", JSON.stringify(data));
        
        // Update JWT token with new name
        const payload: JwtPayload = {
          userId: data.id || '',
          phoneNumber: data.phone_number,
          name: name // Name will never be null here since we check for !name.trim() above
        };
        
        const token = generateToken(payload);
        storeToken(token);
      }
      
      if (error) {
        console.error("Error updating user name:", error);
        toast({
          title: "Database Error",
          description: "There was an error saving your name. Please try again.",
          variant: "destructive",
        });
        return;
      }
      
      // Close the dialog
      setShowNameDialog(false);
      
      toast({
        title: "Registration Successful",
        description: "Welcome to HomeFoodi!",
      });
      
      // Set a flag to indicate user just logged in to prevent location toast
      sessionStorage.setItem('just_logged_in', 'true');
      
      // Redirect to dashboard after name submission
      navigate("/");
    } catch (error) {
      console.error("Unexpected error during name update:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-white font-[Inter,sans-serif]">
      <Header />
      
      <main className="flex flex-col items-center justify-center w-full px-6 py-[83px] max-w-[752px] m-auto">
        <div className="w-full ">
          <h1 className="text-[24px] font-semibold text-[#111827] mb-2">Welcome</h1>
          <p className="text-[14px] text-[#6B7280] mb-6">Login for a seamless experience</p>
          
          <div className="w-full">
            <div className="mb-4 relative">
              <div className="phone-input-wrapper rounded-lg border border-[#D1D5DB] overflow-hidden">
                <div className="flex items-center">
                  <div className="px-4 py-3 text-[16px] text-[#111827]">
                    +91
                  </div>
                  <Input
                    id="phone"
                    type="tel"
                    value={phoneNumber}
                    onChange={handlePhoneNumberChange}
                    className="w-full border-0 h-[70px] text-[16px] focus:ring-0 focus:outline-none"
                    placeholder=""
                    disabled={showOTP && !isPhoneEditable}
                    readOnly={showOTP && !isPhoneEditable}
                  />
                  {showOTP && (
                    <button
                      type="button"
                      onClick={togglePhoneEdit}
                      className="px-4 text-[14px] text-[#0078DD] hover:text-[#0066bb] font-medium"
                    >
                      Edit
                    </button>
                  )}
                </div>
              </div>
              <Label 
                htmlFor="phone" 
                className="absolute -top-[10px] left-[12px] px-1 bg-white text-xs font-normal text-[#6B7280] z-10"
              >
                Enter Mobile Number*
              </Label>
            </div>

            {showOTP && (
              <div className="animate-fadeIn mb-4 relative">
                <div className="rounded-lg border border-[#D1D5DB] overflow-hidden">
                  <div className="relative">
                    <Input
                      id="otp"
                      type="text"
                      value={otp}
                      onChange={handleOtpChange}
                      className="w-full px-4 border-0 rounded-[8px] text-[16px] h-[70px] focus:ring-0 focus:outline-none"
                      placeholder="Enter 6-digit OTP"
                      maxLength={6}
                    />
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-[12px] text-[#6B7280]">
                      Valid for 10 minutes
                    </div>
                  </div>
                </div>
                <Label 
                  htmlFor="otp" 
                  className="absolute -top-[10px] left-[12px] px-1 bg-white text-xs font-normal text-[#6B7280] z-10"
                >
                  Enter OTP*
                </Label>
                <div className="flex justify-end mt-2">
                  <button
                    type="button"
                    onClick={handleResendOTP}
                    className="text-[12px] text-[#0078DD] hover:underline"
                  >
                    Resend OTP
                  </button>
                </div>
              </div>
            )}

            <div className="flex items-center gap-2 mb-6">
              <Checkbox 
                id="terms" 
                checked={isAgreed}
                onCheckedChange={(checked) => setIsAgreed(checked as boolean)}
                className="w-4 h-4 border border-[#E5E7EB] rounded"
              />
              <Label
                htmlFor="terms"
                className="text-[14px] text-[#6B7280]"
              >
                I Agree to{" "}
                <a 
                  href="/page/terms"
                  className="text-[#0078DD] hover:underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Terms and Conditions
                </a>
                {" "}T&C's{" "}
                <a 
                  href="/page/privacy"
                  className="text-[#0078DD] hover:underline"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Privacy Policy
                </a>
              </Label>
            </div>

            <div className="flex gap-4">
              {!showOTP ? (
                <Button 
                  className="flex-1 h-[48px] bg-[#0078DD] hover:bg-[#0078dde6] text-white rounded-[8px] text-sm font-bold"
                  onClick={handleRequestOTP}
                  disabled={!isAgreed || phoneNumber.length === 0 || isLoading}
                >
                  {isLoading ? "SENDING..." : "LOGIN WITH OTP"}
                </Button>
              ) : (
                <Button 
                  className="flex-1 h-[48px] bg-[#0078DD] hover:bg-[#0078dde6] text-white rounded-[8px] text-sm font-bold"
                  onClick={handleVerifyOTP}
                  disabled={otp.length !== 6 || isLoading}
                >
                  {isLoading ? "VERIFYING..." : "VERIFY OTP"}
                </Button>
              )}
              
              {/* <Button 
                className="flex-1 h-[48px] bg-[#166534] hover:bg-[#166534]/90 text-white rounded-[8px] text-sm font-bold"
                onClick={() => setShowNameDialog(true)}
                disabled={isOtpVerified}
              >
                SAVE & CONTINUE
              </Button> */}
            </div>
          </div>
        </div>
      </main>

      {/* Name Dialog - Always shows on top of any page */}
      <Dialog open={showNameDialog} onOpenChange={setShowNameDialog}>
        <DialogContent className="sm:max-w-md name-dialog">
          <DialogHeader>
            <DialogTitle>Enter Your Name</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                className="w-full px-4  rounded-[8px] text-[16px] h-[50px] focus:ring-0 focus:outline-none"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Enter your full name"
              />
            </div>
            <Button 
              className="submit-btn flex-1 h-[40px] bg-[#0078DD] hover:bg-[#0078dde6] text-white rounded-[8px] text-sm font-bold"
              onClick={handleSubmitName}
            >
              SUBMIT
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      <Footer />
    </div>
  );
};

export default Signup;
