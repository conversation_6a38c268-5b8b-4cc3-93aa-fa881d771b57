import React from 'react';
import { Helmet } from 'react-helmet';

interface PageMetaProps {
  title: string;
  description?: string;
  keywords?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  canonical?: string;
  noIndex?: boolean;
}

/**
 * PageMeta component for managing page-specific SEO meta tags
 * Uses react-helmet for better SSR compatibility
 */
export const PageMeta: React.FC<PageMetaProps> = ({
  title,
  description,
  keywords,
  ogTitle,
  ogDescription,
  ogImage,
  canonical,
  noIndex = false
}) => {
  const defaultOgImage = "/og-image.png";
  const currentUrl = typeof window !== 'undefined' ? window.location.href : '';

  // Debug log to see if component is rendering
  console.log('PageMeta rendering with title:', title);

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      {description && <meta name="description" content={description} />}
      {keywords && <meta name="keywords" content={keywords} />}
      
      {/* Robots Meta */}
      {noIndex && <meta name="robots" content="noindex, nofollow" />}
      
      {/* Open Graph Tags */}
      <meta property="og:title" content={ogTitle || title} />
      <meta property="og:description" content={ogDescription || description || ''} />
      <meta property="og:image" content={ogImage || defaultOgImage} />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:type" content="website" />
      <meta property="og:site_name" content="Homefoodi" />
      
      {/* Twitter Card Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={ogTitle || title} />
      <meta name="twitter:description" content={ogDescription || description || ''} />
      <meta name="twitter:image" content={ogImage || defaultOgImage} />
      
      {/* Canonical URL */}
      {canonical && <link rel="canonical" href={canonical} />}
      
      {/* Additional SEO Tags */}
      <meta name="author" content="Homefoodi" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      
      {/* Structured Data for Local Business */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "LocalBusiness",
          "name": "Homefoodi",
          "description": "Order fresh, healthy, and customized meals from verified home chefs, tiffin suppliers, and caterers.",
          "url": currentUrl,
          "logo": `${window.location.origin}/og-image.png`,
          "sameAs": [
            // Add social media URLs here when available
          ]
        })}
      </script>
      
      {/* Google Fonts */}
      <link
        href="https://fonts.googleapis.com/css2?family=Lato:wght@400;500;600;700;900&display=swap"
        rel="stylesheet"
      />
    </Helmet>
  );
};
