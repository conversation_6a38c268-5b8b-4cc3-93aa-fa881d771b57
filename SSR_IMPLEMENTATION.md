# SSR and Dynamic Meta Tags Implementation

This document outlines the implementation of Server-Side Rendering (SSR) preparation and dynamic meta tags for the Homefoodi application.

## What's Been Implemented

### 1. Dynamic Meta Tags ✅

#### Components Created:
- **`src/components/seo/PageMeta.tsx`** - Reusable component for managing page-specific SEO meta tags
- **`src/utils/metaUtils.ts`** - Utility functions for generating dynamic titles, descriptions, and keywords
- **`src/hooks/usePageMeta.ts`** - Custom hook for managing meta data (alternative approach)

#### Pages Updated:
- **`src/pages/ServiceListing.tsx`** - Now uses dynamic meta titles based on:
  - Vendor type (Home Chefs, Tiffin Suppliers, Caterers)
  - City and state from URL parameters or localStorage
  - Generates SEO-friendly titles like "Home Chefs in Delhi, Delhi | Homefoodi"

- **`src/pages/VendorDetails.tsx`** - Now uses dynamic meta titles based on:
  - Vendor business name
  - Location (city, state)
  - Cuisine types
  - Vendor rating
  - Generates titles like "Abha's Kitchen - Delhi, Delhi | Homefoodi"

#### Features:
- **Dynamic Titles**: Page titles change based on content and location
- **SEO Descriptions**: Auto-generated descriptions for better search visibility
- **Keywords**: Relevant keywords based on vendor type, location, and cuisines
- **Open Graph Tags**: Social media sharing optimization
- **Canonical URLs**: Proper URL canonicalization
- **Structured Data**: JSON-LD for local business schema

### 2. SSR Preparation ✅

#### Files Created:
- **`src/ssr/server.ts`** - Express server setup for SSR (ready for future implementation)
- **`src/entry-server.tsx`** - Server-side rendering entry point

#### Current Status:
- SSR infrastructure is **prepared** but not yet active
- The app currently runs as a standard SPA (Single Page Application)
- All meta tags work correctly in SPA mode using react-helmet

## How Dynamic Meta Tags Work

### Service Listing Page
```typescript
// Example: /service/Delhi?type=home-chef
const pageTitle = generateServiceListingTitle('home-chef', 'Delhi', 'Delhi');
// Result: "Home Chefs in Delhi, Delhi | Homefoodi"

const pageDescription = generateServiceListingDescription('home-chef', 'Delhi', 'Delhi');
// Result: "Find the best home chefs offering delicious homemade meals in Delhi, Delhi. Order fresh, healthy, and customized meals from verified vendors on Homefoodi."
```

### Vendor Details Page
```typescript
// Example: /service/Delhi/Abha's Kitchen/123
const vendorMetaData = {
  businessName: "Abha's Kitchen",
  city: "Delhi",
  state: "Delhi",
  cuisines: ["North Indian", "Fast Food"],
  rating: 4.9
};

const pageTitle = generateVendorDetailsTitle(vendorMetaData);
// Result: "Abha's Kitchen - Delhi, Delhi | Homefoodi"
```

## To Enable Full SSR (Future Implementation)

### 1. Install Additional Dependencies
```bash
npm install express @types/express
npm install --save-dev @vitejs/plugin-react-ssr
```

### 2. Update vite.config.ts
```typescript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  build: {
    rollupOptions: {
      input: {
        client: resolve(__dirname, 'index.html'),
        server: resolve(__dirname, 'src/entry-server.tsx'),
      },
    },
  },
  ssr: {
    // SSR-specific configuration
    noExternal: ['react-helmet'],
  },
});
```

### 3. Update package.json Scripts
```json
{
  "scripts": {
    "build:client": "vite build --outDir dist/client",
    "build:server": "vite build --ssr src/entry-server.tsx --outDir dist/server",
    "build:ssr": "npm run build:client && npm run build:server",
    "serve:ssr": "node dist/server/entry-server.js"
  }
}
```

### 4. Update index.html
Add SSR placeholders:
```html
<div id="root"><!--ssr-outlet--></div>
<!--ssr-head-->
```

## Benefits of Current Implementation

### 1. SEO Improvements
- **Dynamic Titles**: Each page has unique, descriptive titles
- **Meta Descriptions**: Improved search engine snippets
- **Keywords**: Better keyword targeting
- **Open Graph**: Enhanced social media sharing

### 2. User Experience
- **Relevant Titles**: Browser tabs show meaningful page titles
- **Breadcrumb Navigation**: Clear page identification
- **Social Sharing**: Rich previews when sharing links

### 3. Technical Benefits
- **React Helmet**: Works in both SPA and SSR modes
- **Modular Design**: Easy to maintain and extend
- **Type Safety**: Full TypeScript support
- **Performance**: Minimal overhead for meta tag management

## Testing the Implementation

### 1. Service Listing Pages
- Visit `/service-listing?type=home-chef` - Should show "Home Chefs | Homefoodi"
- Visit `/service/Delhi?type=tiffin_supplier` - Should show "Tiffin Suppliers in Delhi | Homefoodi"

### 2. Vendor Details Pages
- Visit any vendor page - Should show "Vendor Name - City, State | Homefoodi"
- Check page source to see meta tags

### 3. SEO Validation
- Use browser dev tools to inspect `<head>` section
- Check for proper Open Graph tags
- Validate structured data with Google's Rich Results Test

## Future Enhancements

1. **Full SSR Implementation**: Complete the SSR setup for better initial page load SEO
2. **Sitemap Generation**: Auto-generate XML sitemaps for all vendor pages
3. **Schema Markup**: Add more detailed structured data for vendors
4. **Meta Tag Testing**: Automated tests for meta tag generation
5. **Performance Monitoring**: Track SEO improvements and page load times

## Maintenance Notes

- Meta tag templates are in `src/utils/metaUtils.ts`
- Update vendor type mappings in the same file when adding new vendor types
- The `PageMeta` component handles all SEO tags consistently across pages
- All changes are backward compatible with existing functionality
