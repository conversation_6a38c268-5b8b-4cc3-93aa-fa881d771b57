/**
 * Interaction Service
 * 
 * This file contains functions for tracking vendor interactions (Call, WhatsApp, Enquiry)
 * and storing them in the database.
 */

import { supabase } from '@/lib/supabase';

// Define interaction types
export type InteractionType = 'Call' | 'WhatsApp' | 'Enquiry' | 'Direction';

/**
 * Track a vendor interaction in the database
 * @param vendorId - ID of the vendor
 * @param userId - ID of the user
 * @param interactionType - Type of interaction (Call, WhatsApp, Enquiry)
 * @param message - Optional message for the interaction (used for Enquiry)
 * @returns Promise with success status
 */
export const trackVendorInteraction = async (
  vendorId: string,
  userId: string,
  interactionType: InteractionType,
  message?: string,
  phone_number?: string,
  name?: string,
  vendor_type?: string
): Promise<boolean> => {
  try {
    // Create the interaction record
    const { data, error } = await supabase
      .from('vendor_interactions')
      .insert([
        {
          vendor_id: vendorId,
          user_id: userId,
          interaction_type: interactionType,
          status: 'pending',
          message: message || null,
          phone_number: phone_number || null,
          name: name || null,
          vendor_type: vendor_type || null,
          created_at: new Date().toISOString()
        }
      ]);

    if (error) {
      console.error('Error tracking vendor interaction:', error);
      return false;
    }

    console.log('Vendor interaction tracked successfully:', interactionType);
    return true;
  } catch (error) {
    console.error('Error tracking vendor interaction:', error);
    return false;
  }
};
