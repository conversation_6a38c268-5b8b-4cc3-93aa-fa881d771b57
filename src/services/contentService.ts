/**
 * Content Service
 * 
 * This file contains functions for fetching content page data from the database.
 * It provides methods to retrieve content pages by their slug.
 */

import { supabase } from '@/lib/supabase';

/**
 * Interface for content page data
 */
export interface ContentPage {
  id: string;
  title: string;
  slug: string;
  content: string;
  last_modified: string;
  published_at: string | null;
  status: string;
  version: string;
}

/**
 * Fetch a content page by its slug
 * @param slug - The slug of the content page to fetch
 * @returns Promise with the content page data or null if not found
 */
export const fetchContentPageBySlug = async (slug: string): Promise<ContentPage | null> => {
  try {
    console.log('ContentService: Fetching content page with slug:', slug);
    
    if (!slug) {
      console.error('ContentService: No slug provided');
      return null;
    }
    
    // Fetch the content page
    const { data, error } = await supabase
      .from('content_pages')
      .select('*')
      .eq('slug', slug)
      .eq('status', 'published')
      .single();
    
    if (error) {
      console.error('Error fetching content page:', error);
      return null;
    }

    if (!data) {
      console.log('ContentService: No content page found with slug:', slug);
      return null;
    }

    console.log('ContentService: Content page found:', data);
    return data as ContentPage;
  } catch (error) {
    console.error('Unexpected error in fetchContentPageBySlug:', error);
    return null;
  }
};
