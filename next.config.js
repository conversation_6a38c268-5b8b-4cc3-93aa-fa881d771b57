/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  images: {
    domains: ['localhost', 'homefoodi.com'],
    unoptimized: true
  },
  experimental: {
    appDir: true
  },
  // Handle static assets
  assetPrefix: process.env.NODE_ENV === 'production' ? '' : '',
  // Custom webpack config for handling assets
  webpack: (config) => {
    config.module.rules.push({
      test: /\.(png|jpe?g|gif|svg)$/,
      use: {
        loader: 'file-loader',
        options: {
          publicPath: '/_next/static/images/',
          outputPath: 'static/images/',
        },
      },
    });
    return config;
  },
  // Redirect configuration
  async redirects() {
    return [
      {
        source: '/service-listing',
        destination: '/services',
        permanent: true,
      },
    ];
  },
  // Rewrite configuration for dynamic routes
  async rewrites() {
    return [
      {
        source: '/service/:city',
        destination: '/services?city=:city',
      },
      {
        source: '/service/:city/:name/:id',
        destination: '/vendor/:id?city=:city&name=:name',
      },
    ];
  },
};

module.exports = nextConfig;
