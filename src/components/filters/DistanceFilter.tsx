/**
 * DistanceFilter.tsx
 * 
 * This component displays a slider for filtering vendors by distance.
 * Updated to ensure the slider position visually matches the selected value and persists after filter application.
 */

import React, { useEffect, useState } from "react";
import { Slider } from "@/components/ui/slider";

interface DistanceFilterProps {
  distance: number;
  onChange: (distance: number) => void;
}

/**
 * DistanceFilter component that ensures the slider position visually matches the selected value.
 * Properly syncs with parent component and maintains state after filter application.
 */
const DistanceFilter: React.FC<DistanceFilterProps> = ({ distance = 1, onChange }) => {
  // Ensure we never go below 1km
  const safeDistance = Math.max(1, distance);
  
  // Local state to track the current slider value
  const [currentDistance, setCurrentDistance] = useState<number>(safeDistance);
  
  // Update local state when prop changes
  useEffect(() => {
    setCurrentDistance(safeDistance);
  }, [safeDistance]);

  // Handle slider change
  const handleSliderChange = (value: number[]) => {
    const newValue = Math.max(1, value[0]); // Ensure minimum is 1
    setCurrentDistance(newValue); // Update local state
    onChange(newValue); // Notify parent component
  };

  return (
    <div className="flex flex-col gap-4 align-center justify-center">
      <div className="text-[#9B9B9B] font-lato text-base select-none touch-none">
        {`Within ${currentDistance}km of your location`}
      </div>
      
      {/* Pass both value and defaultValue to ensure proper rendering */}
      <Slider
        value={[currentDistance]}
        defaultValue={[currentDistance]}
        min={1}
        max={50}
        step={1}
        onValueChange={handleSliderChange}
        className="w-full"
      />
      
      <div className="flex justify-between text-xs text-[#9B9B9B]">
        <span>1km</span>
        <span>50km</span>
      </div>
    </div>
  );
};

export default DistanceFilter;
