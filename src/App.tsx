
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import Index from "./pages/Index";
import Listing from "./pages/Listing";
import ServiceListing from "./pages/ServiceListing";
import VendorDetails from "./pages/VendorDetails";
import NotFound from "./pages/NotFound";
import Signup from "./pages/Signup";
import ContentPage from "./pages/ContentPage";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/listing" element={<Listing />} />
          <Route path="/service-listing" element={<ServiceListing />} />
          <Route path="/service/:city" element={<ServiceListing />} />
          <Route path="/service/:city/:name/:id" element={<VendorDetails />} />
          <Route path="/signup" element={<Signup />} />
          {/* Content Pages */}
          <Route path="/page/:slug" element={<ContentPage />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
