/**
 * SearchBar Component
 * Updated: 2025-03-26
 * Changes: Implemented Google Maps API for address autocomplete and detect location functionality
 * using the useGoogleMaps custom hook for dynamic location suggestions via Google Geocoding API.
 * Added navigation to listing pages based on selected address and vendor type, passing coordinates and type in the query.
 * Updated to save vendor_type and vendor_type_display in localStorage for consistent tab highlighting.
 * Integrated JustDial-like location permission system
 */

import { MapPin, ChevronDown, Locate, Search } from "lucide-react";
import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import useGoogleMaps from "@/hooks/useGoogleMaps";
import { toast } from "@/components/ui/use-toast";
import { requestLocationPermission, getUserAddress, setUserAddress, extractCityFromAddress } from "@/lib/location";
import { normalizeVendorType } from "@/utils/vendorTypeUtils";

// Service options for dropdown with their corresponding types for routing
const serviceOptions = [
  { display: "Search for Supplier", type: "home-chef", displayForTab: "Home Chefs" },
  { display: "Home Chef", type: "home-chef", displayForTab: "Home Chefs" },
  { display: "Tiffin Suppliers", type: "tiffin_supplier", displayForTab: "Tiffin Suppliers" },
  { display: "Caterer", type: "caterer", displayForTab: "Caterer" }
];

// Map to ensure consistent vendor type values
const vendorTypeMap: Record<string, { type: string, display: string }> = {
  "Search for Supplier": { type: "home-chef", display: "Home Chefs" },
  "Home Chef": { type: "home-chef", display: "Home Chefs" },
  "Tiffin Suppliers": { type: "tiffin_supplier", display: "Tiffin Suppliers" },
  "Caterer": { type: "caterer", display: "Caterer" }
};

export const SearchBar = () => {
  // Initialize navigate hook for routing
  const navigate = useNavigate();
  // Initialize Google Maps hook with empty initial location
  const {
    locationInput,
    setLocationInput,
    predictions: filteredLocations,
    selectLocation,
    selectLocationWithPlaceId,
    detectCurrentLocation,
    isLoadingLocation,
    recentLocations,
    coordinates // Include coordinates from the hook
  } = useGoogleMaps({
    initialLocation: ""
  });
  
  // Request browser's native location permission on component mount
  useEffect(() => {
    const requestLocation = async () => {
      try {
        // First check if there's already a saved address in localStorage
        const savedAddress = getUserAddress();
        if (savedAddress) {
          console.log('Using saved address from localStorage:', savedAddress);
          // Use the saved address and don't request current location
          setLocationInput(savedAddress);
          setLocation(savedAddress);
          
          // Force input element to update with the saved value
          setTimeout(() => {
            const locationInputElement = document.getElementById('location-input');
            if (locationInputElement instanceof HTMLInputElement) {
              locationInputElement.value = savedAddress;
            }
          }, 0);
          
          // Exit early - don't request current location when we have a saved address
          return;
        }
        
        // Only proceed with location permission if no saved address exists
        // Check if user just logged in to avoid showing duplicate toasts
        const justLoggedIn = sessionStorage.getItem('just_logged_in');
        
        console.log('Requesting browser location permission...');
        const permissionGranted = await requestLocationPermission();
        
        if (permissionGranted) {
          console.log('Location permission granted');
          
          // Only show toast if user didn't just log in
          if (!justLoggedIn) {
            toast({
              title: "Location Access Granted",
              description: "We'll show you the best vendors near you."
            });
          }
          
          // Clear the login flag after using it
          sessionStorage.removeItem('just_logged_in');
        }
      } catch (error) {
        console.error('Error requesting location permission:', error);
        // Clear input when permission is denied
        setLocationInput("");
        setLocation("");
      }
    };
    
    // Request location permission immediately when component mounts
    requestLocation();
    
    // Listen for address updates from location service
    const handleAddressUpdated = (event: CustomEvent) => {
      console.log('Address updated event received:', event.detail);
      if (event.detail) {
        // Force clear the input first to ensure UI updates
        setLocationInput('');
        setTimeout(() => {
          setLocationInput(event.detail);
          setLocation(event.detail);
        }, 0);
      }
    };
    
    // Listen for location permission denied events
    const handleLocationPermissionDenied = () => {
      console.log('Location permission denied event received');
      // Clear the location input when permission is denied
      setLocationInput("");
      setLocation("");
    };
    
    // Listen for location data cleared events
    const handleLocationDataCleared = () => {
      console.log('Location data cleared event received');
      // Clear the location input when data is cleared
      setLocationInput("");
      setLocation("");
    };
    
    // Listen for location selected events from the useGoogleMaps hook
    const handleLocationSelected = (event: CustomEvent) => {
      console.log('Location selected event received:', event.detail);
      if (event.detail) {
        // Force clear the input first to ensure UI updates
        setLocationInput('');
        // Update the UI with the new location
        setTimeout(() => {
          setLocationInput(event.detail);
          setLocation(event.detail);
          
          // Force input element to update with the new value
          const locationInputElement = document.getElementById('location-input');
          if (locationInputElement instanceof HTMLInputElement) {
            locationInputElement.value = event.detail;
          }
        }, 0);
      }
    };
    
    // Add event listeners
    window.addEventListener('addressUpdated', handleAddressUpdated as EventListener);
    window.addEventListener('locationPermissionDenied', handleLocationPermissionDenied as EventListener);
    window.addEventListener('locationDataCleared', handleLocationDataCleared as EventListener);
    window.addEventListener('locationSelected', handleLocationSelected as EventListener);
    
    // Clean up
    return () => {
      window.removeEventListener('addressUpdated', handleAddressUpdated as EventListener);
      window.removeEventListener('locationPermissionDenied', handleLocationPermissionDenied as EventListener);
      window.removeEventListener('locationDataCleared', handleLocationDataCleared as EventListener);
      window.removeEventListener('locationSelected', handleLocationSelected as EventListener);
    };
  }, []);
  
  // Log coordinates when they change
  useEffect(() => {
    if (coordinates) {
      console.log('Selected location coordinates:', coordinates);
      // You can store these coordinates in localStorage or context if needed
      localStorage.setItem('userCoordinates', JSON.stringify(coordinates));
    }
  }, [coordinates]);
  
  // State for location display and dropdown - initialize from localStorage if available
  const [location, setLocation] = useState(() => {
    // Try to get the saved address from localStorage first
    const savedAddress = localStorage.getItem('homefoodi_user_address');
    if (savedAddress) {
      console.log('Initializing location state from localStorage:', savedAddress);
      return savedAddress;
    }
    return "";
  });
  const [showLocationDropdown, setShowLocationDropdown] = useState(false);
  
  // Force update location input from localStorage on component mount
  useEffect(() => {
    const savedAddress = localStorage.getItem('homefoodi_user_address');
    if (savedAddress) {
      console.log('Forcing location update from localStorage on mount:', savedAddress);
      // Force a clear and update to ensure UI reflects the saved address
      setLocationInput('');
      setTimeout(() => {
        setLocationInput(savedAddress);
        setLocation(savedAddress);
        
        // Force input element to update with the saved value
        const locationInputElement = document.getElementById('location-input');
        if (locationInputElement instanceof HTMLInputElement) {
          locationInputElement.value = savedAddress;
        }
      }, 0);
    }
  }, []);
  
  // Initialize vendor type from localStorage or default to Search for Supplier
  const getInitialVendorType = () => {
    // Check if we're on the homepage - always use Search for Supplier on homepage
    if (window.location.pathname === '/' || window.location.pathname === '') {
      return serviceOptions[0]; // Always default to "Search for Supplier" on homepage
    }
    
    const savedVendorType = localStorage.getItem('vendor_type');
    if (savedVendorType) {
      // Find the matching service option, but skip the "Search for Supplier" option
      // which is at index 0
      let matchingOption = null;
      
      // Special handling for home-chef to ensure we select "Home Chef" (index 1)
      // and not "Search for Supplier" (index 0) when home-chef is saved
      if (savedVendorType === 'home-chef') {
        matchingOption = serviceOptions[1]; // "Home Chef" option
      } else {
        // For other vendor types, find the matching option
        matchingOption = serviceOptions.find((option, index) => 
          index > 0 && option.type === savedVendorType
        );
      }
      
      return matchingOption || serviceOptions[0];
    }
    return serviceOptions[0]; // Default to Search for Supplier
  };
  
  const initialVendorOption = getInitialVendorType();
  
  // State for search input and dropdown
  const [searchQuery, setSearchQuery] = useState(initialVendorOption.display);
  const [searchInput, setSearchInput] = useState(initialVendorOption.display);
  const [selectedVendorType, setSelectedVendorType] = useState(initialVendorOption.type);
  const [showServiceDropdown, setShowServiceDropdown] = useState(false);
  
  // Refs for dropdown elements
  const locationDropdownRef = useRef<HTMLDivElement>(null);
  const serviceDropdownRef = useRef<HTMLDivElement>(null);

  // Update location state when locationInput changes and persist it
  useEffect(() => {
    if (locationInput) {
      setLocation(locationInput);
      // Save location to localStorage to persist it
      localStorage.setItem('homefoodi_selected_location', locationInput);
    }
  }, [locationInput]);
  
  // Save coordinates to localStorage when they change
  useEffect(() => {
    if (coordinates) {
      localStorage.setItem('homefoodi_user_latitude', coordinates.lat.toString());
      localStorage.setItem('homefoodi_user_longitude', coordinates.lng.toString());
    }
  }, [coordinates]);
  
  // Load saved location on component mount
  useEffect(() => {
    // First check for a saved address from reverse geocoding
    const savedAddress = getUserAddress();
    if (savedAddress) {
      setLocationInput(savedAddress);
      setLocation(savedAddress);
      return;
    }
    
    // Fall back to saved location if no address is available
    const savedLocation = localStorage.getItem('homefoodi_selected_location');
    if (savedLocation) {
      setLocationInput(savedLocation);
      setLocation(savedLocation);
      
      // Always call selectLocation to ensure coordinates are set in the hook's state
      // The hook will handle geocoding internally
      selectLocation(savedLocation);
    }
  }, []);

  // Handle clicks outside dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (locationDropdownRef.current && !locationDropdownRef.current.contains(event.target as Node)) {
        setShowLocationDropdown(false);
      }
      if (serviceDropdownRef.current && !serviceDropdownRef.current.contains(event.target as Node)) {
        setShowServiceDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Handle search submission
  const handleSearch = (overrideVendorType?: string) => {
    const effectiveVendorType = overrideVendorType || selectedVendorType;
    
    console.log('handleSearch called with:', {
      locationInput,
      selectedVendorType: effectiveVendorType,
      searchInput
    });
    
    // Update the final selected values
    setLocation(locationInput);
    setSearchQuery(searchInput);
    
    // Close dropdowns
    setShowLocationDropdown(false);
    setShowServiceDropdown(false);
    
    // Check if we have both a location and vendor type
    if (!locationInput) {
      toast({
        title: "Location Required",
        description: "Please select a valid location before searching",
        variant: "destructive"
      });
      return;
    }
    
    if (!effectiveVendorType) {
      toast({
        title: "Service Type Required",
        description: "Please select a service type before searching",
        variant: "destructive"
      });
      return;
    }
    
    // Handle detect current location button click
    const handleDetectLocation = async () => {
      try {
        // Request location permission using browser's native dialog
        const permissionGranted = await requestLocationPermission();
        
        if (permissionGranted) {
          // Get the saved address
          const savedAddress = getUserAddress();
          if (savedAddress) {
            console.log('Setting address after detection:', savedAddress);
            setLocationInput(savedAddress);
            setLocation(savedAddress);
            
            toast({
              title: "Location Detected",
              description: "Your current location has been set.",
            });
          } else {
            // Fall back to the hook's detectCurrentLocation
            await detectCurrentLocation();
            
            toast({
              title: "Location Detected",
              description: "Your current location has been set.",
            });
          }
        } else {
          // Clear location input when permission is denied
          setLocationInput("");
          setLocation("");
          
          toast({
            title: "Location Permission Denied",
            description: "Please allow location access or enter your location manually.",
            variant: "destructive"
          });
        }
      } catch (error) {
        console.error('Error detecting location:', error);
        // Clear location input on error
        setLocationInput("");
        setLocation("");
        
        toast({
          title: "Location Error",
          description: "Could not detect your location. Please try again or enter it manually.",
          variant: "destructive"
        });
      }
    };

    // Save vendor type to localStorage for consistent tab highlighting
    const vendorType = selectedVendorType || "home-chef";
    localStorage.setItem('vendor_type', vendorType);
    
    // Find the corresponding service option to get the display name for the tab
    const selectedService = serviceOptions.find(option => option.type === vendorType);
    if (selectedService) {
      localStorage.setItem('vendor_type_display', selectedService.displayForTab);
      // Also set the activeNav value to ensure the correct tab is highlighted in the header
      localStorage.setItem('activeNav', selectedService.displayForTab);
    } else {
      // Default to Home Chefs if no service is found
      localStorage.setItem('vendor_type_display', 'Home Chefs');
      localStorage.setItem('activeNav', 'Home Chefs');
    }
    
    // Dispatch custom event to notify about vendor type change
    window.dispatchEvent(new Event('vendorTypeChanged'));
    
    // Navigate to the service listing page with the vendor type as a query parameter
    // and coordinates in localStorage (already saved in the useEffect hook)
    if (coordinates) {
      // Coordinates are already saved to localStorage via the useEffect hook
      
      console.log("Navigating to service listing with:", {
        vendorType: selectedVendorType,
        coordinates,
        location: locationInput
      });
      
      // Navigate to the service listing page with the vendor type
      // For home-chef, don't include the type parameter to match header behavior
      const effectiveVendorType = overrideVendorType || selectedVendorType;
      console.log('About to navigate with vendor type:', effectiveVendorType);
      
      if (effectiveVendorType === "home-chef") {
        // Set activeNav to ensure Home Chefs tab is highlighted
        localStorage.setItem('activeNav', 'Home Chefs');
        console.log('Navigating to Home Chefs listing');
        navigate(`/service/${extractCityFromAddress(location)}`);
      } else if (effectiveVendorType === "tiffin_supplier") {
        // Set activeNav to ensure Tiffin Suppliers tab is highlighted
        localStorage.setItem('activeNav', 'Tiffin Suppliers');
        console.log('Navigating to Tiffin Suppliers listing');
        navigate(`/service/${extractCityFromAddress(location)}?type=tiffin_supplier`);
      } else if (effectiveVendorType === "caterer") {
        // Set activeNav to ensure Caterer tab is highlighted
        localStorage.setItem('activeNav', 'Caterer');
        console.log('Navigating to Caterer listing');
        navigate(`/service/${extractCityFromAddress(location)}?type=caterer`);
      } else {
        // Fallback for any other vendor types
        localStorage.setItem('activeNav', 'Caterer');
        console.log('Navigating to fallback listing with type:', effectiveVendorType);
        navigate(`/service/${extractCityFromAddress(location)}?type=${effectiveVendorType}`);
      }
    } else {
      // If we have a location but no coordinates, try to geocode it again
      toast({
        title: "Processing Location",
        description: "Getting coordinates for your location...",
      });
      
      // Try to geocode the location again
      selectLocation(locationInput);
      
      // Wait a moment for geocoding to complete
      setTimeout(() => {
        if (coordinates) {
          // Store coordinates in localStorage
          localStorage.setItem('homefoodi_user_latitude', coordinates.lat.toString());
          localStorage.setItem('homefoodi_user_longitude', coordinates.lng.toString());
          
          // Navigate to the service listing page
          // For home-chef, don't include the type parameter to match header behavior
          if (selectedVendorType === "home-chef") {
            // Set activeNav to ensure Home Chefs tab is highlighted
            localStorage.setItem('activeNav', 'Home Chefs');
            navigate(`/service/${extractCityFromAddress(location)}`);
          } else if (selectedVendorType === "tiffin_supplier") {
            // Set activeNav to ensure Tiffin Suppliers tab is highlighted
            localStorage.setItem('activeNav', 'Tiffin Suppliers');
            navigate(`/service/${extractCityFromAddress(location)}?type=tiffin_supplier`);
          } else if (selectedVendorType === "caterer") {
            // Set activeNav to ensure Caterer tab is highlighted
            localStorage.setItem('activeNav', 'Caterer');
            navigate(`/service/${extractCityFromAddress(location)}?type=caterer`);
          } else {
            // Fallback for any other vendor types
            navigate(`/service/${extractCityFromAddress(location)}?type=${selectedVendorType}`);
          }
        } else {
          toast({
            title: "Location Error",
            description: "Could not get coordinates for the selected location",
            variant: "destructive"
          });
        }
      }, 1000);
    }
  };

  // Handle Enter key press for search
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  // Handle service option selection
  const handleServiceSelect = (service: typeof serviceOptions[0]) => {
    console.log('Service selected:', service);
    setSearchInput(service.display);
    setSearchQuery(service.display);
    setSelectedVendorType(service.type);
    setShowServiceDropdown(false);
    
    // If it's the "Search for Supplier" placeholder, don't do anything else
    if (service.display === "Search for Supplier") {
      console.log('Search for Supplier selected - not triggering search');
      return;
    }
    
    // Save vendor type to localStorage for consistent tab highlighting
    localStorage.setItem('vendor_type', service.type);
    localStorage.setItem('vendor_type_display', service.displayForTab);
    console.log('Set vendor_type to:', service.type);
    console.log('Set vendor_type_display to:', service.displayForTab);
    
    // Set activeNav to ensure the correct tab is highlighted in the header
    localStorage.setItem('activeNav', service.displayForTab);
    console.log('Set activeNav to:', service.displayForTab);
    
    // Dispatch custom event to notify about vendor type change
    window.dispatchEvent(new Event('vendorTypeChanged'));
    
    // If location is already selected, trigger search
    if (locationInput) {
      console.log('Location input exists, will trigger search with:', locationInput);
      // Even if coordinates aren't available yet, we'll try to geocode in handleSearch
      // Small delay to ensure state is updated
      setTimeout(() => {
        console.log('Triggering handleSearch with vendor type:', service.type);
        // Force the selectedVendorType to be the correct one from the service object
        // This ensures we're using the exact type from the service option
        handleSearchWithVendorType(service.type);
      }, 100);
    }
  };
  
  // Handle search with a specific vendor type
  const handleSearchWithVendorType = (vendorType: string) => {
    // Temporarily set the selectedVendorType to ensure correct navigation
    setSelectedVendorType(vendorType);
    
    // Find the corresponding service option
    const serviceOption = serviceOptions.find(option => option.type === vendorType);
    if (serviceOption) {
      // Set the activeNav value to ensure the correct tab is highlighted
      localStorage.setItem('activeNav', serviceOption.displayForTab);
      localStorage.setItem('vendor_type', vendorType);
      localStorage.setItem('vendor_type_display', serviceOption.displayForTab);
    }
    
    // Now call the regular handleSearch function
    handleSearch(vendorType);
  };

  // Handle location selection
  const handleLocationSelect = (loc: string) => {
    console.log(`Location selected: ${loc}`);
    
    // Close the dropdown immediately
    setShowLocationDropdown(false);
    
    // Immediately save to localStorage using the proper function
    setUserAddress(loc);
    
    // First clear the input field to ensure old value is removed
    setLocationInput("");
    setLocation("");
    
    // Force update the UI with the new location
    setTimeout(() => {
      // Update both state variables with the new location
      setLocation(loc);
      setLocationInput(loc);
      
      // Force input element to update with the new value
      const locationInputElement = document.getElementById('location-input');
      if (locationInputElement instanceof HTMLInputElement) {
        locationInputElement.value = loc;
      }
      
      // Update location in the Google Maps hook
      // This will trigger the custom event and handle all the updates
      selectLocation(loc);
      
      // Provide user feedback
      toast({
        title: "Location Updated",
        description: `Your location has been set to ${loc}`,
        duration: 2000,
      });
    }, 0);
  };

  // Clear all recent locations
  const clearRecentLocations = () => {
    localStorage.removeItem('recentLocations');
    toast({
      title: "Recent Locations Cleared",
      description: "Your recent location history has been cleared.",
    });
    // The hook will automatically update its state on the next render
  };

  return (
    <div className="flex items-center bg-white mx-auto p-3 md:p-5 rounded-[10px] max-md:flex-col max-md:gap-2 relative z-[4] shadow-md ">

      
      {/* Location Input with Dropdown */}
        <div className="relative flex items-center gap-2.5 text-[#989CA3] w-full md:w-auto" ref={locationDropdownRef}>
          {/* Location Icon */}
          <div 
            className="cursor-pointer" 
            onClick={() => setShowLocationDropdown(!showLocationDropdown)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 28 28" fill="none" className="md:w-[28px] md:h-[28px]">
              <path fillRule="evenodd" clipRule="evenodd" d="M14 25.6663C17.9375 25.6663 24.5 18.4308 24.5 12.7034C24.5 6.97598 19.799 2.33301 14 2.33301C8.20101 2.33301 3.5 6.97598 3.5 12.7034C3.5 18.4308 10.0625 25.6663 14 25.6663ZM14 16.333C15.933 16.333 17.5 14.766 17.5 12.833C17.5 10.9 15.933 9.33301 14 9.33301C12.067 9.33301 10.5 10.9 10.5 12.833C10.5 14.766 12.067 16.333 14 16.333Z" fill="#008001"/>
            </svg>
          </div>
          
          {/* Location Input */}
          <div className="flex-1">
            <input
              id="location-input"
              type="text"
              value={locationInput}
              onChange={(e) => {
                setLocationInput(e.target.value);
                // Always show dropdown when typing, even for a single character
                setShowLocationDropdown(true);
                // Ensure predictions are fetched immediately
                if (e.target.value.trim().length >= 2) {
                  console.log('Fetching predictions for:', e.target.value);
                }
              }}
              onFocus={() => setShowLocationDropdown(true)}
              onKeyDown={handleKeyDown}
              className="outline-none text-[#989CA3] text-base md:text-xl w-full"
              placeholder="Enter your location for suggestions"
              autoComplete="off" // Prevent browser autocomplete from interfering
            />
          </div>
          
          {/* Location Dropdown Icon */}
          <div 
            className="cursor-pointer" 
            onClick={() => setShowLocationDropdown(!showLocationDropdown)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="10" viewBox="0 0 22 12" fill="none" className="md:w-[22px] md:h-[12px]">
              <path d="M11 12L0.607697 0.75H21.3923L11 12Z" fill="#E87616"/>
            </svg>
          </div>
          
          {isLoadingLocation && (
            <div className="absolute right-12 top-1/2 transform -translate-y-1/2">
              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-gray-900"></div>
            </div>
          )}
        
        {/* Location Dropdown - JustDial Style */}
        {showLocationDropdown && (
          <div className="absolute top-full left-0 mt-1 w-full bg-white shadow-lg rounded-md z-10 max-h-[400px] overflow-y-auto">
            {/* Detect Location Button - Always show at the top like JustDial */}
            <div className="p-3 hover:bg-gray-100 cursor-pointer text-base border-b flex items-center gap-2 text-[#4285F4]">
              <div className="flex items-center gap-2" onClick={detectCurrentLocation}>
                <Locate className="h-6 w-6" />
                <span>Detect Location</span>
                {isLoadingLocation && (
                  <div className="ml-2 animate-spin h-4 w-4 border-2 border-[#4285F4] border-t-transparent rounded-full"></div>
                )}
              </div>
            </div>
            
            {/* Search Results - Show specific locations when user is typing */}
            {locationInput.trim().length >= 2 && (
              filteredLocations.length > 0 ? (
                <>
                  {filteredLocations.map((loc, index) => {
                    // Split location into main text and secondary text like JustDial
                    const parts = loc.description.split(',').map(part => part.trim());
                    const mainText = parts[0];
                    const secondaryText = parts.slice(1).join(', ');
                    
                    return (
                      <div
                        key={`search-${index}`}
                        className="p-3 hover:bg-gray-100 cursor-pointer text-base border-b text-left"
                        onClick={() => selectLocationWithPlaceId(loc.place_id, loc.description)}
                      >
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-2 text-gray-500 flex-shrink-0" />
                          <div>
                            <div className="font-medium">{mainText}</div>
                            {secondaryText && (
                              <div className="text-sm text-gray-500">{secondaryText}</div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </>
              ) : (
                <div className="p-3 text-center text-gray-500">
                  <div className="animate-pulse">Searching for locations...</div>
                </div>
              )
            )}
            
            {/* Recent Locations - Only show if no search input */}
            {locationInput.trim().length < 2 && recentLocations.length > 0 && (
              <>
                <div className="p-3 flex justify-between items-center border-b">
                  <div className="text-sm font-medium text-gray-500">RECENT LOCATIONS</div>
                  <button 
                    onClick={clearRecentLocations}
                    className="text-sm text-[#4285F4] hover:text-[#2b5fba]"
                  >
                    Clear All
                  </button>
                </div>
                {recentLocations.map((loc, index) => {
                  // Split location into main text and secondary text like JustDial
                  const parts = loc.split(',').map(part => part.trim());
                  const mainText = parts[0];
                  const secondaryText = parts.slice(1).join(', ');
                  
                  return (
                    <div
                      key={`recent-${index}`}
                      className="p-3 hover:bg-gray-100 cursor-pointer text-base border-b text-left"
                      onClick={() => selectLocationWithPlaceId(`mock_${index}`, loc)}
                    >
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 mr-2 text-gray-500 flex-shrink-0" />
                        <div>
                          <div className="font-medium">{mainText}</div>
                          {secondaryText && (
                            <div className="text-sm text-gray-500">{secondaryText}</div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </>
            )}
            
            {/* No Results - JustDial Style */}
            {locationInput.trim() !== "" && filteredLocations.length === 0 && (
              <div className="p-3 text-gray-500 text-base text-left">
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-2 text-gray-500 flex-shrink-0" />
                  <span>No results found for "{locationInput}"</span>
                </div>
                <div className="mt-2 text-sm text-gray-400">
                  Try a different location or check your spelling
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="w-full md:w-px h-px md:h-[30px] bg-[#D9D9D9] my-1 md:my-0 md:mx-5" />

      {/* Service Selection with Dropdown */}
      <div className="relative flex-1 flex items-center text-[#989CA3] w-full md:w-auto" ref={serviceDropdownRef}>
        {/* Service Input */}
        <div 
          className="flex-1 cursor-pointer" 
          onClick={() => setShowServiceDropdown(!showServiceDropdown)}
        >
          <input
            type="text"
            value={searchInput}
            readOnly
            className="outline-none text-[#989CA3] text-base md:text-xl w-full cursor-pointer"
            placeholder="Select a service"
          />
        </div>
        
        {/* Dropdown Icon */}
        <div 
          className="cursor-pointer" 
          onClick={() => setShowServiceDropdown(!showServiceDropdown)}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="10" viewBox="0 0 22 12" fill="none" className="md:w-[22px] md:h-[12px]">
            <path d="M11 12L0.607697 0.75H21.3923L11 12Z" fill="#E87616"/>
          </svg>
        </div>
        
        {/* Service Options Dropdown */}
        {showServiceDropdown && (
          <div className="absolute top-full left-0 mt-1 w-full bg-white shadow-lg rounded-md z-10">
            {serviceOptions.map((service, index) => (
              <div
                key={index}
                className="p-3 hover:bg-gray-100 cursor-pointer text-base border-b last:border-b-0 text-left"
                onClick={() => handleServiceSelect(service)}
              >
                {service.display}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchBar;
