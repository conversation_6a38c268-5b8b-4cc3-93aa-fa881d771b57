/**
 * ServiceCard component for the home page
 * 
 * Updated to set vendor type and activeNav in localStorage when a service card is clicked
 * This ensures proper redirection to the correct service listing page with the appropriate tab active
 */

import { cn } from "@/lib/utils";
import { useNavigate } from "react-router-dom";

interface ServiceCardProps {
  title: string;
  subtitle: string;
  className?: string;
  backgroundImage?: string;
  iconColor?: string;
  shadowColor?: string;
  link?: string;
  vendorType?: string; // Added vendor type prop
}

export const ServiceCard = ({
  title,
  subtitle,
  className,
  backgroundImage,
  iconColor = "#E87616",
  shadowColor = "#008001",
  link = "#",
  vendorType, // Default vendor type based on title
}: ServiceCardProps) => {
  const navigate = useNavigate();
  
  // Handle click on service card
  const handleServiceClick = (e: React.MouseEvent) => {
    e.preventDefault();
    
    let type = vendorType;
    let redirectUrl = '/service-listing';
    let activeTab = title;
    
    // Determine vendor type and redirect URL based on title if not provided
    if (!type) {
      if (title === 'Home Chefs') {
        type = 'home-chef';
        // No need to add type parameter for home-chef as it's the default
      } else if (title === 'Tiffin Suppliers') {
        type = 'tiffin_supplier';
        redirectUrl = '/service-listing?type=tiffin_supplier';
      } else if (title === 'Caterer') {
        type = 'caterer';
        redirectUrl = '/service-listing?type=caterer';
      }
    } else {
      // If type is provided as a prop, use it to determine the redirect URL
      if (type === 'tiffin_supplier') {
        redirectUrl = '/service-listing?type=tiffin_supplier';
      } else if (type === 'caterer') {
        redirectUrl = '/service-listing?type=caterer';
      }
    }
    
    console.log('ServiceCard: Navigating to', redirectUrl, 'with vendor type', type);
    
    // Store vendor type and display name in localStorage
    localStorage.setItem('vendor_type', type || 'home-chef');
    localStorage.setItem('vendor_type_display', title);
    
    // Set activeNav to ensure the correct tab is highlighted in the header
    localStorage.setItem('activeNav', activeTab);
    
    // Dispatch custom event to notify about vendor type change
    window.dispatchEvent(new Event('vendorTypeChanged'));
    
    // Navigate to the appropriate service listing page
    navigate(redirectUrl);
  };
  return (
    <div
      className={cn(
        "relative overflow-hidden rounded-[24px] h-[280px] md:h-[312px] w-full max-w-[316px] bg-cover bg-center z-[0] cursor-pointer",
        className,
      )}
      // style={
      //   backgroundImage ? { backgroundImage: `url(${backgroundImage})` } : {}
      // }
      onClick={handleServiceClick}
      role="button"
      aria-label={`View ${title} listing`}
    >
      {/* Gradient overlay at bottom - uses the shadowColor prop */}
      <div 
        className="absolute inset-0 z-10" 
        style={{
          background: `linear-gradient(to bottom, rgba(255, 255, 255, 0) 37.78%, ${shadowColor} 85.57%)`,
        }}
      />
      <div 
        className="absolute inset-0" 
        
      >
      <img src={backgroundImage} alt={title} className="bg-cover h-full object-cover
"/>
      </div>
      
      
      {/* Content container */}
      <div className="relative z-10 flex flex-col justify-end h-full p-4 md:p-5">
        {/* Title and subtitle with icon */}
        <div className="text-white text-left flex items-center">
          {/* Circular icon with arrow */}
          <a 
            href={link} 
            className="w-[32px] h-[32px] md:w-[36px] md:h-[36px] rounded-full flex items-center justify-center mr-[15px] md:mr-[20px]"
            style={{ backgroundColor: iconColor }}
            aria-label={`Go to ${title}`}
            onClick={handleServiceClick}
          >
            <svg width="16" height="12" viewBox="0 0 19 14" fill="none" xmlns="http://www.w3.org/2000/svg" className="md:w-[19px] md:h-[14px]">
              <path d="M18.7071 7.70711C19.0976 7.31658 19.0976 6.68342 18.7071 6.29289L12.3431 -0.0710083C11.9526 -0.46153 11.3195 -0.46153 10.9289 -0.0710083C10.5384 0.319513 10.5384 0.952629 10.9289 1.34315L16.5858 7L10.9289 12.6569C10.5384 13.0474 10.5384 13.6805 10.9289 14.0711C11.3195 14.4616 11.9526 14.4616 12.3431 14.0711L18.7071 7.70711ZM0 8H18V6H0V8Z" fill="white"/>
            </svg>
          </a>
          
          <div>
            <h3 className="text-[24px] md:text-[28px]  font-black mb-0.5 md:mb-1">{title}</h3>
            <p className="text-[14px] md:text-[15px]">{subtitle}</p>
          </div>
        </div>
      </div>
      
      {/* Drop shadow effect */}
      <div 
        className="absolute inset-0 rounded-[24px]"
        style={{ boxShadow: `0 8px 8px ${shadowColor}4D` }}
      ></div>
    </div>
  );
};

export default ServiceCard;
