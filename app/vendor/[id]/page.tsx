import type { Metadata } from 'next';
import { Suspense } from 'react';
import { fetchVendorById } from '@/services/vendorService';
import VendorDetailsClient from './VendorDetailsClient';

type Props = {
  params: { id: string };
  searchParams: { [key: string]: string | string[] | undefined };
};

export async function generateMetadata({ params, searchParams }: Props): Promise<Metadata> {
  try {
    const vendor = await fetchVendorById(params.id);
    const city = searchParams.city as string;
    const vendorName = searchParams.name as string;

    if (!vendor) {
      return {
        title: 'Vendor Not Found',
        description: 'The requested vendor could not be found.',
      };
    }

    let title = 'Service Detail';
    let description = `Order from ${vendor.name}`;

    if (city) {
      title = `${vendor.name} - ${city}`;
      description += ` in ${city}`;
    } else if (vendor.location) {
      const location = vendor.location.split(',')[0]?.trim();
      if (location) {
        title = `${vendor.name} - ${location}`;
        description += ` in ${location}`;
      }
    }

    if (vendor.type) {
      description += `. Specializing in ${vendor.type}`;
    }

    if (vendor.rating && vendor.rating > 0) {
      description += `. Rated ${vendor.rating}/5 stars`;
    }

    description += '. Fresh, homemade meals delivered to your doorstep.';

    const keywords = [
      'homefoodi',
      'food delivery',
      vendor.name.toLowerCase(),
      vendor.vendor_type || 'home-chef',
      ...(city ? [city.toLowerCase()] : []),
      ...(vendor.type ? vendor.type.split(',').map(t => t.trim().toLowerCase()) : [])
    ];

    return {
      title,
      description,
      keywords,
      openGraph: {
        title,
        description,
        images: vendor.image ? [vendor.image] : undefined,
      },
      twitter: {
        title,
        description,
        images: vendor.image ? [vendor.image] : undefined,
      },
    };
  } catch (error) {
    console.error('Error generating metadata for vendor:', error);
    return {
      title: 'Service Detail',
      description: 'Order fresh, healthy, and customized meals from verified vendors on Homefoodi.',
    };
  }
}

export default function VendorPage({ params, searchParams }: Props) {
  return (
    <Suspense fallback={
      <div className="max-w-[1120px] mx-auto mt-[20px] md:mt-[30px] p-4 flex justify-center items-center h-[50vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#E87616]"></div>
      </div>
    }>
      <VendorDetailsClient vendorId={params.id} searchParams={searchParams} />
    </Suspense>
  );
}
