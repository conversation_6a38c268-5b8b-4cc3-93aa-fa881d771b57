/**
 * VendorList Component
 * 
 * This component displays a list of vendors based on the vendor type
 * stored in localStorage. It uses the useVendors hook to fetch data
 * and handles different vendor types consistently.
 */

import { useState } from 'react';
import { useVendors } from '@/hooks/useVendors';
import { BaseVendor } from '@/services/vendorService';
import { Button } from '@/components/ui/button';
import { MapPin, Phone, MessageSquare, Navigation, Star } from 'lucide-react';

const VendorList = () => {
  const { 
    vendors, 
    loading, 
    error, 
    refetch, 
    currentVendorTypeDisplay, 
    totalVendors 
  } = useVendors();

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const vendorsPerPage = 10;

  // Get current vendors for pagination
  const indexOfLastVendor = currentPage * vendorsPerPage;
  const indexOfFirstVendor = indexOfLastVendor - vendorsPerPage;
  const currentVendors = vendors.slice(indexOfFirstVendor, indexOfLastVendor);

  // Change page
  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#E87616]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-10">
        <p className="text-red-500 mb-4">Error loading vendors. Please try again.</p>
        <Button onClick={() => refetch()} variant="outline">
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="max-w-[1120px] mx-auto px-4 py-8">
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Showing 1-{Math.min(currentVendors.length, vendorsPerPage)} of {totalVendors} Results</h2>
        <p className="text-gray-600">Browse {currentVendorTypeDisplay} in your area</p>
      </div>

      {currentVendors.length === 0 ? (
        <div className="text-center py-10">
          <p className="text-gray-500 mb-4">No {currentVendorTypeDisplay} found in your area.</p>
        </div>
      ) : (
        <div className="space-y-6">
          {currentVendors.map((vendor: BaseVendor) => (
            <VendorCard key={vendor.id} vendor={vendor} />
          ))}

          {/* Pagination */}
          {totalVendors > vendorsPerPage && (
            <div className="flex justify-center mt-8">
              <div className="flex space-x-2">
                {Array.from({ length: Math.ceil(totalVendors / vendorsPerPage) }).map((_, index) => (
                  <Button
                    key={index}
                    variant={currentPage === index + 1 ? "default" : "outline"}
                    className={currentPage === index + 1 ? "bg-[#E87616]" : ""}
                    onClick={() => paginate(index + 1)}
                  >
                    {index + 1}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

interface VendorCardProps {
  vendor: BaseVendor;
}

const VendorCard = ({ vendor }: VendorCardProps) => {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="flex flex-col md:flex-row">
        {/* Vendor Image */}
        <div className="md:w-1/3 h-[200px] md:h-auto">
          <img
            src={vendor.profile_image || 'https://via.placeholder.com/300x200?text=No+Image'}
            alt={vendor.business_name || 'Vendor'}
            className="w-full h-full object-cover"
          />
        </div>

        {/* Vendor Details */}
        <div className="md:w-2/3 p-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-xl font-semibold">{vendor.business_name || 'Unnamed Vendor'}</h3>
              
              {/* Display Rating */}
              <div className="flex items-center mt-1 mb-1">
                {vendor.rating ? (
                  <>
                    <div className="flex items-center">
                      <Star size={16} className="text-yellow-400 fill-yellow-400 mr-1" />
                      <span className="text-sm font-medium">{vendor.rating}</span>
                    </div>
                  </>
                ) : (
                  <span className="text-sm text-gray-500">No ratings yet</span>
                )}
              </div>
              
              <div className="flex items-center text-gray-600 mt-1">
                <MapPin size={16} className="mr-1" />
                <span>{vendor.address || 'No address provided'}</span>
              </div>
              {vendor.city && vendor.state && (
                <p className="text-gray-600 mt-1">{vendor.city}, {vendor.state}</p>
              )}
            </div>
            <div className="flex items-center">
              <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">
                {vendor.operational_status === 'public' ? 'Open' : 'Closed'}
              </span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-6">
            <Button className="bg-[#FF6B35] hover:bg-[#E85A2A] flex items-center justify-center">
              <Phone size={16} className="mr-2" />
              Call Now
            </Button>
            <Button className="bg-[#25D366] hover:bg-[#128C7E] flex items-center justify-center">
              <MessageSquare size={16} className="mr-2" />
              Chat
            </Button>
            <Button className="bg-[#4A90E2] hover:bg-[#3A7BC8] flex items-center justify-center">
              <MessageSquare size={16} className="mr-2" />
              Send Enquiry
            </Button>
            <Button className="bg-[#5E35B1] hover:bg-[#4527A0] flex items-center justify-center">
              <Navigation size={16} className="mr-2" />
              Direction
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VendorList;
