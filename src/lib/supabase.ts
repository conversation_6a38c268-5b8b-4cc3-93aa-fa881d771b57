/**
 * Supabase client initialization and user management functions
 * Updated: 2025-03-20
 * This file contains the Supabase client and functions for user management
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// User type definition
export interface User {
  id?: string;
  phone_number: string;
  name: string | null;
  address?: string | null;
  city?: string | null;
  state?: string | null;
  created_at?: string;
}

// User-related functions
/**
 * Creates a new user or updates an existing user in the database
 * @param phoneNumber The user's phone number (used for initial lookup)
 * @param name Optional user name
 * @param address Optional user address
 * @param city Optional user city
 * @param state Optional user state
 * @returns Object containing user data or error
 */
export const createOrUpdateUser = async (phoneNumber: string, name?: string, address?: string, city?: string, state?: string) => {
  try {
    // Current timestamp for last_login
    const currentTimestamp = new Date().toISOString();
    
    // Check if user already exists
    const { data: existingUsers, error: fetchError } = await supabase
      .from('users')
      .select('*')
      .eq('phone_number', phoneNumber);
    
    if (fetchError) {
      console.error('Error checking for existing user:', fetchError);
      return { error: fetchError };
    }
    
    // Get the first user if any exists
    const existingUser = existingUsers && existingUsers.length > 0 ? existingUsers[0] : null;
    
    if (existingUser) {
      // Update existing user with last_login and name if provided
      const updateData: { 
        last_login: string; 
        name?: string;
        address?: string;
        city?: string;
        state?: string;
      } = {
        last_login: currentTimestamp
      };
      
      // Add name to update data if provided
      if (name) {
        updateData.name = name;
      }
      
      // Add address, city, and state to update data if provided
      if (address) {
        updateData.address = address;
      }
      
      if (city) {
        updateData.city = city;
      }
      
      if (state) {
        updateData.state = state;
      }
      
      const { data, error } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', existingUser.id)
        .select();
        
      if (error) {
        console.error('Error updating user:', error);
        return { error };
      }
      
      // Return updated user data
      return { 
        data: data?.[0] || { ...existingUser, ...updateData }, 
        updated: true 
      };
    } else {
      // Create new user
      const userData = {
        phone_number: phoneNumber,
        // If name is provided, use it; otherwise use null
        // The database will handle the null value since we've set a default value
        name: name || null,
        address: address || null,
        city: city || null,
        state: state || null,
        created_at: currentTimestamp,
        updated_at: currentTimestamp,
        last_login: currentTimestamp
      };
      
      const { data, error } = await supabase
        .from('users')
        .insert([userData])
        .select();
        
      if (error) {
        console.error('Error creating user:', error);
        return { error };
      }
      
      return { data: data?.[0], created: true };
    }
  } catch (error) {
    console.error('Unexpected error in createOrUpdateUser:', error);
    return { error };
  }
};

/**
 * Gets user details by ID
 * @param userId The user's ID
 * @returns User object or null if not found
 */
export const getUserById = async (userId: string): Promise<User | null> => {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();
      
    if (error) {
      // If error is PGRST116 (not found), return null instead of throwing error
      if (error.code === 'PGRST116') {
        return null;
      }
      console.error('Error fetching user:', error);
      throw error;
    }
    
    return data as User;
  } catch (error) {
    console.error('Unexpected error in getUserById:', error);
    return null;
  }
};

