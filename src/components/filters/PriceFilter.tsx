
import { cn } from "@/lib/utils";

interface PriceFilterProps {
  selectedPrice: string;
  onChange: (price: string) => void;
}

const PRICE_OPTIONS = [
  { value: "low-to-high", label: "Price-Low to High" },
  { value: "high-to-low", label: "Price-High to Low" },
];

const PriceFilter = ({ selectedPrice, onChange }: PriceFilterProps) => {
  console.log('PriceFilter rendered with selectedPrice:', selectedPrice);
  
  const handlePriceChange = (price: string) => {
    console.log('Price filter changed to:', price);
    onChange(price);
  };
  
  return (
    <div className="flex flex-col gap-3 max-w-[300px]">
      {PRICE_OPTIONS.map((option) => (
        <div
          key={option.value}
          className={cn(
            "flex items-center justify-center h-[30px] px-4 py-2 rounded-md border border-[#D9D9D9] cursor-pointer transition-colors",
            selectedPrice === option.value 
              ? " text-[#008001] border-[#008001]" 
              : "text-[#9B9B9B]"
          )}
          onClick={() => handlePriceChange(option.value)}
        >
          <span className="font-lato text-sm font-medium">{option.label}</span>
        </div>
      ))}
    </div>
  );
};

export default PriceFilter;
