import { useEffect } from 'react';

interface PageMetaData {
  title: string;
  description?: string;
  keywords?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  canonical?: string;
}

/**
 * Custom hook for managing page-specific meta data
 * This hook updates the document head with SEO-friendly meta tags
 */
export const usePageMeta = (metaData: PageMetaData) => {
  useEffect(() => {
    // Update document title
    if (metaData.title) {
      document.title = metaData.title;
    }

    // Update meta description
    if (metaData.description) {
      updateMetaTag('description', metaData.description);
    }

    // Update meta keywords
    if (metaData.keywords) {
      updateMetaTag('keywords', metaData.keywords);
    }

    // Update Open Graph tags
    if (metaData.ogTitle) {
      updateMetaTag('og:title', metaData.ogTitle, 'property');
    }

    if (metaData.ogDescription) {
      updateMetaTag('og:description', metaData.ogDescription, 'property');
    }

    if (metaData.ogImage) {
      updateMetaTag('og:image', metaData.ogImage, 'property');
    }

    // Update canonical URL
    if (metaData.canonical) {
      updateCanonicalLink(metaData.canonical);
    }
  }, [metaData]);
};

/**
 * Helper function to update or create meta tags
 */
const updateMetaTag = (name: string, content: string, attribute: string = 'name') => {
  let metaTag = document.querySelector(`meta[${attribute}="${name}"]`);
  
  if (!metaTag) {
    metaTag = document.createElement('meta');
    metaTag.setAttribute(attribute, name);
    document.head.appendChild(metaTag);
  }
  
  metaTag.setAttribute('content', content);
};

/**
 * Helper function to update canonical link
 */
const updateCanonicalLink = (href: string) => {
  let canonicalLink = document.querySelector('link[rel="canonical"]');
  
  if (!canonicalLink) {
    canonicalLink = document.createElement('link');
    canonicalLink.setAttribute('rel', 'canonical');
    document.head.appendChild(canonicalLink);
  }
  
  canonicalLink.setAttribute('href', href);
};
