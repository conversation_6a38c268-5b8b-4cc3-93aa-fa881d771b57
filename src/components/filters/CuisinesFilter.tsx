
/**
 * CuisinesFilter.tsx
 * 
 * This component displays a list of cuisines that users can select as filters.
 * Updated to fetch cuisines from the database and support filtering by cuisine ID.
 */

import { useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import { fetchAllCuisines, Cuisine } from "@/services/cuisineService";

interface CuisinesFilterProps {
  selectedCuisines: string[];
  onChange: (selected: string[]) => void;
}

const CuisinesFilter = ({ selectedCuisines, onChange }: CuisinesFilterProps) => {
  const [cuisines, setCuisines] = useState<Cuisine[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch cuisines from the database
  useEffect(() => {
    const getCuisines = async () => {
      setLoading(true);
      try {
        const data = await fetchAllCuisines();
        setCuisines(data);
      } catch (error) {
        console.error("Error fetching cuisines:", error);
      } finally {
        setLoading(false);
      }
    };

    getCuisines();
  }, []);

  const toggleCuisine = (cuisineId: string) => {
    if (selectedCuisines.includes(cuisineId)) {
      onChange(selectedCuisines.filter((id) => id !== cuisineId));
    } else {
      onChange([...selectedCuisines, cuisineId]);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-[200px]">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#008001]"></div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 gap-[30px] md:grid-cols-2 max-w-[300px]">
      {cuisines.map((cuisine) => (
        <div
          key={cuisine.id}
          className={cn(
            "font-lato text-sm text-[#9B9B9B] border border-[#EFEFEF] rounded-md py-1.5 px-3 text-center min-w-[67px] cursor-pointer transition-colors w-[auto] h-fit",
            selectedCuisines.includes(cuisine.id) 
              ? "text-[#008001] border-[#008001]" 
              : "hover:border-[#D9D9D9]"
          )}
          onClick={() => toggleCuisine(cuisine.id)}
        >
          {cuisine.name}
        </div>
      ))}
    </div>
  );
};

export default CuisinesFilter;
