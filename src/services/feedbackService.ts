/**
 * Feedback Service
 * 
 * This service handles feedback-related functionality, including:
 * - Fetching the feedback email address from the database
 * - Opening the default email client with the feedback email address
 */

import { supabase } from '@/lib/supabase';

/**
 * Interface for feedback settings data
 */
interface FeedbackSettings {
  id: string;
  email: string;
  created_at: string;
  updated_at: string;
}

/**
 * Fetches the feedback email address from the database
 * @returns Promise with the feedback email address
 */
export const getFeedbackEmail = async (): Promise<string> => {
  try {
    // Fetch the feedback email from the feedback_settings table
    const { data, error } = await supabase
      .from('feedback_settings')
      .select('email')
      .single();
    
    if (error) {
      console.error('Error fetching feedback email:', error);
      // Return a default email if there's an error
      return '<EMAIL>';
    }
    
    if (!data || !data.email) {
      console.warn('No feedback email found in settings');
      // Return a default email if no data is found
      return '<EMAIL>';
    }
    
    console.log('Fetched feedback email:', data.email);
    return data.email;
  } catch (error) {
    console.error('Unexpected error fetching feedback email:', error);
    // Return a default email in case of unexpected errors
    return '<EMAIL>';
  }
};

/**
 * Opens the default email client with the feedback email address
 */
export const openFeedbackEmail = async (): Promise<void> => {
  try {
    // Get the feedback email from the database
    const feedbackEmail = await getFeedbackEmail();
    
    // Open the default email client with the feedback email address
    window.location.href = `mailto:${feedbackEmail}?subject=Feedback for HomeFoodi`;
  } catch (error) {
    console.error('Error opening feedback email:', error);
    // Fallback to a default email in case of errors
    window.location.href = 'mailto:<EMAIL>?subject=Feedback for HomeFoodi';
  }
};
