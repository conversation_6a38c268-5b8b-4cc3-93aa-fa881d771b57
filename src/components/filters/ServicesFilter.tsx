
/**
 * ServicesFilter.tsx
 * 
 * This component displays a list of services that users can select as filters.
 * Updated to fetch services from the database and support filtering by service ID.
 */

import { useEffect, useState } from "react";
import { cn } from "@/lib/utils";
import { fetchAllServices, Service, fetchServicesByVendorType } from "@/services/serviceService";

interface ServicesFilterProps {
  selectedServices: string[];
  onChange: (selected: string[]) => void;
}

const ServicesFilter = ({ selectedServices, onChange }: ServicesFilterProps) => {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch services from the database based on current vendor type
  useEffect(() => {
    const getServices = async () => {
      setLoading(true);
      try {
        // Get the current vendor type from localStorage
        const vendorType = localStorage.getItem('vendor_type') || 'home-chef';
        
        // Fetch services by vendor type
        const data = await fetchServicesByVendorType(vendorType);
        setServices(data);
      } catch (error) {
        console.error("Error fetching services:", error);
        // Fallback to fetching all services if there's an error
        try {
          const allServices = await fetchAllServices();
          setServices(allServices);
        } catch (fallbackError) {
          console.error("Error fetching all services:", fallbackError);
        }
      } finally {
        setLoading(false);
      }
    };

    getServices();
  }, []);

  const toggleService = (serviceId: string) => {
    if (selectedServices.includes(serviceId)) {
      onChange(selectedServices.filter((id) => id !== serviceId));
    } else {
      onChange([...selectedServices, serviceId]);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-[200px]">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#008001]"></div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-[30px] md:grid-cols-2 max-w-[auto]">
      {services.map((service) => (
        <div
          key={service.id}
          className={cn(
            "font-lato text-sm text-[#9B9B9B] border border-[#EFEFEF] rounded-md py-1.5 px-3 text-center min-w-[67px] cursor-pointer transition-colors w-[auto] h-fit",
            selectedServices.includes(service.id) 
              ? "text-[#008001] border-[#008001]" 
              : "hover:border-[#D9D9D9]"
          )}
          onClick={() => toggleService(service.id)}
        >
          {service.name}
        </div>
      ))}
    </div>
  );
};

export default ServicesFilter;
