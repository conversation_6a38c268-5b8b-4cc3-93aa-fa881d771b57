
/**
 * ReviewTabContent Component
 * 
 * Updated to fetch and display dynamic review data based on vendor ID.
 * Retrieves reviews from the reviews table in the database with user information.
 * Maintains the existing UI structure while making the data dynamic.
 */

import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { useParams } from "react-router-dom";
import { fetchVendorReviews, submitReview, ReviewWithUserInfo, hasUserReviewedVendor } from "@/services/reviewService";
import { toast, Toaster } from "react-hot-toast";
import { isAuthenticated, getUserId, requireAuth } from "@/utils/authUtils";
import { useRating } from "@/contexts/RatingContext";

interface ReviewTabContentProps {
  className?: string;
  vendorId?: string;
}

interface ReviewTabContentProps {
  className?: string;
  vendorId?: string;
}

export const ReviewTabContent = ({ className, vendorId }: ReviewTabContentProps) => {
  const { id } = useParams<{ id: string }>();
  const [reviewsData, setReviewsData] = useState<ReviewWithUserInfo[]>([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [averageRating, setAverageRating] = useState<number>(4.6); // Default value
  const [reviewText, setReviewText] = useState<string>('');
  const [selectedRating, setSelectedRating] = useState<number>(0);
  const [hasAlreadyReviewed, setHasAlreadyReviewed] = useState<boolean>(false);
  
  // Use the rating context to share the rating with other components
  const { setVendorRating } = useRating();
  
  /**
   * Handles the submission of a new review
   * Gets the current user ID from localStorage and submits the review to the database
   */
  const handleSubmitReview = async () => {
    // Check if user is authenticated first
    if (!isAuthenticated()) {
      toast.error('Please log in to submit a review');
      return;
    }
    
    try {
      // Get vendor ID from URL path or props
      const pathParts = window.location.pathname.split('/');
      const vendorIdFromPath = pathParts[pathParts.length - 1]; // Last part of the path
      const effectiveVendorId = vendorId || id;
      const finalVendorId = effectiveVendorId || vendorIdFromPath;
      
      // Get user ID using the utility function
      const userId = getUserId();
      
      // Double-check user ID (should never happen since we checked isAuthenticated)
      if (!userId) {
        toast.error('Please log in to submit a review');
        return;
      }
      
      if (!finalVendorId) {
        toast.error('Vendor ID not found');
        return;
      }
      
      if (!userId) {
        toast.error('Please log in to submit a review');
        return;
      }
      
      // Always set a default rating of 1 if none is selected
      const finalRating = selectedRating === 0 ? 1 : selectedRating;
      
      if (!reviewText.trim()) {
        toast.error('Please enter a review');
        return;
      }
      
      setSubmitting(true);
      
      // Submit the review
      const result = await submitReview(
        finalVendorId,
        userId,
        finalRating,
        reviewText
      );
      
      if (result.success) {
        toast.success('Review submitted successfully');
        
        // Reset form
        setReviewText('');
        setSelectedRating(0);
        
        // Refresh reviews
        const updatedReviews = await fetchVendorReviews(finalVendorId);
        if (updatedReviews && updatedReviews.length > 0) {
          setReviewsData(updatedReviews);
          
          // Recalculate average rating
          const totalRating = updatedReviews.reduce((sum, review) => sum + Number(review.rating), 0);
          const avgRating = totalRating / updatedReviews.length;
          setAverageRating(parseFloat(avgRating.toFixed(1)));
        }
      } else {
        toast.error(result.error?.message || 'Failed to submit review');
      }
    } catch (error) {
      console.error('Error submitting review:', error);
      toast.error('An error occurred while submitting your review');
    } finally {
      setSubmitting(false);
    }
  };
  
  useEffect(() => {
    const fetchReviews = async () => {
      try {
        setLoading(true);
        // Use the vendorId from props or from the URL params
        const effectiveVendorId = vendorId || id;
        
        // Get vendor ID from URL path
        const pathParts = window.location.pathname.split('/');
        const vendorIdFromPath = pathParts[pathParts.length - 1]; // Last part of the path
        
        console.log('URL path:', window.location.pathname);
        console.log('Path parts:', pathParts);
        console.log('Vendor ID from path:', vendorIdFromPath);
        
        // Use the most reliable vendor ID source from available options
        const finalVendorId = effectiveVendorId || vendorIdFromPath;
        
        console.log('Fetching reviews for vendor ID:', finalVendorId);
        console.log('URL path vendor ID:', vendorIdFromPath);
        console.log('Props vendor ID:', vendorId);
        console.log('Context ID:', id);
        
        if (!finalVendorId) {
          console.warn('No vendor ID available, cannot fetch reviews');
          setError('Vendor ID not available');
          setLoading(false);
          return;
        }
        
        // Check if the current user has already reviewed this vendor
        if (isAuthenticated()) {
          const userId = getUserId();
          if (userId) {
            const userHasReviewed = await hasUserReviewedVendor(finalVendorId, userId);
            setHasAlreadyReviewed(userHasReviewed);
            console.log('User has already reviewed this vendor:', userHasReviewed);
          }
        }
        
        // Fetch reviews data
        const reviewsData = await fetchVendorReviews(finalVendorId);
        console.log('Fetched reviews data:', reviewsData);
        
        if (reviewsData && reviewsData.length > 0) {
          console.log('Setting dynamic review data:', reviewsData);
          setReviewsData(reviewsData);
          
          // Calculate average rating
          const totalRating = reviewsData.reduce((sum, review) => sum + Number(review.rating), 0);
          const avgRating = totalRating / reviewsData.length;
          const formattedRating = parseFloat(avgRating.toFixed(1));
          setAverageRating(formattedRating);
          
          // Update the shared rating context
          setVendorRating(formattedRating);
          
          console.log('Calculated average rating:', formattedRating);
        } else {
          console.log('No reviews found for vendor ID:', finalVendorId);
          // If no reviews, set empty array
          setReviewsData([]);
          setAverageRating(0);
          
          // Update the shared rating context to 0
          setVendorRating(0);
        }
      } catch (err: any) {
        console.error('Error fetching reviews data:', err);
        setError('Failed to load reviews data');
      } finally {
        setLoading(false);
      }
    };
    
    fetchReviews();
  // Run when vendorId or id changes
  }, [vendorId, id]);
  return (
    <div className={cn("mb-20 border-t border-[#E6E6E6]", className)}>
      <Toaster position="top-center" />
      {/* Header with Rating and Review Form - Added gray background */}
      <div className="bg-[#F8F8FA] p-5 mb-5">
        <div className="flex gap-[10px] items-center mb-10 flex-col md:flex-row  md:gap-[60px] ">
          <div className="flex gap-2 items-center">
            <h3 className="text-base font-bold text-[#2C2F24]">Start a Review</h3>
            {/* Rating stars with selection functionality - Made more prominent */}
            <div className="flex gap-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <div 
                  key={star} 
                  className={`text-3xl cursor-pointer ${selectedRating >= star ? 'text-[#FFD700]' : 'text-[#D9D9D9]'}`}
                  onClick={() => {
                    if (!isAuthenticated()) {
                      toast.error('Please log in to rate this vendor');
                      return;
                    }
                    if (selectedRating === star) {
                      setSelectedRating(star - 1); // Unselect this and all to the right
                    } else {
                      setSelectedRating(star); // Select up to this one
                    }
                    console.log('Selected rating:', selectedRating === star ? star - 1 : star);
                  }}
                  title={`Rate ${star} star`}
                >
                  ★
                </div>
              ))}
            </div>

          </div>
          
          <div className="flex items-center gap-5">
            <h3 className="text-base font-bold text-[#2C2F24]">Reviews & Rating</h3>
            {reviewsData.length > 0 ? (
              <div className="bg-[#008001] text-white px-4 py-2.5 rounded-md text-[22px] font-black">{averageRating}</div>
            ) : (
              <div className="text-gray-500 text-sm">No ratings yet</div>
            )}
          </div>
        </div>
        
        {/* Review Input - Only show if authenticated */}
        {isAuthenticated() ? (
          hasAlreadyReviewed ? (
            <div className="bg-amber-50 border border-amber-200 text-amber-800 p-4 rounded-lg mb-4">
              <div className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9z" clipRule="evenodd" />
                </svg>
                <span className="font-medium">You have already submitted a review for this vendor.</span>
              </div>
              <p className="mt-2 text-sm">You cannot submit multiple reviews for the same vendor. Thank you for your feedback!</p>
            </div>
          ) : (
            <>
              <div className="mb-5">
                <textarea 
                  placeholder="Write A Comment (Max 300 Character)" 
                  className="w-full h-[116px] border border-[#D8D8D8] rounded-md p-4 font-lato text-base text-[#ADADAD] resize-none"
                  value={reviewText}
                  onChange={(e) => setReviewText(e.target.value)}
                  maxLength={300}
                />
              </div>
              
              {/* Submit Button - Positioned outside the textarea container for better visibility */}
              <div className="flex justify-between items-center mb-5">
                <span className="text-xs text-gray-500">{reviewText.length}/300 characters</span>
                <button 
                  className={`px-6 py-2.5 rounded-md text-white font-medium text-base ${submitting ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#008001] hover:bg-[#006801]'}`}
                  onClick={handleSubmitReview}
                  disabled={submitting}
                >
                  {submitting ? 'Submitting...' : 'Submit Review'}
                </button>
              </div>
            </>
          )
        ) : (
          <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-md mb-5">
            <p className="text-center text-yellow-700">Please log in to write a review</p>
          </div>
        )}
      </div>
      
      {/* All Reviews Section */}
      <div>
        <h3 className="text-base font-bold text-[#2C2F24] mb-5">All Reviews</h3>
        
        {loading && (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#008001]"></div>
          </div>
        )}
        
        {error && (
          <div className="text-red-500 mb-4">{error}</div>
        )}
        
        {!loading && !error && reviewsData.length === 0 && (
          <div className="flex flex-col items-center justify-center py-10">
            <div className="text-6xl mb-4">📝</div>
            <p className="text-lg text-gray-600 font-medium mb-2">No Reviews Yet</p>
            <p className="text-sm text-gray-400">Be the first one to review this vendor!</p>
          </div>
        )}
        
        {reviewsData.map((review, index) => (
          <React.Fragment key={review.id}>
            <div className="relative py-5">
              {/* Fixed layout: Reviewer info at top, review content below */}
              <div className="flex justify-between items-start">
                {/* Reviewer Info */}
                <div className="flex items-center gap-4">
                  {/* Circular avatar with user's first letter */}
                  <div 
                    className="w-[42px] h-[42px] rounded-full bg-[#E87616] text-white flex items-center justify-center font-bold text-lg"
                    aria-label={review.name}
                  >
                    {review.name ? review.name.charAt(0).toUpperCase() : '?'}
                  </div>
                  <span className="text-base font-semibold text-[#2C2F24]">{review.name}</span>
                </div>
                
                {/* Rating Badge */}
                <div className="bg-[#008001] text-white py-1 px-2.5 rounded font-bold text-sm">
                  ★ {review.rating}
                </div>
              </div>
              
              {/* Review Content - Now positioned below the reviewer name */}
              <div className="mt-3">
                <div className="text-sm italic text-[#9B9B9B] mb-2.5">{review.date}</div>
                <p className="text-sm text-[#9B9B9B] leading-relaxed">{review.text}</p>
                
                {/* Flag indicator if review is flagged */}
                {/* {review.isFlagged && (
                  <div className="mt-2 text-xs text-amber-600 bg-amber-50 p-1.5 rounded border border-amber-200">
                    <span className="font-medium">Note:</span> {review.flagMessage || 'This review has been flagged for review.'}
                  </div>
                )} */}
                
                
                {/* Review Actions */}
              
                {/* <div className="flex gap-6 mt-3.5">
                  <div className="flex items-center gap-2.5 text-sm text-[#9B9B9B] cursor-pointer">
                  <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 10 11" fill="none">
  <g clip-path="url(#clip0_1622_9638)">
    <path d="M9.7065 6.3065C9.7065 6.0665 9.61983 5.8465 9.4665 5.67316C9.79316 5.51316 9.99983 5.17316 9.99983 4.81316C9.99983 4.2865 9.5665 3.85316 9.03983 3.85316H6.47983C6.43316 3.85316 6.39316 3.81316 6.39316 3.7665V3.43316C6.59316 3.0465 6.73316 2.3865 6.7265 1.81316C6.7265 1.35316 6.63316 0.726497 6.21983 0.333164C5.97983 0.0998307 5.71983 -0.00683594 5.3865 -0.00683594C4.93983 -0.00683594 4.57983 0.353164 4.57983 0.799831V1.45316C4.57983 2.07983 4.27316 2.4465 3.9465 2.83316V2.8465C3.75983 3.05983 3.57983 3.27316 3.43983 3.53983C3.3065 3.77983 3.11316 3.9465 2.8465 4.01983L2.6265 4.07983C2.47983 3.91983 2.27983 3.8265 2.05983 3.8265H0.746497C0.333164 3.8265 -0.00683594 4.1665 -0.00683594 4.57983V9.4865C-0.00683594 9.89983 0.333164 10.2398 0.746497 10.2398H2.05983C2.27316 10.2398 2.47983 10.1465 2.61983 9.9865L3.39983 10.1865C3.5665 10.2332 3.7265 10.2532 3.9065 10.2532H8.1465C8.67316 10.2532 9.1065 9.81983 9.1065 9.29316C9.1065 9.05316 9.01983 8.83316 8.8665 8.65983C9.19316 8.49983 9.39983 8.15983 9.39983 7.79983C9.39983 7.55983 9.31316 7.33983 9.15983 7.1665C9.4865 7.0065 9.69316 6.6665 9.69316 6.3065H9.7065ZM8.4465 8.33983H8.15316C8.03316 8.33983 7.93983 8.43316 7.93983 8.55316C7.93983 8.67316 8.03316 8.7665 8.15316 8.7665C8.4465 8.7665 8.6865 9.0065 8.6865 9.29983C8.6865 9.59316 8.4465 9.83316 8.15316 9.83316H3.91316C3.77316 9.83316 3.65316 9.81983 3.51983 9.77983L2.81983 9.59316C2.81983 9.55983 2.8265 9.51983 2.8265 9.4865V4.57983C2.8265 4.5465 2.8265 4.5065 2.81983 4.47316L2.97316 4.4265C3.35316 4.31983 3.63983 4.0865 3.8265 3.73983C3.95316 3.5065 4.1265 3.29316 4.2865 3.1065C4.6465 2.67316 5.01316 2.2265 5.01316 1.45316V0.799831C5.01316 0.586497 5.1865 0.419831 5.39316 0.419831C5.59983 0.419831 5.77316 0.486497 5.93316 0.639831C6.23316 0.933164 6.3065 1.43983 6.3065 1.81983C6.3065 2.41983 6.15983 3.0065 5.99983 3.27316C5.97983 3.3065 5.97316 3.33983 5.97316 3.37983V3.7665C5.97316 4.0465 6.19983 4.27316 6.47983 4.27316H9.03983C9.33317 4.27316 9.57316 4.51316 9.57316 4.8065C9.57316 5.09983 9.3265 5.33983 9.03316 5.3465H8.73983C8.61983 5.3465 8.5265 5.43983 8.5265 5.55983C8.5265 5.67983 8.61983 5.77316 8.73983 5.77316C9.03316 5.77316 9.27316 6.01316 9.27316 6.3065C9.27316 6.59983 9.03316 6.83983 8.73983 6.83983H8.4465C8.3265 6.83983 8.23316 6.93316 8.23316 7.05316C8.23316 7.17316 8.3265 7.2665 8.4465 7.2665C8.73983 7.2665 8.97983 7.5065 8.97983 7.79983C8.97983 8.09316 8.73983 8.33316 8.4465 8.33316V8.33983ZM2.39983 4.5865V9.49316C2.39983 9.67316 2.25316 9.81983 2.07316 9.81983H0.759831C0.579831 9.81983 0.433164 9.67316 0.433164 9.49316V4.5865C0.433164 4.4065 0.579831 4.25983 0.759831 4.25983H2.07316C2.25316 4.25983 2.39983 4.4065 2.39983 4.5865Z" fill="#9B9B9B"/>
  </g>
  <defs>
    <clipPath id="clip0_1622_9638">
      <rect width="10" height="10.26" fill="white"/>
    </clipPath>
  </defs>
</svg>                    <span>Helpful</span>
                  </div>
                  <div className="flex items-center gap-2.5 text-sm text-[#9B9B9B] cursor-pointer">
                  <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 10 10" fill="none">
  <g clip-path="url(#clip0_1622_9673)">
    <path d="M9.06169 0H0.943494C0.419907 0 0 0.419907 0 0.93831V6.93624C0 7.45464 0.419907 7.87973 0.943494 7.87973H4.21462V8.95283C4.21462 9.02022 4.25609 9.08243 4.3183 9.10835C4.33904 9.11871 4.35977 9.11871 4.38051 9.11871C4.42198 9.11871 4.46864 9.10316 4.49974 9.07206L5.69207 7.87973H9.06687C9.58528 7.87973 10.0052 7.45982 10.0052 6.93624V0.93831C10.0052 0.419907 9.58528 0 9.06687 0H9.06169ZM9.66822 6.93624C9.66822 7.2732 9.39865 7.54277 9.06169 7.54277H5.61949C5.57284 7.54277 5.53136 7.55832 5.50026 7.58942L4.5464 8.54329V7.70347C4.5464 7.61016 4.47382 7.53758 4.38051 7.53758H0.943494C0.606532 7.53758 0.336962 7.26283 0.336962 6.93105V0.93831C0.336962 0.606532 0.611716 0.331778 0.943494 0.331778H9.06687C9.39865 0.331778 9.67341 0.601348 9.67341 0.93831V6.93624H9.66822Z" fill="#9B9B9B"/>
    <path d="M8.2529 1.77832H1.74694C1.65363 1.77832 1.58105 1.8509 1.58105 1.94421C1.58105 2.03752 1.65363 2.1101 1.74694 2.1101H8.2529C8.34622 2.1101 8.41879 2.03752 8.41879 1.94421C8.41879 1.8509 8.34622 1.77832 8.2529 1.77832Z" fill="#9B9B9B"/>
    <path d="M8.2529 3.13623H1.74694C1.65363 3.13623 1.58105 3.20881 1.58105 3.30212C1.58105 3.39543 1.65363 3.46801 1.74694 3.46801H8.2529C8.34622 3.46801 8.41879 3.39543 8.41879 3.30212C8.41879 3.20881 8.34622 3.13623 8.2529 3.13623Z" fill="#9B9B9B"/>
    <path d="M8.2529 4.49951H1.74694C1.65363 4.49951 1.58105 4.57209 1.58105 4.6654C1.58105 4.75871 1.65363 4.83129 1.74694 4.83129H8.2529C8.34622 4.83129 8.41879 4.75871 8.41879 4.6654C8.41879 4.57209 8.34622 4.49951 8.2529 4.49951Z" fill="#9B9B9B"/>
    <path d="M6.40739 5.85791H1.74694C1.65363 5.85791 1.58105 5.93049 1.58105 6.0238C1.58105 6.11711 1.65363 6.18969 1.74694 6.18969H6.40739C6.5007 6.18969 6.57328 6.11711 6.57328 6.0238C6.57328 5.93049 6.5007 5.85791 6.40739 5.85791Z" fill="#9B9B9B"/>
  </g>
  <defs>
    <clipPath id="clip0_1622_9673">
      <rect width="10" height="9.11871" fill="white"/>
    </clipPath>
  </defs>
</svg>                    <span>Comment</span>
                  </div>
                  <div className="flex items-center gap-2.5 text-sm text-[#9B9B9B] cursor-pointer">
                  <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 10 8" fill="none">
  <g clip-path="url(#clip0_1622_9700)">
    <path d="M9.86 3.39313L6.63333 0.166465C6.41333 -0.0535352 6.04667 -0.0535352 5.82667 0.166465C5.72 0.273132 5.66 0.419798 5.66 0.573132V1.6998C4.18 1.7798 2.79333 2.3998 1.74667 3.44647C0.686667 4.50646 0.0666667 5.91313 0 7.41313C0 7.5398 0.0933333 7.64646 0.22 7.65313C0.22 7.65313 0.226667 7.65313 0.233333 7.65313C0.306667 7.65313 0.373333 7.6198 0.413333 7.5598C1.64 5.97313 3.69333 5.2198 5.65333 5.63313V6.8998C5.65333 7.05313 5.71333 7.19313 5.82 7.30647C5.92667 7.41313 6.07333 7.47313 6.22667 7.47313C6.38 7.47313 6.52 7.41313 6.63333 7.30647L9.86 4.0798C10.0467 3.89313 10.0467 3.5798 9.86 3.39313ZM5.94 5.22647C5.48667 5.11313 5.02 5.0598 4.56667 5.0598C3.07333 5.0598 1.62 5.64647 0.54 6.7198C0.993333 4.10647 3.22 2.19313 5.88667 2.15313C6.01333 2.15313 6.11333 2.04646 6.11333 1.9198V0.573132C6.11333 0.573132 6.12667 0.513132 6.14667 0.493132C6.16667 0.473132 6.19333 0.459798 6.22667 0.459798C6.22667 0.459798 6.28 0.473132 6.30667 0.493132L9.53333 3.7198C9.53333 3.7198 9.54 3.74646 9.53333 3.75313L6.30667 6.9798C6.30667 6.9798 6.26 7.01313 6.22667 7.01313C6.22667 7.01313 6.16667 6.9998 6.14667 6.9798C6.12667 6.9598 6.11333 6.93313 6.11333 6.90647V5.45313C6.11333 5.34647 6.04 5.25313 5.94 5.22647Z" fill="#9B9B9B"/>
  </g>
  <defs>
    <clipPath id="clip0_1622_9700">
      <rect width="10" height="7.65333" fill="white"/>
    </clipPath>
  </defs>
</svg>                    <span>Share</span>
                  </div>
                </div>
               */}
              </div>
            </div>
            
            {/* Divider */}
            {index < reviewsData.length - 1 && (
              <div className="h-px bg-[#D9D9D9] my-5"></div>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

export default ReviewTabContent;
