/**
 * Enquiry Form Component
 * 
 * A form component that submits contact inquiries to the Supabase database.
 * Includes validation and error handling.
 * Updated to store submissions in the contact_inquiries table without subject field.
 */

import React, { useState } from "react";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, <PERSON><PERSON>T<PERSON>le, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import toast from "react-hot-toast";
import { submitContactForm } from "../../services/contactService";

interface EnquiryFormProps {
  trigger?: React.ReactNode;
  isOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export const EnquiryForm: React.FC<EnquiryFormProps> = ({ 
  trigger, 
  isOpen, 
  onOpenChange 
}) => {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone_number: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    
    // For phone_number field, only allow numeric input and limit to 10 digits
    if (name === 'phone_number') {
      // Check if the input is numeric
      if (!/^\d*$/.test(value)) {
        // If non-numeric characters are entered, don't update the state
        return;
      }
      
      // Limit to 10 digits
      if (value.length > 10) {
        return;
      }
    }
    
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      newErrors.name = "Name is required";
    }
    
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }
    
    if (!formData.phone_number.trim()) {
      newErrors.phone_number = "Phone number is required";
    } else if (!/^\d+$/.test(formData.phone_number)) {
      newErrors.phone_number = "Phone number must contain only digits";
    } else if (formData.phone_number.length !== 10) {
      newErrors.phone_number = "Phone number must be 10 digits";
    }
    
    if (!formData.message.trim()) {
      newErrors.message = "Message is required";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      const { data, error } = await submitContactForm(formData);
      
      if (error) {
        toast.error(error.message || "Failed to send your message. Please try again.");
        console.error("Form submission error:", error);
      } else {
        toast.success("Your message has been sent successfully!");
        
        // Reset form
        setFormData({
          name: "",
          email: "",
          phone_number: "",
          message: "",
        });
        
        // Close dialog if onOpenChange is provided
        if (onOpenChange) {
          onOpenChange(false);
        }
      }
    } catch (error) {
      console.error("Unexpected error:", error);
      toast.error("An unexpected error occurred. Please try again later.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">Send us a Message</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4 mt-4">
          <div>
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              placeholder="Your name"
              className={errors.name ? "border-red-500" : ""}
            />
            {errors.name && (
              <p className="text-red-500 text-sm mt-1">{errors.name}</p>
            )}
          </div>
          
          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Your email"
              className={errors.email ? "border-red-500" : ""}
            />
            {errors.email && (
              <p className="text-red-500 text-sm mt-1">{errors.email}</p>
            )}
          </div>
          
          <div>
            <Label htmlFor="phone_number">Phone</Label>
            {/* <Input
              id="phone_number"
              name="phone_number"
              value={formData.phone_number}
              onChange={handleChange}
              placeholder="Your phone number"
              className={errors.phone_number ? "border-red-500" : ""}
              type="tel"
              inputMode="numeric"
            /> */}
         <input
  id="phone_number"
  name="phone_number"
  value={formData.phone_number}
  onChange={handleChange}
  placeholder="Your phone number"
  type="number"
  inputMode="numeric"
  className={`w-full px-3 py-2 border rounded-md outline-none ring-0
    focus-visible:ring-2 focus-visible:ring-[rgb(0,0,0)]
    active:ring-2 active:ring-[rgb(0,0,0)]
    ${errors.phone_number ? "border-red-500" : "border-gray-300"}`}
/>


            {errors.phone_number && (
              <p className="text-red-500 text-sm mt-1">{errors.phone_number}</p>
            )}
          </div>
          
          <div>
            <Label htmlFor="message">Message</Label>
            <Textarea
              id="message"
              name="message"
              value={formData.message}
              onChange={handleChange}
              placeholder="Your message"
              rows={4}
              className={errors.message ? "border-red-500" : ""}
            />
            {errors.message && (
              <p className="text-red-500 text-sm mt-1">{errors.message}</p>
            )}
          </div>
          
          <Button 
            type="submit" 
            className="w-full bg-[#E87616] hover:bg-[#D56A14] text-white"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Sending..." : "Send Message"}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EnquiryForm;
