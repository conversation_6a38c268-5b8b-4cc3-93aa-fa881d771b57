import { Layout } from "@/components/layout/Layout";
import { Hero } from "@/components/home/<USER>";
import { ServiceSection } from "@/components/home/<USER>";
import { images } from "@/assets/images/placeholder";
import { Helmet } from "react-helmet";
import { useEffect, useState } from "react";
import { FooterMenuItem, getFooterMenuItems } from "@/services/footerMenuService";

const Index = () => {
  const [menuItems, setMenuItems] = useState<FooterMenuItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchMenuItems = async () => {
      try {
        setLoading(true);
        const items = await getFooterMenuItems();
        setMenuItems(items);
      } catch (err) {
        console.error("Error fetching menu items:", err);
        setError("Failed to load menu items");
      } finally {
        setLoading(false);
      }
    };

    fetchMenuItems();
  }, []);

  return (
    <>
      <Helmet>
        <link
          href="https://fonts.googleapis.com/css2?family=Lato:wght@400;500;700;900&family=DM+Sans:wght@400;500;700&display=swap"
          rel="stylesheet"
        />
      </Helmet>

      <Layout>
        <Hero />

        <div className="max-w-[1120px] mx-auto my-[60px] max-md:px-5">
          <ServiceSection
            title="Home Chefs"
            subtitle="CUSTOMIZED HOME FOOD"
            description="Order Delicious & Healthy HomeMade Meals Customized as per Your Taste Preference by Talented Home Chefs."
            imageSrc={images.homeChef}
            imageAlt="Home Chef"
          />

          <ServiceSection
            title="Tiffin Suppliers"
            subtitle="WEEKLY & MONTHLY MEALS"
            description="Order Healthy & Nutritious Homemade Tiffin Meals by Certified Tiffin Suppliers in their Hygienic Home Kitchens."
            imageSrc={images.tiffin}
            imageAlt="Tiffin"
          />

          <ServiceSection
            title="Caterer"
            subtitle="PARTY & BULK ORDERS"
            description="Make your Events Unforgettable with the Widest Variety of Meals Cooked by Verified Caterers at affordable Cost."
            imageSrc={images.caterer}
            imageAlt="Caterer"
          />
        </div>
        
        {/* Dynamic Menu Section */}
        <div className="bg-[#F0F0F0] py-[30px] text-center">
          <div className="max-w-[1120px] mx-auto px-4">
            <h3 className="text-xl font-bold text-[#323232] mb-4">Discover Home-Cooked Goodness</h3>
            
            {loading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#323232]"></div>
              </div>
            ) : error ? (
              <div className="text-red-500 py-4">{error}</div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-[#323232]">
                {menuItems.length > 0 ? (
                  menuItems.map((item) => (
                    <div key={item.id} className="p-4 bg-white rounded-lg shadow-sm">
                      <a 
                        href={item.menu_url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="font-semibold mb-2 hover:text-[#FF5A5F] transition-colors"
                      >
                        {item.menu_name}
                      </a>
                    </div>
                  ))
                ) : (
                  <div className="col-span-3 text-center py-4">
                    <p>No menu items available</p>
                  </div>
                )}
              </div>
            )}
            
            {/* <p className="mt-6 text-sm text-[#323232] max-w-[800px] mx-auto">
              HomeFoodi connects food lovers with local culinary talent. Our platform ensures quality, hygiene, and authentic flavors in every meal delivered to your doorstep.
            </p> */}
          </div>
        </div>
      </Layout>
    </>
  );
};

export default Index;
