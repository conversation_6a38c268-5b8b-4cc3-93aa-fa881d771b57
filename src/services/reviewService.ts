/**
 * Review Service
 * 
 * This file contains functions for fetching review data from the database
 * and associating it with user information.
 */

import { supabase } from '@/lib/supabase';

/**
 * Interface for review data with user information
 */
export interface ReviewWithUserInfo {
  id: string;
  name: string;
  date: string;
  text: string;
  rating: number;
  image: string | null;
  isFlagged: boolean;
  flagMessage: string | null;
  createdAt: string;
  updatedAt: string | null;
}

/**
 * Fetch vendor reviews with user information
 * @param vendorId - ID of the vendor
 * @returns Promise with array of review items with user information
 */
export const fetchVendorReviews = async (vendorId: string): Promise<ReviewWithUserInfo[]> => {
  try {
    console.log('ReviewService: Fetching reviews for vendor ID:', vendorId);
    
    if (!vendorId) {
      console.error('ReviewService: No vendor ID provided');
      return [];
    }
    
    // Fetch reviews for the vendor
    console.log('ReviewService: Executing query for vendor ID:', vendorId);
    
    // Log the exact query we're about to execute
    console.log(`ReviewService: Query: SELECT * FROM reviews WHERE vendor_id = '${vendorId}'`);
    
    // Execute the query with filters to show only pending reviews
    const { data: reviewsData, error: reviewsError } = await supabase
      .from('reviews')
      .select('id, user_id, rating, review, created_at, updated_at, vendor_current_time, is_flagged, flag_message, removal_request_status, status')
      .eq('vendor_id', vendorId)
       .eq('removal_request_status', 'pending')
      .not('status', 'eq', 'deleted')
      .order('created_at', { ascending: false });
    
    console.log('ReviewService: Raw reviews data:', reviewsData);

    if (reviewsError) {
      console.error('Error fetching reviews:', reviewsError);
      return [];
    }

    if (!reviewsData || reviewsData.length === 0) {
      console.log('ReviewService: No reviews found for vendor ID:', vendorId);
      console.log('ReviewService: Query returned empty result');
      return [];
    }

    // Extract user IDs from reviews
    const userIds = reviewsData.map(review => review.user_id);
    console.log('ReviewService: User IDs found in reviews:', userIds);

    // Fetch user information for these IDs
    const { data: usersData, error: usersError } = await supabase
      .from('users')
      .select('id, name')
      .in('id', userIds);
      
    console.log('ReviewService: User data retrieved:', usersData);

    if (usersError) {
      console.error('Error fetching user data:', usersError);
      return [];
    }

    // Create a map of user data for quick lookup
    const userMap: Record<string, any> = {};
    usersData?.forEach(user => {
      userMap[user.id] = user;
    });

    // Combine review data with user data
    const reviewsWithUserInfo = reviewsData.map(review => {
      const user = userMap[review.user_id] || { name: 'Anonymous' };
      
      // Convert to Indian Standard Time (IST) - UTC+5:30
      const utcDate = new Date(review.created_at);
      
      // Add 5 hours and 30 minutes to convert to IST
      const istDate = new Date(utcDate.getTime() + (5.5 * 60 * 60 * 1000));
      
      // Format hours in 12-hour format with am/pm
      let hours = istDate.getHours();
      const ampm = hours >= 12 ? 'pm' : 'am';
      hours = hours % 12;
      hours = hours ? hours : 12; // the hour '0' should be '12'
      
      // Format the date in "14 April 2025 - 7:51 am" format for IST
      const formattedDate = `${istDate.getDate()} ${istDate.toLocaleString('en-IN', { month: 'long' })} ${istDate.getFullYear()} - ${hours}:${String(istDate.getMinutes()).padStart(2, '0')} ${ampm}`;
      
      return {
        id: review.id,
        name: user.name,
        date: formattedDate,
        text: review.review,
        rating: review.rating,
        image: '/lovable-uploads/9d0a6a4e-00d4-4dfd-8ab1-bf5b7882b3a5.png', // Default image since profile_image doesn't exist // Default image if none available
        isFlagged: review.is_flagged,
        flagMessage: review.flag_message,
        createdAt: review.created_at,
        updatedAt: review.updated_at || null
      };
    });

    console.log('ReviewService: Final processed reviews:', reviewsWithUserInfo);
    return reviewsWithUserInfo;
  } catch (error) {
    console.error('Error in fetchVendorReviews:', error);
    
    // For testing, return mock data when there's an error
    return [
      {
        id: 'error-mock-1',
        name: "Error Test User",
        date: "24 March 2025 - 4:00 pm",
        text: "This is a fallback review shown when an error occurs.",
        rating: 3.5,
        image: "/lovable-uploads/9d0a6a4e-00d4-4dfd-8ab1-bf5b7882b3a5.png",
        isFlagged: false,
        flagMessage: null,
        createdAt: new Date().toISOString(),
        updatedAt: null
      }
    ];
  }
};

/**
 * Check if a user has already reviewed a vendor
 * @param vendorId - The ID of the vendor
 * @param userId - The ID of the user
 * @returns Promise with boolean indicating if user has already reviewed
 */
export const hasUserReviewedVendor = async (
  vendorId: string,
  userId: string
): Promise<boolean> => {
  try {
    if (!vendorId || !userId) {
      console.error('ReviewService: Missing required parameters for hasUserReviewedVendor');
      return false;
    }

    console.log('ReviewService: Checking if user has already reviewed vendor');
    console.log('ReviewService: Vendor ID:', vendorId);
    console.log('ReviewService: User ID:', userId);

    // Query the database to check if this user has already reviewed this vendor
    const { data, error, count } = await supabase
      .from('reviews')
      .select('id', { count: 'exact' })
      .eq('vendor_id', vendorId)
      .eq('user_id', userId)
      .not('status', 'eq', 'deleted');

    if (error) {
      console.error('ReviewService: Error checking for existing review:', error);
      return false;
    }

    console.log('ReviewService: Existing review count:', count);
    return count !== null && count > 0;
  } catch (error) {
    console.error('ReviewService: Exception checking for existing review:', error);
    return false;
  }
};

/**
 * Submit a new review for a vendor
 * @param vendorId - The ID of the vendor being reviewed
 * @param userId - The ID of the user submitting the review
 * @param rating - The rating given (1-5)
 * @param reviewText - The text content of the review
 * @returns The result of the submission operation
 */
export const submitReview = async (
  vendorId: string,
  userId: string,
  rating: number,
  reviewText: string
): Promise<{ success: boolean; error?: any; data?: any }> => {
  try {
    if (!vendorId || !userId) {
      console.error('ReviewService: Missing required parameters for submitReview');
      return { success: false, error: 'Missing required parameters' };
    }

    console.log('ReviewService: Submitting review for vendor ID:', vendorId);
    console.log('ReviewService: User ID:', userId);
    console.log('ReviewService: Rating:', rating);
    console.log('ReviewService: Review text:', reviewText);

    // Check if user has already reviewed this vendor
    const hasReviewed = await hasUserReviewedVendor(vendorId, userId);
    if (hasReviewed) {
      console.log('ReviewService: User has already reviewed this vendor');
      return { 
        success: false, 
        error: { 
          message: 'You have already submitted a review for this vendor. You cannot submit multiple reviews.' 
        } 
      };
    }

    // Generate a UUID for the review
    const reviewId = crypto.randomUUID();
    const currentTime = new Date().toISOString();

    // Insert the review into the database
    const { data, error } = await supabase
      .from('reviews')
      .insert([
        {
          id: reviewId,
          user_id: userId,
          vendor_id: vendorId,
          rating: rating,
          review: reviewText,
          created_at: currentTime,
          updated_at: currentTime,
          status: 'active'
        }
      ]);

    if (error) {
      console.error('ReviewService: Error submitting review:', error);
      return { success: false, error };
    }

    console.log('ReviewService: Review submitted successfully:', data);
    return { success: true, data };
  } catch (error) {
    console.error('ReviewService: Exception submitting review:', error);
    return { success: false, error };
  }
};
