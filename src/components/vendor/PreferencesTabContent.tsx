/**
 * PreferencesTabContent Component
 * 
 * Updated to fetch and display dynamic preferences data based on vendor ID.
 * Retrieves preferences from the preferences table in the database.
 * Modified to only show preferences when they are available, with no static fallbacks.
 */

import React, { useState, useEffect } from "react";
import { Plus, Check } from "lucide-react";
import { cn } from "@/lib/utils";
import { fetchVendorPreferences } from "@/services/vendorService";
import { useParams } from "react-router-dom";

interface PreferenceItem {
  name: string;
  isActive?: boolean;
}

interface PreferencesTabContentProps {
  className?: string;
  preferences?: PreferenceItem[];
  vendorId?: string;
}

const PreferencesTabContent = ({ 
  className, 
  preferences: initialPreferences,
  vendorId
}: PreferencesTabContentProps) => {
  const { id } = useParams<{ id: string }>();
  const [preferences, setPreferences] = useState<PreferenceItem[]>(initialPreferences || []);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  useEffect(() => {
    const fetchPreferences = async () => {
      // If preferences are already provided as props, use them
      if (initialPreferences && initialPreferences.length > 0) {
        setPreferences(initialPreferences);
        return;
      }
      
      // Otherwise fetch from the database
      try {
        setLoading(true);
        // Use the vendorId from props or from the URL params
        const effectiveVendorId = vendorId || id;
        
        if (!effectiveVendorId) {
          throw new Error('Vendor ID is required');
        }
        
        // Fetch preferences data
        const preferencesData = await fetchVendorPreferences(effectiveVendorId);
        console.log('Fetched preferences data:', preferencesData);
        
        if (preferencesData && preferencesData.length > 0) {
          setPreferences(preferencesData);
        } else {
          // No fallback to static preferences, just set empty array
          setPreferences([]);
        }
      } catch (err) {
        console.error('Error fetching preferences data:', err);
        setError('Failed to load preferences data');
        // No fallback to static preferences, just set empty array
        setPreferences([]);
      } finally {
        setLoading(false);
      }
    };
    
    fetchPreferences();
  // Only run once on mount
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  
  return (
    <div className={cn("mb-10 md:mb-20 mt-[20px] md:mt-[30px] border-t border-[#E6E6E6] px-3 md:px-0", className)}>
      <h2 className="text-lg md:text-xl font-bold text-[#2C2F24] mb-4 md:mb-8 mt-[20px] md:mt-[30px]">Select Your Preference</h2>
      
      {loading && (
        <div className="flex justify-center items-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#008001]"></div>
        </div>
      )}
      
      {error && (
        <div className="text-red-500 mb-4">{error}</div>
      )}
      
      {preferences.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
          {preferences.map((preference, index) => (
            <div 
              key={index} 
              className={cn(
                "flex justify-between items-center p-3 border border-[#D9D9D9] rounded-md",
                preference.isActive && "border-[#008001] border-opacity-50"
              )}
            >
              <span 
                className={cn(
                  "text-base font-semibold", 
                  preference.isActive ? "text-[#008001]" : "text-[#9B9B9B]"
                )}
              >
                {preference.name}
              </span>
              {preference.isActive ? (
                <div className="h-6 w-6 bg-[#008001] rounded-full flex items-center justify-center">
                  <Check className="h-4 w-4 text-white" />
                </div>
              ) : (
                <Plus className="h-5 w-5 text-[#9B9B9B]" />
              )}
            </div>
          ))}
        </div>
      ) : (
        !loading && (
          <div className="text-center py-8 text-gray-500">
            No preferences available for this vendor.
          </div>
        )
      )}
    </div>
  );
};

export default PreferencesTabContent;
