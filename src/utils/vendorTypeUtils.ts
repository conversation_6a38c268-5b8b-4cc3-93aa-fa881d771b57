/**
 * Vendor Type Utility Functions
 * 
 * This file contains utility functions for handling vendor types in the HomeFoodi application.
 * It provides mapping between different vendor type formats in the database and application.
 */

// Define vendor type mapping to handle different formats in the database
export const VENDOR_TYPE_MAPPING: Record<string, string> = {
  // Home Chef variations
  'home_chef': 'home-chef',
  'home-chef': 'home-chef',
  'homechef': 'home-chef',
  // Tiffin variations
  'tiffin_supplier': 'tiffin_supplier',
  'tiffin_service': 'tiffin_supplier',
  'tiffin': 'tiffin_supplier',
  // Caterer variations
  'caterer': 'caterer',
  'catering': 'caterer',
  'caters': 'caterer'
};

// Define database table mapping for each vendor type
export const VENDOR_TABLE_MAPPING: Record<string, string> = {
  'home-chef': 'home_chef',
  'tiffin_supplier': 'tiffin_service_menu',
  'caterer': 'caterer_menu'
};

/**
 * Normalize vendor type from database to application format
 * @param dbVendorType - Vendor type as stored in the database
 * @returns Normalized vendor type for application use
 */
export const normalizeVendorType = (dbVendorType: string): string => {
  // For query purposes, we want to use the original type
  if (dbVendorType === 'home-chef' || dbVendorType === 'tiffin_supplier' || dbVendorType === 'caterer') {
    return dbVendorType;
  }
  
  // For internal application use, normalize to standard format
  return VENDOR_TYPE_MAPPING[dbVendorType] || 'home-chef'; // Default to home-chef if unknown
};

/**
 * Get the display name for a vendor type
 * @param vendorType - Normalized vendor type
 * @returns Display name for the vendor type
 */
export const getVendorTypeDisplay = (vendorType: string): string => {
  switch(vendorType) {
    case 'home-chef': return 'Home Chefs';
    case 'tiffin_supplier': return 'Tiffin Suppliers';
    case 'caterer': return 'Caterer';
    default: return 'Home Chefs';
  }
};

/**
 * Get the database table name for a vendor type
 * @param vendorType - Normalized vendor type
 * @returns Database table name for the vendor type
 */
export const getVendorTableName = (vendorType: string): string => {
  // For table lookups, we want to use the normalized type directly
  return VENDOR_TABLE_MAPPING[vendorType] || 'home_chef';
};

/**
 * Get the current vendor type from localStorage
 * @returns The original vendor type as stored in localStorage
 */
export const getCurrentVendorType = (): string => {
  // Return the original vendor type from localStorage without normalization
  // This ensures we use the exact value stored in localStorage for queries
  return localStorage.getItem('vendor_type') || 'home-chef';
};

/**
 * Get the current vendor type display name from localStorage
 * @returns The current vendor type display name
 */
export const getCurrentVendorTypeDisplay = (): string => {
  const vendorTypeDisplay = localStorage.getItem('vendor_type_display');
  if (vendorTypeDisplay) return vendorTypeDisplay;
  
  const vendorType = getCurrentVendorType();
  return getVendorTypeDisplay(vendorType);
};
