'use client';

import React, { useState, useEffect } from "react";
import { Layout } from "@/components/layout/Layout";
import { Button } from "@/components/ui/button";
import { Filter, Star, MapPin, ChevronDown } from "lucide-react";
import { ActionButtons } from "@/components/ui/action-buttons";
import FilterDialog, { FilterState, FilterCategory } from "@/components/filters/FilterDialog";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { getCurrentVendorType, getCurrentVendorTypeDisplay } from "@/utils/vendorTypeUtils";
import { useVendors } from "@/hooks/useVendors";
import { BaseVendor } from "@/services/vendorService";
import { calculateDistance } from "@/utils/distanceUtils";

type Props = {
  searchParams: { [key: string]: string | string[] | undefined };
};

export default function ServiceListingClient({ searchParams }: Props) {
  const router = useRouter();
  const vendorType = searchParams.type as string || getCurrentVendorType();
  const city = searchParams.city as string;

  // State management
  const [appliedFilters, setAppliedFilters] = useState<FilterState>({
    priceRange: [0, 1000],
    rating: 0,
    distance: 50,
    categories: [],
    sortBy: "distance"
  });

  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [sortBy, setSortBy] = useState<string>("distance");
  const [showSortDropdown, setShowSortDropdown] = useState(false);

  // Fetch vendors
  const { 
    data: fetchedVendors = [], 
    isLoading, 
    error 
  } = useVendors(vendorType);

  // Filter vendors
  const filteredVendors = fetchedVendors.filter(vendor => 
    vendor.coordinates && 
    vendor.coordinates.latitude && 
    vendor.coordinates.longitude && 
    !isNaN(vendor.coordinates.latitude) && 
    !isNaN(vendor.coordinates.longitude)
  );

  // Apply filters and sorting
  const currentVendors = filteredVendors
    .filter(vendor => {
      // Apply filters here
      return true; // Simplified for now
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "rating":
          return (b.rating || 0) - (a.rating || 0);
        case "distance":
          return (a.distance || 0) - (b.distance || 0);
        case "name":
          return a.name.localeCompare(b.name);
        default:
          return 0;
      }
    });

  const handleVendorClick = (vendor: BaseVendor) => {
    const cityParam = city || vendor.location?.split(',')[0]?.trim() || 'Unknown';
    const vendorName = vendor.name.replace(/\s+/g, '-').toLowerCase();
    router.push(`/vendor/${vendor.id}?city=${cityParam}&name=${vendorName}`);
  };

  const handleFilterApply = (filters: FilterState) => {
    setAppliedFilters(filters);
    setIsFilterOpen(false);
  };

  const sortOptions = [
    { value: "distance", label: "Distance" },
    { value: "rating", label: "Rating" },
    { value: "name", label: "Name" }
  ];

  if (isLoading) {
    return (
      <Layout>
        <div className="max-w-[1120px] mx-auto mt-[20px] md:mt-[30px] p-4 flex justify-center items-center h-[50vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#E87616]"></div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="max-w-[1120px] mx-auto mt-[20px] md:mt-[30px] p-4 text-center">
          <div className="text-red-500 text-xl font-semibold mb-2">Error loading vendors</div>
          <div className="text-gray-600">Please try again later</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-[1120px] mx-auto mt-[20px] md:mt-[30px] p-4">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-[#2C2F24]">
              {getCurrentVendorTypeDisplay()} {city && `in ${city}`}
            </h1>
            <p className="text-[#9B9B9B] mt-2">
              {currentVendors.length} vendors found
            </p>
          </div>

          <div className="flex gap-3">
            {/* Sort Dropdown */}
            <div className="relative">
              <Button
                variant="outline"
                onClick={() => setShowSortDropdown(!showSortDropdown)}
                className="flex items-center gap-2"
              >
                Sort by: {sortOptions.find(opt => opt.value === sortBy)?.label}
                <ChevronDown className="h-4 w-4" />
              </Button>
              
              {showSortDropdown && (
                <div className="absolute right-0 top-full mt-2 bg-white border rounded-md shadow-lg z-10 min-w-[150px]">
                  {sortOptions.map((option) => (
                    <button
                      key={option.value}
                      className="w-full text-left px-4 py-2 hover:bg-gray-100 first:rounded-t-md last:rounded-b-md"
                      onClick={() => {
                        setSortBy(option.value);
                        setShowSortDropdown(false);
                      }}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Filter Button */}
            <Button
              variant="outline"
              onClick={() => setIsFilterOpen(true)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              Filter
            </Button>
          </div>
        </div>

        {/* Vendors Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {currentVendors.map((vendor) => (
            <div
              key={vendor.id}
              className="bg-white rounded-lg shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
              onClick={() => handleVendorClick(vendor)}
            >
              <div className="relative h-48">
                <img
                  src={vendor.image || '/lovable-uploads/image (1).png'}
                  alt={vendor.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = '/lovable-uploads/image (1).png';
                  }}
                />
              </div>
              
              <div className="p-4">
                <h3 className="font-bold text-lg text-[#2C2F24] mb-2">{vendor.name}</h3>
                <p className="text-[#9B9B9B] text-sm mb-2">{vendor.type}</p>
                
                <div className="flex items-center gap-2 mb-2">
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 fill-[#FFC529] text-[#FFC529]" />
                    <span className="font-semibold">{vendor.rating || 'New'}</span>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    <MapPin className="h-4 w-4 text-[#008001]" />
                    <span className="text-sm">{vendor.distance?.toFixed(2) || '0.00'} km</span>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <span className={cn(
                    "text-sm font-semibold",
                    vendor.isVeg ? "text-[#008001]" : "text-[#FF0000]"
                  )}>
                    {vendor.isVeg ? "Veg" : "Non-Veg"}
                  </span>
                  
                  <ActionButtons
                    phoneNumber={vendor.phone}
                    whatsappNumber={vendor.whatsapp}
                    coordinates={vendor.coordinates}
                    vendorName={vendor.name}
                    vendorId={vendor.id}
                    address={vendor.address}
                    className="scale-75"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* No vendors found */}
        {currentVendors.length === 0 && (
          <div className="text-center py-12">
            <h3 className="text-xl font-semibold text-[#2C2F24] mb-2">No vendors found</h3>
            <p className="text-[#9B9B9B]">Try adjusting your filters or search in a different area.</p>
          </div>
        )}

        {/* Filter Dialog */}
        <FilterDialog
          isOpen={isFilterOpen}
          onClose={() => setIsFilterOpen(false)}
          onApply={handleFilterApply}
          initialFilters={appliedFilters}
          categories={[]} // Add categories as needed
        />
      </div>
    </Layout>
  );
}
