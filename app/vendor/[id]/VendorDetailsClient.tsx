'use client';

import React, { useState, useEffect, useRef } from "react";
import { Layout } from "@/components/layout/Layout";
import { Star } from "lucide-react";
import { cn } from "@/lib/utils";
import { ActionButtons } from "@/components/ui/action-buttons";
import { RatingProvider, useRating } from "@/contexts/RatingContext";
import MenuTabContent from "@/components/vendor/MenuTabContent";
import ReviewTabContent from "@/components/vendor/ReviewTabContent";
import ServicesTabContent from "@/components/vendor/ServicesTabContent";
import PreferencesTabContent from "@/components/vendor/PreferencesTabContent";
import PaymentTabContent from "@/components/vendor/PaymentTabContent";
import AboutUsTabContent from "@/components/vendor/AboutUsTabContent";
import RatingLoader from "@/components/vendor/RatingLoader";
import { fetchVendorById, BaseVendor } from "@/services/vendorService";
import { calculateDistance } from "@/utils/distanceUtils";

type Props = {
  vendorId: string;
  searchParams: { [key: string]: string | string[] | undefined };
};

// Helper function to format time
const formatTime = (time: string): string => {
  if (!time) return time;
  
  try {
    let hours, minutes;
    if (time.includes(':')) {
      [hours, minutes] = time.split(':');
    } else {
      hours = time.slice(0, 2);
      minutes = time.slice(2);
    }
    
    hours = parseInt(hours, 10);
    minutes = parseInt(minutes, 10);
    
    if (isNaN(hours) || isNaN(minutes)) {
      return time;
    }
    
    const period = hours >= 12 ? 'PM' : 'AM';
    const displayHours = hours % 12 || 12;
    
    return `${displayHours}:${minutes.toString().padStart(2, '0')} ${period}`;
  } catch (error) {
    return time;
  }
};

const tabs = [
  { id: "menu", label: "Menu" },
  { id: "review", label: "Review" },
  { id: "services", label: "Services" },
  { id: "preferences", label: "Preferences" },
  { id: "payment", label: "Payment" },
  { id: "about", label: "About Us" }
];

// Inner component that uses the rating context
const VendorDetailsInner = ({ vendorId, searchParams }: Props) => {
  const [activeTab, setActiveTab] = useState("menu");
  const [vendor, setVendor] = useState<BaseVendor | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userCoordinates, setUserCoordinates] = useState<{lat: number, lng: number} | null>(null);

  const { vendorRating } = useRating();
  const tabRefs = useRef<(HTMLButtonElement | null)[]>([]);
  const [indicatorStyle, setIndicatorStyle] = useState({ left: "0px", width: "0px" });

  // Fetch vendor details
  useEffect(() => {
    const loadVendor = async () => {
      try {
        setLoading(true);
        const vendorData = await fetchVendorById(vendorId);
        setVendor(vendorData);
      } catch (err) {
        console.error('Error fetching vendor:', err);
        setError('Failed to load vendor details');
      } finally {
        setLoading(false);
      }
    };

    if (vendorId) {
      loadVendor();
    }
  }, [vendorId]);

  // Get user coordinates
  useEffect(() => {
    const coords = localStorage.getItem('homefoodi_user_coordinates');
    if (coords) {
      try {
        setUserCoordinates(JSON.parse(coords));
      } catch (error) {
        console.error('Error parsing user coordinates:', error);
      }
    }
  }, []);

  // Update indicator position
  useEffect(() => {
    const activeIndex = tabs.findIndex((tab) => tab.id === activeTab);
    if (activeIndex !== -1 && tabRefs.current[activeIndex]) {
      const { offsetLeft, offsetWidth } = tabRefs.current[activeIndex]!; 
      setIndicatorStyle({ left: `${offsetLeft}px`, width: `${offsetWidth}px` });
    }
  }, [activeTab]);

  if (loading) {
    return (
      <Layout>
        <div className="max-w-[1120px] mx-auto mt-[20px] md:mt-[30px] p-4 flex justify-center items-center h-[50vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#E87616]"></div>
        </div>
      </Layout>
    );
  }

  if (error || !vendor) {
    return (
      <Layout>
        <div className="max-w-[1120px] mx-auto mt-[20px] md:mt-[30px] p-4 text-center">
          <div className="text-red-500 text-xl font-semibold mb-2">Error loading vendor details</div>
          <div className="text-gray-600">{error || 'Vendor data not available'}</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <RatingLoader vendorId={vendorId} />
      <div className="w-full">
        {/* Vendor Header */}
        <div className="bg-[#F8F8FA] p-3 md:p-5 mb-3 md:mb-5 max-w-[1120px] mx-auto mt-[20px] md:mt-[30px]">
          <div className="max-w-[1120px] mx-auto flex flex-col md:flex-row justify-between flex-wrap">
            <div className="w-full md:w-[50%] mb-4 md:mb-0">
              {/* Vendor Image - Mobile only */}
              <div className="block md:hidden mb-4">
                <img 
                  src={vendor.image || '/lovable-uploads/image (1).png'}
                  alt={`${vendor.name}`} 
                  className="w-full h-[180px] sm:h-[220px] object-cover rounded-md shadow-md"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.onerror = null;
                    target.src = '/lovable-uploads/image (1).png';
                  }}
                />
              </div>
              
              <div className="flex flex-col md:flex-row gap-[15px] md:gap-[30px] items-start">
                <div className="border-b md:border-b-0 md:border-r border-[#9B9B9B] pb-3 md:pb-0 pr-0 md:pr-[20px] w-full md:w-auto">
                  <h1 className="text-xl md:text-2xl font-bold text-[#2C2F24]">
                    {vendor.name}
                  </h1>
                  <p className="text-sm text-[#9B9B9B] max-w-[350px]">{vendor.type}</p>
                </div>

                <div className="flex items-center gap-1 mt-0">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="12"
                    height="14"
                    viewBox="0 0 12 14"
                    fill="none"
                  >
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M6 13.6668C8.25 13.6668 12 9.53222 12 6.25942C12 2.98662 9.31371 0.333496 6 0.333496C2.68629 0.333496 0 2.98662 0 6.25942C0 9.53222 3.75 13.6668 6 13.6668ZM6 8.3335C7.10457 8.3335 8 7.43807 8 6.3335C8 5.22893 7.10457 4.3335 6 4.3335C4.89543 4.3335 4 5.22893 4 6.3335C4 7.43807 4.89543 8.3335 6 8.3335Z"
                      fill="#008001"
                    />
                  </svg>
                  <span className="font-bold text-sm">
                    {userCoordinates && vendor.vendorCoordinates && 
                     vendor.vendorCoordinates.latitude && 
                     vendor.vendorCoordinates.longitude ? 
                      calculateDistance(
                        userCoordinates.lat,
                        userCoordinates.lng,
                        vendor.vendorCoordinates.latitude,
                        vendor.vendorCoordinates.longitude
                      ).toFixed(2) : 
                      (vendor.distance ? vendor.distance.toFixed(2) : '0.00')
                    } km
                  </span>
                </div>
              </div>

              <div className="flex flex-col gap-2 mt-2">
                <div className="flex items-center gap-2">
                  {/* Veg/Non-Veg Label */}
                  {vendor.isMixed ? (
                    <span className="italic font-bold text-sm">
                      <span className="text-[#008001]">Veg</span>
                      <span className="text-[#000000]"> / </span>
                      <span className="text-[#FF0000]">Non-Veg</span>
                    </span>
                  ) : (
                    <span className={cn(
                      "italic font-bold text-sm",
                      vendor.isVeg ? "text-[#008001]" : "text-[#FF0000]"
                    )}>
                      {vendor.isVeg ? "Veg" : "Non-Veg"}
                    </span>
                  )}
                  <span className="text-[#2C2F24]">|</span>
                  <span className="italic font-bold text-sm text-[#2C2F24]">
                    {vendor.timings}
                  </span>
                </div>
              </div>
            </div>
            
            <div className="w-full md:w-[50%] flex items-center">
              <ActionButtons
                className="flex justify-start md:justify-end w-full"
                phoneNumber={vendor.phone}
                whatsappNumber={vendor.whatsapp}
                coordinates={vendor.coordinates}
                vendorName={vendor.name}
                vendorId={vendorId}
                address={vendor.address}
              />
            </div>
          </div>
        </div>

        {/* Vendor Information */}
        <div className="max-w-[1120px] mx-auto px-3 md:px-0">
          <div className="bg-[#F8F8FA] grid grid-cols-1 gap-3 md:gap-5 p-3 md:p-5 mb-4 md:mb-8">
            <div className="flex gap-3">
              <div className="text-[#E87616]">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="34"
                  height="34"
                  viewBox="0 0 34 34"
                  fill="none"
                >
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M16.9939 31.1555C21.7734 31.1555 29.7392 22.3727 29.7392 15.4205C29.7392 8.46836 24.0329 2.83252 16.9939 2.83252C9.95482 2.83252 4.24854 8.46836 4.24854 15.4205C4.24854 22.3727 12.2144 31.1555 16.9939 31.1555ZM16.9939 19.8263C19.3402 19.8263 21.2423 17.9242 21.2423 15.5779C21.2423 13.2315 19.3402 11.3294 16.9939 11.3294C14.6475 11.3294 12.7454 13.2315 12.7454 15.5779C12.7454 17.9242 14.6475 19.8263 16.9939 19.8263Z"
                    fill="#008001"
                  />
                </svg>
              </div>
              <div>
                <h3 className="text-[#E87616] font-bold text-base uppercase">
                  Address
                </h3>
                <p className="text-[#989CA3] text-base">{vendor.address}</p>
              </div>
            </div>
          </div>

          {/* Tabs Navigation */}
          <div className="relative">
            <div className="flex overflow-x-auto scrollbar-hide space-x-4 md:space-x-8 py-2 md:py-4 mb-[20px] md:mb-[30px]">
              {tabs.map((tab, index) => (
                <button
                  key={tab.id}
                  ref={(el) => (tabRefs.current[index] = el)}
                  className={`relative text-base font-semibold whitespace-nowrap focus-visible:outline-none
                    ${activeTab === tab.id
                      ? "text-[#000] after:content-[''] after:absolute after:left-0 after:top-full after:w-full after:h-[2px] after:bg-black"
                      : "text-[#9B9B9B]"}
                  `}
                  onClick={() => setActiveTab(tab.id)}
                >
                  {tab.label}
                </button>
              ))}
            </div>
          </div>

          {/* Tab Content */}
          <div className="max-w-[1120px] mx-auto">
            {activeTab === "menu" && <MenuTabContent vendorId={vendorId} />}
            {activeTab === "review" && <ReviewTabContent vendorId={vendorId} />}
            {activeTab === "services" && <ServicesTabContent vendorId={vendorId} />}
            {activeTab === "preferences" && <PreferencesTabContent vendorId={vendorId} />}
            {activeTab === "payment" && <PaymentTabContent />}
            {activeTab === "about" && <AboutUsTabContent vendorId={vendorId} />}
          </div>
        </div>

        {/* Sticky bottom action bar */}
        <div className="sticky bottom-0 w-full bg-white shadow-[0px_-1px_8px_0px_rgba(0,0,0,0.18)] p-3 md:p-4 z-50 border-t border-gray-200 md:px-5 xl:px-10">
          <div className="max-w-[1120px] mx-auto flex flex-col md:flex-row justify-between items-start md:items-center gap-3 md:gap-0">
            <div className="flex flex-col">
              <h1 className="text-xl md:text-2xl font-bold text-[#2C2F24]">
                {vendor.name}
              </h1>
              <div className="flex gap-1 items-center">
                {Number(vendorRating || vendor.rating) === 0 ? (
                  <>
                    <div className="font-bold">No ratings yet</div>
                    <Star className="h-4 w-4 md:h-5 md:w-5 fill-[#FFC529] text-[#FFC529]" />
                  </>
                ) : (
                  <>
                    <div className="font-bold">{vendorRating || vendor.rating}</div>
                    <Star className="h-4 w-4 md:h-5 md:w-5 fill-[#FFC529] text-[#FFC529]" />
                  </>
                )}
              </div>
            </div>
            <div className="flex w-full md:w-auto">
              <ActionButtons
                phoneNumber={vendor.phone}
                whatsappNumber={vendor.whatsapp}
                coordinates={vendor.coordinates}
                vendorName={vendor.name}
                vendorId={vendorId}
                address={vendor.address}
                vendorType={vendor.vendor_type}
                className="w-full md:flex-wrap"
              />
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

// Wrapper component that provides the rating context
export default function VendorDetailsClient(props: Props) {
  return (
    <RatingProvider>
      <VendorDetailsInner {...props} />
    </RatingProvider>
  );
}
