/**
 * Google Maps API Type Definitions
 * Created: 2025-03-26
 * Description: Type definitions for Google Maps API integration
 */

// Global declaration for Google namespace
declare global {
  interface Window {
    google: typeof google;
  }

  namespace google {
    namespace maps {
      class Map {
        constructor(mapDiv: Element, opts?: MapOptions);
      }
      
      interface MapOptions {
        center?: LatLng | LatLngLiteral;
        zoom?: number;
        [key: string]: any;
      }
      
      // Places API
      namespace places {
        class AutocompleteService {
          getPlacePredictions(
            request: AutocompletionRequest,
            callback: (predictions: AutocompletePrediction[], status: PlacesServiceStatus) => void
          ): void;
        }
        
        class AutocompleteSessionToken {}
        
        class PlacesService {
          constructor(attrContainer: HTMLDivElement | Map);
          findPlaceFromQuery(
            request: FindPlaceFromQueryRequest,
            callback: (results: PlaceResult[], status: PlacesServiceStatus) => void
          ): void;
          getDetails(
            request: PlaceDetailsRequest,
            callback: (result: PlaceResult, status: PlacesServiceStatus) => void
          ): void;
        }
        
        enum PlacesServiceStatus {
          OK = 'OK',
          ZERO_RESULTS = 'ZERO_RESULTS',
          OVER_QUERY_LIMIT = 'OVER_QUERY_LIMIT',
          REQUEST_DENIED = 'REQUEST_DENIED',
          INVALID_REQUEST = 'INVALID_REQUEST',
          UNKNOWN_ERROR = 'UNKNOWN_ERROR',
          NOT_FOUND = 'NOT_FOUND'
        }
      }
      
      class Geocoder {
        geocode(
          request: GeocoderRequest,
          callback: (results: GeocoderResult[], status: GeocoderStatus) => void
        ): void;
      }
      
      enum GeocoderStatus {
        ERROR = 'ERROR',
        INVALID_REQUEST = 'INVALID_REQUEST',
        OK = 'OK',
        OVER_QUERY_LIMIT = 'OVER_QUERY_LIMIT',
        REQUEST_DENIED = 'REQUEST_DENIED',
        UNKNOWN_ERROR = 'UNKNOWN_ERROR',
        ZERO_RESULTS = 'ZERO_RESULTS'
      }
      
      enum PlacesServiceStatus {
        OK = 'OK',
        ZERO_RESULTS = 'ZERO_RESULTS',
        OVER_QUERY_LIMIT = 'OVER_QUERY_LIMIT',
        REQUEST_DENIED = 'REQUEST_DENIED',
        INVALID_REQUEST = 'INVALID_REQUEST',
        UNKNOWN_ERROR = 'UNKNOWN_ERROR',
        NOT_FOUND = 'NOT_FOUND'
      }
    }
  }
}

declare namespace google.maps {
  class Geocoder {
    geocode(
      request: GeocoderRequest,
      callback: (results: GeocoderResult[], status: GeocoderStatus) => void
    ): void;
  }

  interface GeocoderRequest {
    address?: string;
    location?: LatLng | LatLngLiteral;
    placeId?: string;
    bounds?: LatLngBounds | LatLngBoundsLiteral;
    componentRestrictions?: GeocoderComponentRestrictions;
    region?: string;
  }

  interface GeocoderComponentRestrictions {
    country: string | string[];
    postalCode?: string;
    administrativeArea?: string;
    locality?: string;
    route?: string;
  }

  interface GeocoderResult {
    address_components: GeocoderAddressComponent[];
    formatted_address: string;
    geometry: GeocoderGeometry;
    place_id: string;
    plus_code?: {
      compound_code: string;
      global_code: string;
    };
    types: string[];
  }

  interface GeocoderAddressComponent {
    long_name: string;
    short_name: string;
    types: string[];
  }

  interface GeocoderGeometry {
    location: LatLng;
    location_type: GeocoderLocationType;
    viewport: LatLngBounds;
    bounds?: LatLngBounds;
  }

  enum GeocoderLocationType {
    APPROXIMATE = 'APPROXIMATE',
    GEOMETRIC_CENTER = 'GEOMETRIC_CENTER',
    RANGE_INTERPOLATED = 'RANGE_INTERPOLATED',
    ROOFTOP = 'ROOFTOP'
  }

  enum GeocoderStatus {
    ERROR = 'ERROR',
    INVALID_REQUEST = 'INVALID_REQUEST',
    OK = 'OK',
    OVER_QUERY_LIMIT = 'OVER_QUERY_LIMIT',
    REQUEST_DENIED = 'REQUEST_DENIED',
    UNKNOWN_ERROR = 'UNKNOWN_ERROR',
    ZERO_RESULTS = 'ZERO_RESULTS'
  }

  class LatLng {
    constructor(lat: number, lng: number, noWrap?: boolean);
    lat(): number;
    lng(): number;
    toString(): string;
    toUrlValue(precision?: number): string;
    toJSON(): LatLngLiteral;
    equals(other: LatLng): boolean;
  }

  interface LatLngLiteral {
    lat: number;
    lng: number;
  }

  class LatLngBounds {
    constructor(sw?: LatLng | LatLngLiteral, ne?: LatLng | LatLngLiteral);
    contains(latLng: LatLng | LatLngLiteral): boolean;
    equals(other: LatLngBounds | LatLngBoundsLiteral): boolean;
    extend(point: LatLng | LatLngLiteral): LatLngBounds;
    getCenter(): LatLng;
    getNorthEast(): LatLng;
    getSouthWest(): LatLng;
    intersects(other: LatLngBounds | LatLngBoundsLiteral): boolean;
    isEmpty(): boolean;
    toJSON(): LatLngBoundsLiteral;
    toSpan(): LatLng;
    toString(): string;
    toUrlValue(precision?: number): string;
    union(other: LatLngBounds | LatLngBoundsLiteral): LatLngBounds;
  }

  interface LatLngBoundsLiteral {
    east: number;
    north: number;
    south: number;
    west: number;
  }

  class PlacesService {
    constructor(attrContainer: HTMLDivElement | Map);
    findPlaceFromQuery(
      request: FindPlaceFromQueryRequest,
      callback: (results: PlaceResult[], status: PlacesServiceStatus) => void
    ): void;
    getDetails(
      request: PlaceDetailsRequest,
      callback: (result: PlaceResult, status: PlacesServiceStatus) => void
    ): void;
    nearbySearch(
      request: PlaceSearchRequest,
      callback: (results: PlaceResult[], status: PlacesServiceStatus, pagination: PlaceSearchPagination) => void
    ): void;
    textSearch(
      request: TextSearchRequest,
      callback: (results: PlaceResult[], status: PlacesServiceStatus, pagination: PlaceSearchPagination) => void
    ): void;
  }

  interface AutocompletePrediction {
    description: string;
    matched_substrings: PredictionSubstring[];
    place_id: string;
    structured_formatting: AutocompleteStructuredFormatting;
    terms: PredictionTerm[];
    types: string[];
  }

  interface AutocompleteStructuredFormatting {
    main_text: string;
    main_text_matched_substrings: PredictionSubstring[];
    secondary_text: string;
  }

  interface PredictionTerm {
    offset: number;
    value: string;
  }

  interface PredictionSubstring {
    length: number;
    offset: number;
  }

  class AutocompleteSessionToken {
    constructor();
  }

  class AutocompleteService {
    getPlacePredictions(
      request: AutocompletionRequest,
      callback: (predictions: AutocompletePrediction[], status: PlacesServiceStatus) => void
    ): void;
  }

  interface AutocompletionRequest {
    input: string;
    bounds?: LatLngBounds | LatLngBoundsLiteral;
    componentRestrictions?: ComponentRestrictions;
    location?: LatLng;
    offset?: number;
    radius?: number;
    sessionToken?: AutocompleteSessionToken;
    types?: string[];
  }

  interface ComponentRestrictions {
    country: string | string[];
  }

  enum PlacesServiceStatus {
    OK = 'OK',
    ZERO_RESULTS = 'ZERO_RESULTS',
    OVER_QUERY_LIMIT = 'OVER_QUERY_LIMIT',
    REQUEST_DENIED = 'REQUEST_DENIED',
    INVALID_REQUEST = 'INVALID_REQUEST',
    UNKNOWN_ERROR = 'UNKNOWN_ERROR',
    NOT_FOUND = 'NOT_FOUND'
  }
}

export {};
