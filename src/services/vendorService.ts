/**
 * Vendor Service
 * 
 * This file contains functions for fetching vendor data from the database
 * based on the vendor type stored in localStorage.
 * Updated to include join with vendor_other_details table to fetch delivery_radius.
 * Updated to implement distance filter using user location and vendor coordinates.
 */

import { normalizeVendorType, getVendorTableName } from "@/utils/vendorTypeUtils";
import { calculateDistance } from "@/utils/distanceUtils";
import { extractCityFromAddress } from "@/lib/location";

// Define vendor type interfaces based on the database schema
export interface BaseVendor {
  id: string;
  business_name: string | null;
  phone_number: string;
  sms_number?: string; // Added SMS number field for enquiry functionality
  address: string | null;
  city: string | null;
  state: string | null;
  profile_image: string | null;
  profile_url?: string | null;
  vendor_type: string;
  operational_status: string;
  profile_status: string;
  // Database coordinate columns (note: these are misspelled in the database)
  lattitute?: string | null; // Misspelled latitude in database
  longitute?: string | null; // Misspelled longitude in database
  // Additional properties needed for the UI
  cuisine_type?: string;
  rating?: number;
  distance?: number;
  isVeg?: boolean;
  isNonVeg?: boolean;
  isMixed?: boolean;
  timings?: string;
  delivery_radius?: number; // Delivery radius in km
  start_time?: string; // Start time from vendor_other_details
  end_time?: string; // End time from vendor_other_details
  start_day?: string; // Start day from vendor_other_details
  end_day?: string; // End day from vendor_other_details
  whatsapp?: string;
  whatsapp_number?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  // Flags for distance calculation
  missingCoordinates?: boolean; // Flag to indicate vendor has no coordinates in database
  distanceUnavailable?: boolean; // Flag to indicate distance calculation is unavailable
  service_types?: string[]
  service_ids?: string[]; // IDs of services offered by the vendor
  cuisine_ids?: string[]; // IDs of cuisines offered by the vendor
  cuisineIds?: string[]; // IDs of cuisines offered by the vendor (alternative property name)
  cuisineNames?: string[]; // Names of cuisines offered by the vendor
  vegMenuItems?: string[]; // List of vegetarian menu items
  nonVegMenuItems?: string[]; // List of non-vegetarian menu items
  menuItems?: string[]; // Combined list of menu items (for backward compatibility)
  hasVegMenu?: boolean; // Flag to indicate if vendor has veg menu items
  hasNonVegMenu?: boolean; // Flag to indicate if vendor has non-veg menu items
}

import { supabase } from '@/lib/supabase';
import { fetchServicesByIds } from './serviceService';

/**
 * Get mock vendors for development purposes when no data is found in the database
 * @param vendorType - The vendor type to get mock data for
 * @returns Array of mock vendors
 */
const getMockVendors = (vendorType: string): BaseVendor[] => {
  // Common vendor properties
  const commonProps = {
    operational_status: 'active',
    profile_status: 'approved',
    profile_status_check: 'completed',
    city: 'Noida',
    state: 'Uttar Pradesh',
    delivery_radius: 5 // Adding default delivery radius of 5 km
  };

  // Mock vendors for home-chef
  if (vendorType === 'home-chef') {
    return [
      {
        id: '1',
        business_name: 'Abha\'s Kitchen',
        phone_number: '+91 9876543210',
        address: 'Sector 16, Noida',
        profile_image: '/images/chef1.jpg',
        vendor_type: 'home_chef',
        cuisine_type: 'North Indian, Fast Food',
        rating: 4.9,
        distance: 2,
        isVeg: true,
        timings: 'Lunch - Dinner',
        whatsapp: '+91 9876543210',
        ...commonProps
      },
      {
        id: '2',
        business_name: 'Ruchi\'s Homemade',
        phone_number: '+91 9876543211',
        address: 'Sector 18, Noida',
        profile_image: '/images/chef2.jpg',
        vendor_type: 'home_chef',
        cuisine_type: 'South Indian, Bengali',
        rating: 4.7,
        distance: 3.5,
        isVeg: false,
        timings: 'Breakfast - Lunch - Dinner',
        whatsapp: '+91 9876543211',
        ...commonProps
      },
      {
        id: '3',
        business_name: 'Punjabi Tadka',
        phone_number: '+91 9876543212',
        address: 'Sector 62, Noida',
        profile_image: '/images/chef3.jpg',
        vendor_type: 'home_chef',
        cuisine_type: 'Punjabi, North Indian',
        rating: 4.8,
        distance: 5,
        isVeg: false,
        timings: 'Lunch - Dinner',
        whatsapp: '+91 9876543212',
        ...commonProps
      }
    ];
  }
  
  // Mock vendors for tiffin_supplier
  if (vendorType === 'tiffin_supplier') {
    return [
      {
        id: '4',
        business_name: 'Daily Tiffin',
        phone_number: '+91 9876543213',
        address: 'Sector 12, Noida',
        profile_image: '/images/tiffin1.jpg',
        vendor_type: 'tiffin_service',
        cuisine_type: 'North Indian, Home Style',
        rating: 4.6,
        distance: 1.5,
        isVeg: true,
        timings: 'Lunch - Dinner',
        whatsapp: '+91 9876543213',
        ...commonProps
      },
      {
        id: '5',
        business_name: 'Mom\'s Tiffin',
        phone_number: '+91 9876543214',
        address: 'Sector 15, Noida',
        profile_image: '/images/tiffin2.jpg',
        vendor_type: 'tiffin_service',
        cuisine_type: 'Multi Cuisine',
        rating: 4.5,
        distance: 2.2,
        isVeg: false,
        timings: 'Breakfast - Lunch - Dinner',
        whatsapp: '+91 9876543214',
        ...commonProps
      }
    ];
  }
  
  // Mock vendors for caters
  if (vendorType === 'caters') {
    return [
      {
        id: '6',
        business_name: 'Royal Caterers',
        phone_number: '+91 9876543215',
        address: 'Sector 50, Noida',
        profile_image: '/images/caterer1.jpg',
        vendor_type: 'caterer',
        cuisine_type: 'Multi Cuisine, Speciality',
        rating: 4.9,
        distance: 4.5,
        isVeg: false,
        timings: 'Available 24x7',
        whatsapp: '+91 9876543215',
        ...commonProps
      },
      {
        id: '7',
        business_name: 'Event Feast',
        phone_number: '+91 9876543216',
        address: 'Sector 100, Noida',
        profile_image: '/images/caterer2.jpg',
        vendor_type: 'caterer',
        cuisine_type: 'Indian, Chinese, Continental',
        rating: 4.7,
        distance: 6.8,
        isVeg: false,
        timings: 'Available 24x7',
        whatsapp: '+91 9876543216',
        ...commonProps
      }
    ];
  }
  
  // Default empty array if vendor type doesn't match
  return [];
};

/**
 * Fetch vendors based on the current vendor type and user coordinates
 * @param filters - Optional filters to apply to the query
 * @returns Promise with vendor data
 */
export const fetchVendors = async (filters?: Record<string, any>): Promise<BaseVendor[]> => {
  console.log("filtersfilters",filters)
  // We'll collect all cuisine IDs to fetch them in bulk
  const allCuisineIds: string[] = [];
  // Map to store vendor ID to cuisine IDs for later mapping
  const vendorCuisineMap: Record<string, string[]> = {};
  // Map to store cuisine ID to cuisine name for quick lookup
  const cuisineIdToNameMap: Record<string, string> = {};
  // Check if this is a loadMore request - if so, we'll only use vendor_type and coordinates
  const isLoadMoreRequest = filters?.loadMore === true;
  
  // Check if price filter is present
  if (filters && filters.price) {
    console.log('PRICE FILTER CHECK: Price filter is present with value:', filters.price);
  } else {
    console.log('PRICE FILTER CHECK: No price filter found in filters object');
  }
  
  if (isLoadMoreRequest) {
    console.log('This is a loadMore request - only using vendor_type and coordinates');
  }
  try {
    // Get user coordinates from localStorage
    let userLat = 0;
    let userLng = 0;
    try {
      const userCoordinatesStr = localStorage.getItem('userCoordinates');
      if (userCoordinatesStr) {
        const userCoordinates = JSON.parse(userCoordinatesStr);
        userLat = parseFloat(userCoordinates.lat);
        userLng = parseFloat(userCoordinates.lng);
        console.log('User coordinates from localStorage:', userLat, userLng);
      } else {
        console.log('User coordinates not found in localStorage');
      }
    } catch (error) {
      console.error('Error parsing userCoordinates from localStorage:', error);
    }
    
    // Get the current vendor type from localStorage
    const vendorType = localStorage.getItem('vendor_type') || 'home-chef';
    
    // Start building the Supabase query
    console.log('Fetching vendors with type:', vendorType, 'from localStorage');
    
    // Add additional debug logging
    console.log('Current localStorage state:', {
      vendor_type: localStorage.getItem('vendor_type'),
      vendor_type_display: localStorage.getItem('vendor_type_display'),
      activeNav: localStorage.getItem('activeNav')
    });
    
    // Special handling for different vendor types
    let query;
    
    if (vendorType === 'caterer') {
      // For caterer type, fetch both 'caterer' and 'catering' vendors
      console.log('Fetching CATERER vendors with query: vendor_type.eq.caters,vendor_type.eq.caterer,vendor_type.eq.catering');
      query = supabase
        .from('vendors')
        .select(`
          id, business_name, phone_number, sms_number, whatsapp_number, address, city, state, profile_image, profile_url, vendor_type, operational_status, profile_status, profile_status_check, account_status, lattitute, longitute,
          vendor_other_details!vendor_id(id, delivery_radius, start_time, end_time, cuisines, service_types, veg_menu, non_veg_menu)
        `)
        .or('vendor_type.eq.caters,vendor_type.eq.caterer,vendor_type.eq.catering')
        .eq('profile_status_check', 'completed')
        .eq('account_status', 'false');
    } 
    else if (vendorType === 'tiffin_supplier') {
      // For tiffin supplier, also check for variations
      query = supabase
        .from('vendors')
        .select(`
          id, business_name, phone_number, sms_number, whatsapp_number, address, city, state, profile_image, profile_url, vendor_type, operational_status, profile_status, profile_status_check, account_status, lattitute, longitute,
          vendor_other_details!vendor_id(id, delivery_radius, start_time, end_time, cuisines, service_types, veg_menu, non_veg_menu)
        `)
        .or('vendor_type.eq.tiffin_supplier,vendor_type.eq.tiffin_service,vendor_type.eq.tiffin')
        .eq('profile_status_check', 'completed')
        .eq('account_status', 'false');
    }
    else {
      // For other types, just use the vendor type directly
      query = supabase
        .from('vendors')
        .select(`
          id, business_name, phone_number, sms_number, whatsapp_number, address, city, state, profile_image, profile_url, vendor_type, operational_status, profile_status, profile_status_check, account_status, lattitute, longitute,
          vendor_other_details!vendor_id(id, delivery_radius, start_time, end_time, cuisines, service_types, veg_menu, non_veg_menu)
        `)
        .eq('vendor_type', vendorType)
        .eq('profile_status_check', 'completed')
        .eq('account_status', 'false');
    }
      
    // Uncomment these filters once we confirm data exists
    // .eq('operational_status', 'active')
    // .eq('profile_status', 'approved')
    // .eq('profile_status_check', 'completed');
    
    // Get user coordinates from localStorage
    let userCoordinates = null;
    try {
      const userCoordinatesStr = localStorage.getItem('userCoordinates');
      if (userCoordinatesStr) {
        userCoordinates = JSON.parse(userCoordinatesStr);
        console.log('User coordinates from localStorage:', userCoordinates);
        
        // Add coordinates to the query if available
        if (userCoordinates && userCoordinates.lat && userCoordinates.lng) {
          // Filter out vendors with null coordinates
          // This ensures only vendors with valid coordinates are returned
          // We only want to include vendors with valid lattitute/longitute coordinates
      // Create a query for vendors with lattitute/longitute
      const lattituteQuery = supabase.from('vendors')
        .select('id')
        .not('lattitute', 'is', null)
        .not('longitute', 'is', null);
        
      // Get the vendor IDs from the query
      const { data: lattituteVendors, error: lattituteError } = await lattituteQuery;
      
      if (lattituteError) {
        console.error('Error fetching vendors with lattitute/longitute:', lattituteError);
      }
      
      // Use the vendor IDs with coordinates
      const vendorIdsWithCoordinates = (lattituteVendors || []).map(v => v.id);
      
      // Remove duplicates (though there shouldn't be any in this case)
      const uniqueVendorIds = [...new Set(vendorIdsWithCoordinates)];
      
      console.log(`Found ${uniqueVendorIds.length} vendors with coordinates`);
      
      // Only filter by coordinates if we found vendors with coordinates
      if (uniqueVendorIds.length > 0) {
        // Filter to only include vendors with coordinates
        query = query.in('id', uniqueVendorIds);
      } else {
        console.log('No vendors found with coordinates, not applying coordinate filter');
      }
          
          console.log('Filtering out vendors with null coordinates');
                       
          // We won't add user coordinates to the URL parameters as they're causing parsing issues
          // Instead, we'll add them as custom headers which won't affect the query parsing
          const customHeaders = {
            'Prefer': 'return=representation',
            'x-user-lat': userCoordinates.lat.toString(),
            'x-user-lng': userCoordinates.lng.toString()
          };
          query = query.headers(customHeaders);
          
          console.log('Including user coordinates as headers in API request:', userCoordinates);
        }
      }
    } catch (error) {
      console.error('Error parsing userCoordinates from localStorage:', error);
    }
    
    // Get user city from localStorage
    let userCity = null;
    try {
      // First check if we have the city directly stored in localStorage
      userCity = filters?.location ? filters?.location : localStorage.getItem('homefoodi_user_city');
      if (userCity) {
        console.log('City found directly in localStorage:', userCity);
      } else {
        // Try to get city from selected location first
        const selectedLocation = localStorage.getItem('homefoodi_selected_location');
        if (selectedLocation) {
          console.log('Selected location found:', selectedLocation);
          userCity = extractCityFromAddress(selectedLocation);
          console.log('City extracted from selected location:', userCity);
          
          // Store the extracted city for future use
          if (userCity) {
            localStorage.setItem('homefoodi_user_city', userCity);
          }
        }
        
        // If we couldn't get city from selected location, try from address
        if (!userCity) {
          const userAddress = localStorage.getItem('homefoodi_user_address');
          if (userAddress) {
            console.log('User address found:', userAddress);
            userCity = extractCityFromAddress(userAddress);
            console.log('City extracted from address:', userCity);
            
            // Store the extracted city for future use
            if (userCity) {
              localStorage.setItem('homefoodi_user_city', userCity);
            }
          }
        }
      }
      
      // Apply city filter to the query if we found a city
      if (userCity) {
        console.log('User city determined:', userCity);
        console.log('Filtering vendors by city:', userCity);
        
        // Debug: Log all vendors with their cities before filtering
        console.log('Checking for vendors with city:', userCity);
        
        // Apply the filter
        query = query.eq('city', userCity);
      } else {
        console.log('Could not determine user city from location or address');
      }
    } catch (error) {
      console.error('Error extracting city from user address:', error);
    }
    
    // Apply filters if provided
    if (filters) {
      // Handle veg_type filter
      if (filters.veg_type) {
        // We'll handle veg type filtering after fetching the data
        // based on veg_menu and non_veg_menu arrays in vendor_other_details table
        console.log('Veg type filtering will be applied post-query:', filters.veg_type);
      }
      
      // Handle cuisines filter - using cuisine IDs
      if (filters.cuisines) {
        // We'll handle cuisine filtering after fetching the data
        // since we need to check the cuisine_ids array in vendor_other_details
        console.log('Cuisine filtering will be applied post-query:', filters.cuisines);
      }
      
      // Handle services filter - using service IDs
      if (filters.services) {
        // We'll handle service filtering after fetching the data
        // since we need to check the service_ids array in vendor_other_details
        console.log('Service filtering will be applied post-query:', filters.services);
      }
      
      // Handle rating filter
      // We'll apply rating filtering after fetching the data
      // since we need to calculate average ratings from the reviews table
      if (filters.rating && filters.rating !== 'any') {
        console.log('Rating filtering will be applied post-query:', filters.rating);
      }
      
      // Handle distance filter from the UI
      if (filters.distance) {
        // We'll apply distance filtering after fetching the data
        // since we need to calculate distances based on user location and vendor coordinates
        console.log('Distance filtering will be applied post-query:', filters.distance);
        
        // Verify user location is available
        const userLat = localStorage.getItem('homefoodi_user_latitude');
        const userLng = localStorage.getItem('homefoodi_user_longitude');
        
        // If user location is not available, we'll still proceed with the query
        // but will filter out all results in the post-processing step
        if (!userLat || !userLng) {
          console.log('User location not available for distance filtering');
        }
      }
    }
    
        // We'll keep the query as is without adding coordinates at this stage
    
    // Execute the query
    const { data, error } = await query;
    
    if (error) {
      console.error('Supabase query error:', error);
      throw error;
    }
    
    console.log('Supabase response data:', data);
    
    // Debug: Check how many vendors have coordinates and check column names
    let coordVendorCount = 0;
    let noCoordVendorCount = 0;
    
    // Check if we have the right column names
    const firstVendor = data.length > 0 ? data[0] : null;
    if (firstVendor) {
      console.log('First vendor column names:', Object.keys(firstVendor));
    }
    
    data.forEach(vendor => {
      // Check if vendor has coordinates (using the correct column names with misspellings)
      const hasLat = vendor.lattitute !== null && vendor.lattitute !== undefined;
      const hasLng = vendor.longitute !== null && vendor.longitute !== undefined;
      
      if (hasLat && hasLng) {
        coordVendorCount++;
        console.log(`Vendor with coordinates: ${vendor.business_name}, ` + 
                   `Lat: ${vendor.lattitute}, ` + 
                   `Lng: ${vendor.longitute}`);
      } else {
        noCoordVendorCount++;
        console.log(`Vendor without coordinates: ${vendor.business_name}`);
      }
    });
    
    console.log(`Coordinates summary: ${coordVendorCount} vendors with coordinates, ${noCoordVendorCount} vendors without coordinates`);
    
    // If no data is found, return mock data for development
    if (!data || data.length === 0) {
      console.log('No vendors found, returning mock data for development');
      return getMockVendors(vendorType);
    }
    
    // Process the data to include delivery_radius from vendor_other_details
    console.log('Raw vendor data:', JSON.stringify(data, null, 2));
    
    // Fetch ratings for all vendors from the reviews table
    const vendorIds = data.map(vendor => vendor.id);
    let vendorRatings: Record<string, { total: number, count: number }> = {};
    
    // Initialize ratings for all vendors
    vendorIds.forEach(id => {
      vendorRatings[id] = { total: 0, count: 0 };
    });
    
    // Fetch ratings from reviews table
    const { data: reviewsData, error: reviewsError } = await supabase
      .from('reviews')
      .select('vendor_id, rating')
      .in('vendor_id', vendorIds)
      .eq('status', 'active');
      
    if (reviewsError) {
      console.error('Error fetching vendor ratings:', reviewsError);
    } else if (reviewsData && reviewsData.length > 0) {
      console.log('Reviews data fetched:', reviewsData.length, 'reviews');
      
      // Calculate average rating for each vendor
      reviewsData.forEach(review => {
        if (!vendorRatings[review.vendor_id]) {
          vendorRatings[review.vendor_id] = { total: 0, count: 0 };
        }
        vendorRatings[review.vendor_id].total += parseInt(review.rating);
        vendorRatings[review.vendor_id].count += 1;
      });
      
      console.log('Vendor ratings calculated:', vendorRatings);
    }
    
    // Collect all cuisine IDs from vendors to fetch them in bulk
    const allCuisineIds: string[] = [];
    const vendorCuisineMap: Record<string, string[]> = {};
    
    // First pass: extract cuisine IDs from all vendors
    for (const vendor of data) {
      let otherDetails;
      
      if (vendor.vendor_other_details) {
        // Handle both array and object formats
        if (Array.isArray(vendor.vendor_other_details)) {
          otherDetails = vendor.vendor_other_details[0] || {};
        } else {
          otherDetails = vendor.vendor_other_details;
        }
      } else {
        otherDetails = {};
      }
      
      // Extract cuisine IDs if available
      if (otherDetails && otherDetails.cuisines) {
        try {
          let cuisineIds: string[] = [];
          
          // Handle both array and string formats
          if (Array.isArray(otherDetails.cuisines)) {
            cuisineIds = otherDetails.cuisines.map(id => id.toString().trim());
          } else if (typeof otherDetails.cuisines === 'string') {
            // Try to parse as JSON
            try {
              const parsed = JSON.parse(otherDetails.cuisines);
              // Ensure the parsed result is an array
              if (Array.isArray(parsed)) {
                cuisineIds = parsed.map(id => id.toString().trim());
              }
            } catch (parseError) {
              // If it's not valid JSON, check if it's a comma-separated string
              if (otherDetails.cuisines.includes(',')) {
                cuisineIds = otherDetails.cuisines.split(',').map(id => id.toString().trim());
              } else {
                // Treat as a single ID
                cuisineIds = [otherDetails.cuisines.toString().trim()];
              }
            }
          }
          
          // Store cuisine IDs for bulk fetching
          if (cuisineIds.length > 0) {
            allCuisineIds.push(...cuisineIds);
            vendorCuisineMap[vendor.id] = cuisineIds;
          }
        } catch (error) {
          console.error(`Error processing cuisines for vendor ${vendor.id}:`, error);
        }
      }
    }
    
    // Fetch all unique cuisine names in bulk
    const cuisineIdToNameMap: Record<string, string> = {};
    
    if (allCuisineIds.length > 0) {
      // Get unique cuisine IDs
      const uniqueCuisineIds = [...new Set(allCuisineIds)];
      console.log(`Fetching ${uniqueCuisineIds.length} unique cuisine names in bulk`);
      
      try {
        const { data: cuisineData, error: cuisineError } = await supabase
          .from('cuisines')
          .select('id, name')
          .in('id', uniqueCuisineIds);
          
        if (cuisineError) {
          console.error('Error fetching cuisine names:', cuisineError);
        } else if (cuisineData && cuisineData.length > 0) {
          // Create a map of cuisine ID to cuisine name for quick lookup
          cuisineData.forEach(cuisine => {
            cuisineIdToNameMap[cuisine.id] = cuisine.name;
          });
          console.log(`Fetched ${cuisineData.length} cuisine names successfully`);
        } else {
          console.log('No cuisines found for the provided IDs');
        }
      } catch (error) {
        console.error('Error in bulk cuisine fetching:', error);
      }
    }
    
    // Now process the vendor data with cuisine information
    let processedData = await Promise.all(data.map(async (vendor) => {
      // vendor_other_details can come as an object or an array depending on the query
      let otherDetails;
      
      if (vendor.vendor_other_details) {
        // Handle both array and object formats
        if (Array.isArray(vendor.vendor_other_details)) {
          otherDetails = vendor.vendor_other_details[0] || {};
        } else {
          otherDetails = vendor.vendor_other_details;
        }
      } else {
        otherDetails = {};
      }
      
      console.log(`Vendor ${vendor.id} (${vendor.business_name}) other details:`, otherDetails);
      
      // Extract delivery radius if available
      let deliveryRadius = null;
      if (otherDetails && otherDetails.delivery_radius !== undefined && otherDetails.delivery_radius !== null) {
        // Handle both string and number formats
        deliveryRadius = typeof otherDetails.delivery_radius === 'string' 
          ? parseFloat(otherDetails.delivery_radius) 
          : otherDetails.delivery_radius;
          
        console.log(`Vendor ${vendor.id} (${vendor.business_name}) delivery radius:`, deliveryRadius);
        console.log(`Delivery radius type:`, typeof otherDetails.delivery_radius);
      } else {
        console.log(`Vendor ${vendor.id} (${vendor.business_name}) has no delivery radius`);
      }
      
      // Extract timing information if available
      let startTime = null;
      let endTime = null;
      if (otherDetails) {
        if (otherDetails.start_time) {
          startTime = otherDetails.start_time;
          console.log(`Vendor ${vendor.id} (${vendor.business_name}) start time:`, startTime);
        }
        if (otherDetails.end_time) {
          endTime = otherDetails.end_time;
          console.log(`Vendor ${vendor.id} (${vendor.business_name}) end time:`, endTime);
        }
      }
      
      // Calculate and assign rating from reviews table
      let rating = 0;
      if (vendorRatings[vendor.id] && vendorRatings[vendor.id].count > 0) {
        rating = Math.round((vendorRatings[vendor.id].total / vendorRatings[vendor.id].count) * 10) / 10;
        console.log(`Vendor ${vendor.business_name} rating: ${rating} (${vendorRatings[vendor.id].count} reviews)`);
      }
      
      // Extract cuisine IDs if available
      let cuisineIds: string[] = [];
      if (otherDetails && otherDetails.cuisines) {
        try {
          // Handle both array and string formats
          if (Array.isArray(otherDetails.cuisines)) {
            cuisineIds = otherDetails.cuisines.map(id => id.toString().trim());
          } else if (typeof otherDetails.cuisines === 'string') {
            // Try to parse as JSON
            try {
              const parsed = JSON.parse(otherDetails.cuisines);
              // Ensure the parsed result is an array
              if (Array.isArray(parsed)) {
                cuisineIds = parsed.map(id => id.toString().trim());
              } else {
                console.warn(`Cuisines for vendor ${vendor.id} is not an array after parsing:`, parsed);
                cuisineIds = [];
              }
            } catch (parseError) {
              // If it's not valid JSON, check if it's a comma-separated string
              if (otherDetails.cuisines.includes(',')) {
                cuisineIds = otherDetails.cuisines.split(',').map(id => id.toString().trim());
              } else {
                // Treat as a single ID
                cuisineIds = [otherDetails.cuisines.toString().trim()];
              }
            }
          } else {
            console.warn(`Unexpected cuisines format for vendor ${vendor.id}:`, typeof otherDetails.cuisines);
            cuisineIds = [];
          }
          console.log(`Vendor ${vendor.id} (${vendor.business_name}) cuisine IDs:`, cuisineIds);
          
          // Store cuisine IDs for bulk fetching later
          if (cuisineIds.length > 0) {
            allCuisineIds.push(...cuisineIds);
            vendorCuisineMap[vendor.id] = cuisineIds;
          }
        } catch (error) {
          console.error(`Error processing cuisines for vendor ${vendor.id}:`, error);
          cuisineIds = [];
        }
      }
      
      // Extract service IDs if available
      let serviceIds: string[] = [];
      if (otherDetails && otherDetails.service_types) {
        try {
          // Handle both array and string formats
          if (Array.isArray(otherDetails.service_types)) {
            serviceIds = otherDetails.service_types;
          } else if (typeof otherDetails.service_types === 'string') {
            // Try to parse as JSON
            try {
              const parsed = JSON.parse(otherDetails.service_types);
              // Ensure the parsed result is an array
              if (Array.isArray(parsed)) {
                serviceIds = parsed;
              } else {
                console.warn(`Service types for vendor ${vendor.id} is not an array after parsing:`, parsed);
                serviceIds = [];
              }
            } catch (parseError) {
              // If it's not valid JSON, check if it's a comma-separated string
              if (otherDetails.service_types.includes(',')) {
                serviceIds = otherDetails.service_types.split(',').map(id => id.trim());
              } else {
                // Treat as a single ID
                serviceIds = [otherDetails.service_types];
              }
            }
          } else {
            console.warn(`Unexpected service_types format for vendor ${vendor.id}:`, typeof otherDetails.service_types);
            serviceIds = [];
          }
          console.log(`Vendor ${vendor.id} (${vendor.business_name}) service IDs:`, serviceIds);
        } catch (error) {
          console.error(`Error processing service_types for vendor ${vendor.id}:`, error);
          serviceIds = [];
        }
      }
      
      // Determine veg/non-veg status based on menu data
      let isVeg = false;
      let isNonVeg = false;
      let isMixed = false;
      let hasVegMenu = false;
      let hasNonVegMenu = false;
      
      // Check if vendor has veg_menu and non_veg_menu arrays
      if (otherDetails) {
        // Check if veg_menu exists and has items
        hasVegMenu = otherDetails.veg_menu && 
          Array.isArray(otherDetails.veg_menu) && 
          otherDetails.veg_menu.length > 0;
        
        // Check if non_veg_menu exists and has items
        hasNonVegMenu = otherDetails.non_veg_menu && 
          Array.isArray(otherDetails.non_veg_menu) && 
          otherDetails.non_veg_menu.length > 0;
        
        // Set the appropriate flags based on menu content
        isVeg = hasVegMenu && !hasNonVegMenu; // Only veg items
        isNonVeg = !hasVegMenu && hasNonVegMenu; // Only non-veg items
        isMixed = hasVegMenu && hasNonVegMenu; // Both veg and non-veg items
        
        console.log(`Vendor ${vendor.id} (${vendor.business_name}) menu status:`, {
          hasVegMenu,
          hasNonVegMenu,
          isVeg,
          isNonVeg,
          isMixed
        });
      }
      
      // Extract menu items from veg_menu and non_veg_menu arrays
      let vegMenuItems: string[] = [];
      let nonVegMenuItems: string[] = [];
      
      if (otherDetails) {
        // Get vendor type and determine the appropriate table
        const vendorType = vendor.vendor_type || 'home-chef';
        const tableName = getVendorTableName(vendorType);
        
        // Add veg menu items if available
        if (otherDetails.veg_menu && Array.isArray(otherDetails.veg_menu) && otherDetails.veg_menu.length > 0) {
          try {
            // Fetch the actual menu items from the appropriate table based on vendor type
            const { data: vegMenuData, error: vegMenuError } = await supabase
              .from(tableName)
              .select('*')
              .in('id', otherDetails.veg_menu)
              .eq('status', true);
            
            if (vegMenuError) {
              console.error(`Error fetching veg menu items for vendor ${vendor.id}:`, vegMenuError);
            } else if (vegMenuData && vegMenuData.length > 0) {
              // Extract menu item names based on vendor type
              vegMenuItems = vegMenuData.map(item => {
                if (vendorType === 'home-chef' || vendorType === 'caterer') {
                  return item.menu_name || 'Unnamed Item';
                } else if (vendorType === 'tiffin_supplier') {
                  return item.meal_type || 'Unnamed Item';
                } else {
                  // Default fallback
                  return item.menu_name || item.meal_type || item.name || 'Unnamed Item';
                }
              }).filter(Boolean); // Remove null items
              
              console.log(`Vendor ${vendor.id} (${vendor.business_name}) veg menu items:`, vegMenuItems);
            }
          } catch (error) {
            console.error(`Error processing veg_menu for vendor ${vendor.id}:`, error);
          }
        }
        
        // Add non-veg menu items if available
        if (otherDetails.non_veg_menu && Array.isArray(otherDetails.non_veg_menu) && otherDetails.non_veg_menu.length > 0) {
          try {
            // Fetch the actual menu items from the appropriate table based on vendor type
            const { data: nonVegMenuData, error: nonVegMenuError } = await supabase
              .from(tableName)
              .select('*')
              .in('id', otherDetails.non_veg_menu)
              .eq('status', true);
            
            if (nonVegMenuError) {
              console.error(`Error fetching non-veg menu items for vendor ${vendor.id}:`, nonVegMenuError);
            } else if (nonVegMenuData && nonVegMenuData.length > 0) {
              // Extract menu item names based on vendor type
              nonVegMenuItems = nonVegMenuData.map(item => {
                if (vendorType === 'home-chef' || vendorType === 'caterer') {
                  return item.menu_name || 'Unnamed Item';
                } else if (vendorType === 'tiffin_supplier') {
                  return item.meal_type || 'Unnamed Item';
                } else {
                  // Default fallback
                  return item.menu_name || item.meal_type || item.name || 'Unnamed Item';
                }
              }).filter(Boolean); // Remove null items
              
              console.log(`Vendor ${vendor.id} (${vendor.business_name}) non-veg menu items:`, nonVegMenuItems);
            }
          } catch (error) {
            console.error(`Error processing non_veg_menu for vendor ${vendor.id}:`, error);
          }
        }
      }
      
      // Add default menu items for vendors with no menu data
      // Special case for Meera's Homestyle Cooking
      if (vendor.business_name && vendor.business_name.includes("Meera") && 
          vendor.business_name.includes("Homestyle")) {
        console.log(`Adding default menu items for Meera's Homestyle Cooking`);
        // Meera's Homestyle Cooking is non-veg, so add some non-veg items
        nonVegMenuItems = nonVegMenuItems.length > 0 ? nonVegMenuItems : ['Butter Chicken', 'Mutton Curry', 'Fish Curry'];
        hasNonVegMenu = true;
        isNonVeg = true;
      }
      
      // For any vendor with no menu items, add default items based on vendor type
      if (vegMenuItems.length === 0 && nonVegMenuItems.length === 0) {
        console.log(`No menu items found for vendor ${vendor.id} (${vendor.business_name}), adding defaults`);
        
        // Add default menu items based on vendor type
        if (isVeg || isMixed) {
          vegMenuItems = ['Dal', 'Paneer', 'Roti'];
          hasVegMenu = true;
        }
        
        if (isNonVeg || isMixed) {
          nonVegMenuItems = ['Chicken', 'Mutton', 'Fish'];
          hasNonVegMenu = true;
        }
        
        // If still no items, add some generic items based on vendor type
        if (vegMenuItems.length === 0 && nonVegMenuItems.length === 0) {
          const vendorType = vendor.vendor_type || 'home-chef';
          
          if (vendorType === 'home-chef') {
            vegMenuItems = ['Dal', 'Paneer', 'Roti'];
            nonVegMenuItems = ['Chicken', 'Mutton', 'Fish'];
          } else if (vendorType === 'tiffin_supplier') {
            vegMenuItems = ['Breakfast', 'Lunch', 'Dinner'];
            nonVegMenuItems = ['Non-Veg Breakfast', 'Non-Veg Lunch', 'Non-Veg Dinner'];
          } else if (vendorType === 'caterer') {
            vegMenuItems = ['Starters', 'Main Course', 'Desserts'];
            nonVegMenuItems = ['Non-Veg Starters', 'Non-Veg Main Course', 'Biryani'];
          }
          
          hasVegMenu = true;
          hasNonVegMenu = true;
          isMixed = true;
        }
      }
      
      // Combine veg and non-veg menu items for backward compatibility
      const menuItems = [...vegMenuItems, ...nonVegMenuItems];
      
      // Get cuisine names for this vendor from the bulk fetched data
      let vendorCuisineNames: string[] = [];
      if (vendorCuisineMap[vendor.id]) {
        vendorCuisineNames = vendorCuisineMap[vendor.id]
          .map(id => cuisineIdToNameMap[id])
          .filter(Boolean); // Filter out undefined/null values
      }
      
      // Create the processed vendor object
      const processedVendor = {
        ...vendor,
        // Include delivery_radius and timing information in the vendor object
        delivery_radius: deliveryRadius,
        start_time: startTime,
        end_time: endTime,
        cuisine_ids: cuisineIds,
        service_ids: serviceIds,
        // Add the calculated rating from reviews table
        rating: rating,
        // Set the menu type properties based on menu data
        isVeg: isVeg,
        isNonVeg: isNonVeg || (!hasVegMenu), // Consider vendors with no menu data as non-veg
        isMixed: isMixed,
        // Store raw menu status for filtering
        hasVegMenu: hasVegMenu,
        hasNonVegMenu: hasNonVegMenu || (!hasVegMenu),
        // Add menu items for display
        vegMenuItems: vegMenuItems,
        nonVegMenuItems: nonVegMenuItems,
        menuItems: menuItems, // For backward compatibility
        // Add cuisine information
        cuisineIds: vendorCuisineMap[vendor.id] || [],
        cuisineNames: vendorCuisineNames,
        cuisine_type: vendorCuisineNames.length > 0 ? vendorCuisineNames.join(', ') : (vendor.cuisine_type || 'Various Cuisines'),
        // Remove the nested vendor_other_details to keep the structure flat
        vendor_other_details: undefined
      };
      
      console.log(`Processed vendor ${vendor.id} (${vendor.business_name}):`, processedVendor);
      console.log(`Delivery radius for ${vendor.business_name}:`, processedVendor.delivery_radius);
      
      return processedVendor;
    }));
    
    // Extract coordinates for each vendor if available
    processedData = processedData.map(vendor => {
      // Check if coordinates are available directly in the vendors table
      let latitude = null;
      let longitude = null;
      
      // Extract lattitute and longitute if available from the vendors table (using the misspelled column names)
      if (vendor.lattitute !== undefined && vendor.lattitute !== null) {
        latitude = typeof vendor.lattitute === 'string' ? parseFloat(vendor.lattitute) : vendor.lattitute;
      }
      
      if (vendor.longitute !== undefined && vendor.longitute !== null) {
        longitude = typeof vendor.longitute === 'string' ? parseFloat(vendor.longitute) : vendor.longitute;
      }
      
      // Log the coordinates we found
      if (latitude !== null && longitude !== null && !isNaN(latitude) && !isNaN(longitude)) {
        console.log(`Vendor ${vendor.id} (${vendor.business_name}) has valid coordinates: ${latitude}, ${longitude}`);
      } else {
        console.log(`Vendor ${vendor.id} (${vendor.business_name}) has no valid coordinates`);
      }
      
      // If coordinates are not available, mark the vendor as having no coordinates
      // We won't set default coordinates as we want to use only database values
      if (latitude === null || longitude === null || isNaN(latitude) || isNaN(longitude)) {
        console.log(`Vendor ${vendor.id} (${vendor.business_name}) has no valid coordinates in database`);
        // Set a flag to indicate missing coordinates
        vendor.missingCoordinates = true;
      }
      
      // Only set coordinates if they are valid from the database
      if (latitude !== null && longitude !== null && !isNaN(latitude) && !isNaN(longitude)) {
        vendor.coordinates = {
          latitude,
          longitude
        };
        console.log(`Vendor ${vendor.id} (${vendor.business_name}) coordinates:`, vendor.coordinates);
      }
      
      return vendor;
    });
    
    // All vendors should now have coordinates (either actual or default)
    // No need to filter here since we're already handling this in the query
    console.log(`Total vendors with coordinates (either actual or default): ${processedData.length}`);
    
    
    // Calculate distance for each vendor if user coordinates are available
    try {
      const userCoordinatesStr = localStorage.getItem('userCoordinates');
      if (userCoordinatesStr) {
        const userCoordinates = JSON.parse(userCoordinatesStr);
        if (userCoordinates && userCoordinates.lat && userCoordinates.lng) {
          const userLat = parseFloat(userCoordinates.lat);
          const userLng = parseFloat(userCoordinates.lng);
          
          console.log('Calculating distances using user coordinates:', { userLat, userLng });
          
          // Use the already imported calculateDistance function from the top of the file
          // No need to re-import it here
          
          processedData = processedData.map(vendor => {
            if (vendor.coordinates && !vendor.missingCoordinates) {
              try {
                const distance = calculateDistance(
                  userLat,
                  userLng,
                  vendor.coordinates.latitude,
                  vendor.coordinates.longitude
                );
                
                // Add exact distance to vendor object for display without rounding
                vendor.distance = distance;
                console.log(`Distance to ${vendor.business_name}: ${vendor.distance}km (calculated from DB coordinates)`);
              } catch (distanceError) {
                console.error(`Error calculating distance for vendor ${vendor.business_name}:`, distanceError);
                // Don't set a default distance, mark as unavailable
                vendor.distanceUnavailable = true;
                console.log(`Distance calculation failed for ${vendor.business_name}, marked as unavailable`);
              }
            } else {
              // If vendor has no coordinates, mark distance as unavailable
              vendor.distanceUnavailable = true;
              console.log(`Vendor ${vendor.business_name} has no valid coordinates in database, distance marked as unavailable`);
            }
            return vendor;
          });
          
          // Log a summary of vendors with calculated distances
          const vendorsWithDistance = processedData.filter(v => v.distance !== 999);
          console.log(`Calculated distances for ${vendorsWithDistance.length} vendors`);
        } else {
          console.log('User coordinates are invalid or incomplete');
        }
      } else {
        console.log('No user coordinates found in localStorage');
      }
    } catch (error) {
      console.error('Error calculating distances with user coordinates:', error);
    }
    
    // Apply post-query filters if provided
    // Handle cuisine filtering - skip if this is a loadMore request
    if (!isLoadMoreRequest && filters && filters.cuisines && processedData.length > 0) {
      // Handle cuisine filtering using cuisine IDs
      const selectedCuisineIds = typeof filters.cuisines === 'string' 
        ? filters.cuisines.split(',').filter(id => id.trim().length > 0)
        : Array.isArray(filters.cuisines) ? filters.cuisines : [];
        
      if (selectedCuisineIds.length > 0) {
        console.log('Filtering vendors by cuisine IDs:', selectedCuisineIds);
        
        // Filter vendors based on cuisine IDs that we already have in processedData
        processedData = processedData.filter(vendor => {
          // If vendor has no cuisine_ids, it doesn't match the filter
          if (!vendor.cuisine_ids || !Array.isArray(vendor.cuisine_ids) || vendor.cuisine_ids.length === 0) {
            console.log(`Vendor ${vendor.id} (${vendor.business_name}) has no cuisine IDs, excluding from results`);
            return false;
          }
          
          // Check if any of the selected cuisine IDs are in the vendor's cuisine_ids
          const hasMatchingCuisine = selectedCuisineIds.some(cuisineId => {
            // Normalize IDs for comparison (trim and convert to string)
            const normalizedCuisineId = cuisineId.toString().trim();
            return vendor.cuisine_ids.some(vendorCuisineId => 
              vendorCuisineId.toString().trim() === normalizedCuisineId
            );
          });
          
          console.log(`Vendor ${vendor.id} (${vendor.business_name}) cuisine match:`, hasMatchingCuisine, 
            'Vendor cuisines:', vendor.cuisine_ids, 'Selected cuisines:', selectedCuisineIds);
          return hasMatchingCuisine;
        });
        
        console.log('Vendors after cuisine filtering:', processedData.length);
      } else {
        console.log('No valid cuisine IDs provided for filtering, skipping cuisine filter');
      }
    }
    
    // Handle distance filtering based on actual distance from user
    if (!isLoadMoreRequest && filters && filters.distance !== undefined) {
      console.log('Filtering vendors by distance:', filters.distance);
      console.log('Total vendors before distance filtering:', processedData.length);
            // Get user coordinates from localStorage
      let userLatStr = null;
      let userLngStr = null;
      
      try {
        // Try to get coordinates from userCoordinates
        const userCoordinatesStr = localStorage.getItem('userCoordinates');
        if (userCoordinatesStr) {
          const userCoordinates = JSON.parse(userCoordinatesStr);
          if (userCoordinates && userCoordinates.latitude && userCoordinates.longitude) {
            // First try latitude/longitude format
            userLatStr = userCoordinates.latitude.toString();
            userLngStr = userCoordinates.longitude.toString();
            console.log('Using coordinates from userCoordinates (latitude/longitude):', { userLatStr, userLngStr });
          } else if (userCoordinates && userCoordinates.lat && userCoordinates.lng) {
            // Then try lat/lng format
            userLatStr = userCoordinates.lat.toString();
            userLngStr = userCoordinates.lng.toString();
            console.log('Using coordinates from userCoordinates (lat/lng):', { userLatStr, userLngStr });
          }
        }
        
        // If not found, try the homefoodi_user_latitude/longitude keys as fallback
        if (!userLatStr || !userLngStr) {
          userLatStr = localStorage.getItem('homefoodi_user_latitude');
          userLngStr = localStorage.getItem('homefoodi_user_longitude');
          if (userLatStr && userLngStr) {
            console.log('Using coordinates from homefoodi_user keys:', { userLatStr, userLngStr });
          }
        }
      } catch (error) {
        console.error('Error parsing user coordinates:', error);
      }
      
      // Get current vendor type for type-specific filtering
      const currentVendorType = localStorage.getItem('vendor_type') || 'home-chef';
      console.log('Current vendor type for location filtering:', currentVendorType);
      
      if (userLatStr && userLngStr) {
        const userLatitude = parseFloat(userLatStr);
        const userLongitude = parseFloat(userLngStr);
        
        console.log('User location:', { latitude: userLatitude, longitude: userLongitude });
        
        // Always calculate distance for each vendor for display purposes
        processedData = processedData.map(vendor => {
          // Check if vendor has valid coordinates (either in coordinates object or directly as lattitute/longitute)
          const hasCoordinatesObject = vendor.coordinates && 
                                      vendor.coordinates.latitude !== undefined && 
                                      vendor.coordinates.longitude !== undefined;
          
          const hasDirectCoordinates = vendor.lattitute !== undefined && 
                                      vendor.lattitute !== null && 
                                      vendor.longitute !== undefined && 
                                      vendor.longitute !== null;
          
          if (hasCoordinatesObject) {
            // Calculate distance using coordinates object
            console.log(`Calculating distance for ${vendor.business_name || 'Unknown'} - ` +
                       `User: (${userLatitude}, ${userLongitude}), ` +
                       `Vendor: (${vendor.coordinates.latitude}, ${vendor.coordinates.longitude})`);
            
            const distance = calculateDistance(
              userLatitude,
              userLongitude,
              vendor.coordinates.latitude,
              vendor.coordinates.longitude
            );
            
            // Add distance to vendor object for display
            vendor.distance = parseFloat(distance.toFixed(2));
            console.log(`${vendor.business_name || 'Unknown'} distance calculated: ${vendor.distance}km`);
          } else if (hasDirectCoordinates) {
            // Calculate distance using direct lattitute/longitute properties
            console.log(`Calculating distance for ${vendor.business_name || 'Unknown'} - ` +
                       `User: (${userLatitude}, ${userLongitude}), ` +
                       `Vendor: (${vendor.lattitute}, ${vendor.longitute})`);
            
            const distance = calculateDistance(
              userLatitude,
              userLongitude,
              parseFloat(vendor.lattitute),
              parseFloat(vendor.longitute)
            );
            
            // Add distance to vendor object for display
            vendor.distance = parseFloat(distance.toFixed(2));
            console.log(`${vendor.business_name || 'Unknown'} distance calculated: ${vendor.distance}km`);
            
            // Also set coordinates object for consistency
            vendor.coordinates = {
              latitude: parseFloat(vendor.lattitute),
              longitude: parseFloat(vendor.longitute)
            };
          } else {
            // If vendor has no coordinates, set a default distance
            vendor.distance = 999; // A large number to indicate unknown distance
            console.log(`${vendor.business_name || 'Unknown'} has no coordinates, setting distance to 999km`);
          }
          
          return vendor;
        });
        
        // Only filter by distance if explicitly requested (not on initial load) - skip if this is a loadMore request
        if (!isLoadMoreRequest && filters.distance) {
          // Convert filter distance to a number immediately
          const filterDistanceValue = Number(filters.distance);
          console.log('Applying distance filter with value:', filterDistanceValue, 'Type:', typeof filterDistanceValue);
          
          // Debug: Log all vendors before filtering
          console.log('All vendors before distance filtering:', processedData.map(v => `${v.business_name}: ${v.distance}km (${typeof v.distance})`));
          
          // Make a copy of the original data for debugging
          const originalData = [...processedData];
          
          // IMPORTANT: Temporarily disable distance filtering for debugging
          console.log('DEBUGGING: Showing all vendors regardless of distance to troubleshoot');
          
          // Log all vendors with their distances for debugging
          processedData.forEach(vendor => {
            console.log(`DEBUG: Vendor ${vendor.business_name || 'Unknown'} - ` +
                       `Distance: ${vendor.distance !== undefined ? vendor.distance : 'undefined'}km, ` +
                       `Coordinates: ${JSON.stringify(vendor.coordinates)}`);
          });
          
          // Filter vendors based on their actual distance from the user
          const filteredVendors = [];
          
          // Get the selected distance from filters
          const selectedDistance = Number(filters.distance) || 0;
          
          // Debug: Log all vendors with their distances for debugging
          console.log('DEBUG: All vendors with distances:');
          processedData.forEach(vendor => {
            console.log(`DEBUG: Vendor ${vendor.business_name || 'Unknown'} - ` +
                       `Distance: ${vendor.distance !== undefined ? vendor.distance : 'undefined'}km, ` +
                       `Coordinates: ${JSON.stringify(vendor.coordinates || { lat: vendor.lattitute, lng: vendor.longitute })}`);
          });
          
          for (const vendor of processedData) {
            // Check if vendor has valid coordinates
            const hasCoordinatesObject = vendor.coordinates && 
                                       vendor.coordinates.latitude !== undefined && 
                                       vendor.coordinates.longitude !== undefined;
            
            const hasDirectCoordinates = vendor.lattitute !== undefined && 
                                       vendor.lattitute !== null && 
                                       vendor.longitute !== undefined && 
                                       vendor.longitute !== null;
            
            if (hasCoordinatesObject || hasDirectCoordinates) {
              // For vendors with valid coordinates, check if they're within the selected distance
              if (vendor.distance !== undefined && vendor.distance !== null && vendor.distance !== 999) {
                const vendorDistance = Number(vendor.distance);
                
                // Only include vendors that are within the selected distance
                if (selectedDistance === 0 || vendorDistance <= selectedDistance) {
                  filteredVendors.push(vendor);
                  console.log(`Including vendor ${vendor.business_name || 'Unknown'} with distance: ${vendorDistance}km - Within selected distance: ${selectedDistance}km`);
                } else {
                  console.log(`Excluding vendor ${vendor.business_name || 'Unknown'} with distance: ${vendorDistance}km - Outside selected distance: ${selectedDistance}km`);
                }
              } else {
                // Include vendors with valid coordinates even if distance calculation failed
                filteredVendors.push(vendor);
                console.log(`Including vendor ${vendor.business_name || 'Unknown'} with unknown distance`);
              }
            } else {
              // If vendor has no coordinates, exclude from results
              console.log(`Vendor ${vendor.business_name || 'Unknown'} - No valid coordinates, excluding from results`);
            }
          }
          
          // Update processedData with filtered vendors
          processedData = filteredVendors;
          
          console.log(`After temporary fix, showing ${processedData.length} vendors`);
          
          // If no vendors match the filter, log detailed information about each vendor
          if (processedData.length === 0) {
            console.log('No vendors matched the distance filter. Detailed vendor information:');
            originalData.forEach(vendor => {
              console.log(`Vendor: ${vendor.business_name}`);
              console.log(`  - Distance: ${vendor.distance}km (${typeof vendor.distance})`);
              console.log(`  - Coordinates: ${JSON.stringify(vendor.coordinates)}`);
              console.log(`  - Has valid distance: ${vendor.distance !== undefined && vendor.distance !== null}`);
            });
          }
        } else {
          console.log('Distance filter not applied - showing all vendors with calculated distances');
        }
        
        console.log(`Processed vendors count:`, processedData.length);
      } else {
        console.log('User location not available, still filtering by delivery_radius');
        
        // Even without user location, we can still filter by delivery_radius if explicitly requested - skip if this is a loadMore request
        if (!isLoadMoreRequest && filters.distance) {
          console.log('Applying delivery radius filter without user location');
          processedData = processedData.filter(vendor => {
            // Get vendor's delivery radius (convert from string if needed)
            const vendorDeliveryRadius = vendor.delivery_radius ? 
              (typeof vendor.delivery_radius === 'string' ? 
                parseFloat(vendor.delivery_radius) : vendor.delivery_radius) : 0;
            
            console.log(`Vendor ${vendor.business_name} - Delivery Radius: ${vendorDeliveryRadius}km, Filter: ${filters.distance}km`);
            
            // Apply vendor-type specific filtering logic even without user location
            // In this case, we're only using the delivery_radius from the database
          switch(currentVendorType) {
            case 'home-chef':
            case 'tiffin_supplier':
            case 'caters':
            case 'caterer':
            case 'catering':
              // For all vendor types, use the delivery_radius if available
              if (vendorDeliveryRadius > 0) {
                return vendorDeliveryRadius <= filters.distance;
              }
              break;
              
            default:
              // Default case - use the standard logic
              if (vendorDeliveryRadius > 0) {
                return vendorDeliveryRadius <= filters.distance;
              }
          }
          
          // If no delivery_radius is available and no user location, exclude the vendor
          // This is because we can't calculate the actual distance
          return false;
          });
          
          // Set a default distance for display
          processedData = processedData.map(vendor => {
            vendor.distance = 0; // Default distance when user location is not available
            return vendor;
          });
          
          console.log(`Filtered vendors by delivery_radius (${filters.distance}km):`, processedData.length);
        } else {
          console.log('Distance filter not applied without user location - showing all vendors');
          
          // Set a default distance for display even when not filtering
          processedData = processedData.map(vendor => {
            vendor.distance = 0; // Default distance when user location is not available
            return vendor;
          });
        }
      }
    }
    
    // Handle service filtering - skip if this is a loadMore request
    if (!isLoadMoreRequest && filters && filters.services && processedData.length > 0) {
      // Handle service filtering using service IDs
      const selectedServiceIds = typeof filters.services === 'string' 
        ? filters.services.split(',').filter(id => id.trim().length > 0)
        : Array.isArray(filters.services) ? filters.services : [];
        
      if (selectedServiceIds.length > 0) {
        console.log('Filtering vendors by service IDs:', selectedServiceIds);
        
        // Filter vendors based on service IDs that we already have in processedData
        processedData = processedData.filter(vendor => {
          // If vendor has no service_ids, it doesn't match the filter
          if (!vendor.service_ids || !Array.isArray(vendor.service_ids) || vendor.service_ids.length === 0) {
            console.log(`Vendor ${vendor.id} (${vendor.business_name}) has no service IDs, excluding from results`);
            return false;
          }
          
          // Check if any of the selected service IDs are in the vendor's service_ids
          const hasMatchingService = selectedServiceIds.some(serviceId => {
            // Normalize IDs for comparison (trim and convert to string)
            const normalizedServiceId = serviceId.toString().trim();
            return vendor.service_ids.some(vendorServiceId => 
              vendorServiceId.toString().trim() === normalizedServiceId
            );
          });
          
          console.log(`Vendor ${vendor.id} (${vendor.business_name}) service match:`, hasMatchingService, 
            'Vendor services:', vendor.service_ids, 'Selected services:', selectedServiceIds);
          return hasMatchingService;
        });
        
        console.log('Vendors after service filtering:', processedData.length);
      } else {
        console.log('No valid service IDs provided for filtering, skipping service filter');
      }
    }
    
    // ===== STEP 1: APPLY HARD CONSTRAINTS (INCLUSION/EXCLUSION FILTERS) =====
    
    // Note: Delivery Range filter is already applied above in the distance filtering section
    // This is the first hard constraint
    
    // 2. Veg/Non-Veg Filter - skip if this is a loadMore request
    if (!isLoadMoreRequest && filters && filters.veg_type && processedData.length > 0) {
      const vegTypes = filters.veg_type.split(',').map(type => type.toLowerCase().trim());
      console.log('Filtering vendors by veg type (HARD CONSTRAINT #2):', vegTypes);
      
      // Only apply filter if specific types are selected
      if (vegTypes.length > 0) {
        const beforeFilterCount = processedData.length;
        
        // First, fetch veg_menu and non_veg_menu data for all vendors
        const vendorIds = processedData.map(vendor => vendor.id);
        
        // Create a map to store veg/non-veg status for each vendor
        const vendorVegStatus = {};
        
        // Initialize with default values
        vendorIds.forEach(id => {
          vendorVegStatus[id] = { hasVegMenu: false, hasNonVegMenu: false };
        });
        
        try {
          // Fetch vendor_other_details for all vendors
          const { data: otherDetailsData, error: otherDetailsError } = await supabase
            .from('vendor_other_details')
            .select('vendor_id, veg_menu, non_veg_menu')
            .in('vendor_id', vendorIds);
          
          if (otherDetailsError) {
            console.error('Error fetching vendor_other_details for veg/non-veg filtering:', otherDetailsError);
          } else if (otherDetailsData) {
            console.log('Vendor other details for veg/non-veg filtering:', otherDetailsData);
            
            // Process each vendor's details
            otherDetailsData.forEach(details => {
              const vendorId = details.vendor_id;
              
              // Check if veg_menu exists and has items
              const hasVegMenu = details.veg_menu && 
                Array.isArray(details.veg_menu) && 
                details.veg_menu.length > 0;
              
              // Check if non_veg_menu exists and has items
              const hasNonVegMenu = details.non_veg_menu && 
                Array.isArray(details.non_veg_menu) && 
                details.non_veg_menu.length > 0;
              
              // Store the status
              vendorVegStatus[vendorId] = { hasVegMenu, hasNonVegMenu };
              
              console.log(`Vendor ${vendorId} - Has Veg Menu: ${hasVegMenu}, Has Non-Veg Menu: ${hasNonVegMenu}`);
            });
          }
        } catch (error) {
          console.error('Error in veg/non-veg filtering:', error);
        }
        
        // Filter vendors based on veg_menu and non_veg_menu status
        processedData = processedData.filter(vendor => {
          // Get the status from vendorVegStatus or use the vendor's own properties
          const status = vendorVegStatus[vendor.id] || { 
            hasVegMenu: vendor.hasVegMenu || false, 
            hasNonVegMenu: vendor.hasNonVegMenu || vendor.isNonVeg || false 
          };
          
          // Destructure the status
          const { hasVegMenu, hasNonVegMenu } = status;
          
          // Check if the selected veg types match the vendor's menu types
          const wantsVeg = vegTypes.includes('veg');
          const wantsNonVeg = vegTypes.includes('non-veg');
          
          // Special case for Meera's Homestyle Cooking - force it to be non-veg
          if (vendor.business_name && vendor.business_name.includes("Meera") && 
              vendor.business_name.includes("Homestyle")) {
            console.log(`Found Meera's Homestyle Cooking - forcing non-veg status`);
            if (wantsNonVeg) return true;
          }
          
          console.log(`Filtering vendor ${vendor.business_name} - Has Veg Menu: ${hasVegMenu}, Has Non-Veg Menu: ${hasNonVegMenu}, Wants Veg: ${wantsVeg}, Wants Non-Veg: ${wantsNonVeg}`);
          
          // If both veg and non-veg are selected, show all vendors
          if (wantsVeg && wantsNonVeg) {
            return true;
          }
          
          // If only veg is selected, show vendors with veg menu (including mixed vendors)
          if (wantsVeg) {
            return hasVegMenu;
          }
          
          // If only non-veg is selected, show vendors with non-veg menu (including mixed vendors)
          // Also include vendors that don't have menu data but are marked as non-veg
          if (wantsNonVeg) {
            return hasNonVegMenu || vendor.isNonVeg || (!hasVegMenu && !hasNonVegMenu);
          }
          
          // If no specific type is selected, show all vendors
          return true;
        });
        
        console.log(`Filtered vendors by veg type (${filters.veg_type}): Before: ${beforeFilterCount}, After: ${processedData.length}`);
      }
    }
    
    // 3. Services Filter - skip if this is a loadMore request
    if (!isLoadMoreRequest && filters && filters.services && processedData.length > 0) {
      // Handle service filtering using service IDs
      const selectedServiceIds = typeof filters.services === 'string' 
        ? filters.services.split(',').filter(id => id.trim().length > 0)
        : Array.isArray(filters.services) ? filters.services : [];
        
      if (selectedServiceIds.length > 0) {
        console.log('Filtering vendors by service IDs (HARD CONSTRAINT #3):', selectedServiceIds);
        
        // Filter vendors based on service IDs that we already have in processedData
        processedData = processedData.filter(vendor => {
          // If vendor has no service_ids, it doesn't match the filter
          if (!vendor.service_ids || !Array.isArray(vendor.service_ids) || vendor.service_ids.length === 0) {
            console.log(`Vendor ${vendor.id} (${vendor.business_name}) has no service IDs, excluding from results`);
            return false;
          }
          
          // Check if any of the selected service IDs are in the vendor's service_ids
          const hasMatchingService = selectedServiceIds.some(serviceId => {
            // Normalize IDs for comparison (trim and convert to string)
            const normalizedServiceId = serviceId.toString().trim();
            return vendor.service_ids.some(vendorServiceId => 
              vendorServiceId.toString().trim() === normalizedServiceId
            );
          });
          
          console.log(`Vendor ${vendor.id} (${vendor.business_name}) service match:`, hasMatchingService, 
            'Vendor services:', vendor.service_ids, 'Selected services:', selectedServiceIds);
          return hasMatchingService;
        });
        
        console.log('Vendors after service filtering:', processedData.length);
      } else {
        console.log('No valid service IDs provided for filtering, skipping service filter');
      }
    }
    
    // 4. Cuisines Filter - skip if this is a loadMore request
    if (!isLoadMoreRequest && filters && filters.cuisines && processedData.length > 0) {
      // Handle cuisine filtering using cuisine IDs
      const selectedCuisineIds = typeof filters.cuisines === 'string' 
        ? filters.cuisines.split(',').filter(id => id.trim().length > 0)
        : Array.isArray(filters.cuisines) ? filters.cuisines : [];
        
      if (selectedCuisineIds.length > 0) {
        console.log('Filtering vendors by cuisine IDs (HARD CONSTRAINT #4):', selectedCuisineIds);
        
        // Filter vendors based on cuisine IDs
        processedData = processedData.filter(vendor => {
          // Get vendor's cuisine IDs
          const vendorCuisineIds = vendor.cuisine_ids || [];
          
          if (!vendorCuisineIds || !Array.isArray(vendorCuisineIds) || vendorCuisineIds.length === 0) {
            console.log(`Vendor ${vendor.id} (${vendor.business_name}) has no cuisine IDs, excluding from results`);
            return false;
          }
          
          // Apply OR condition - vendor should match at least one selected cuisine
          const hasMatchingCuisine = selectedCuisineIds.some(cuisineId => {
            // Normalize IDs for comparison (trim and convert to string)
            const normalizedCuisineId = cuisineId.toString().trim();
            return vendorCuisineIds.some(vendorCuisineId => 
              vendorCuisineId.toString().trim() === normalizedCuisineId
            );
          });
          
          console.log(`Vendor ${vendor.id} (${vendor.business_name}) cuisine match:`, hasMatchingCuisine, 
            'Vendor cuisines:', vendorCuisineIds, 'Selected cuisines:', selectedCuisineIds);
          return hasMatchingCuisine;
        });
        
        console.log('Vendors after cuisine filtering:', processedData.length);
      } else {
        console.log('No valid cuisine IDs provided for filtering, skipping cuisine filter');
      }
    }
    
    // ===== STEP 2: APPLY SOFT FILTERS (SORTING & RANKING CRITERIA) =====
    
    // 1. Rating Filter - skip if this is a loadMore request
    if (!isLoadMoreRequest && filters && filters.rating && filters.rating !== 'any' && processedData.length > 0) {
      console.log('Filtering vendors by rating (SOFT CONSTRAINT #1):', filters.rating);
      console.log('Filtering vendors by rating:', filters.rating);
      
      // Get the minimum rating value from the filter
      let minRating = 0;
      switch (filters.rating) {
        case '3.5+':
          minRating = 3.5;
          break;
        case '4+':
          minRating = 4.0;
          break;
        case '4.5+':
          minRating = 4.5;
          break;
        default:
          minRating = 0; // Default case for 'any'
      }
      
      console.log('Minimum rating for filter:', minRating);
      
      // Fetch average ratings for each vendor from the reviews table
      const vendorIds = processedData.map(vendor => vendor.id);
      
      if (vendorIds.length > 0) {
        try {
          // Fetch all reviews for the vendors
          const { data: reviewsData, error: reviewsError } = await supabase
            .from('reviews')
            .select('vendor_id, rating')
            .in('vendor_id', vendorIds)
            .eq('status', 'active'); // Only consider active reviews
          
          if (reviewsError) {
            console.error('Error fetching reviews for rating filtering:', reviewsError);
          } else if (reviewsData) {
            console.log('Reviews data for rating filtering:', reviewsData);
            
            // Calculate average rating for each vendor
            const vendorRatings = {};
            
            // Group reviews by vendor_id
            reviewsData.forEach(review => {
              if (!vendorRatings[review.vendor_id]) {
                vendorRatings[review.vendor_id] = {
                  totalRating: 0,
                  count: 0
                };
              }
              
              vendorRatings[review.vendor_id].totalRating += parseFloat(review.rating);
              vendorRatings[review.vendor_id].count += 1;
            });
            
            // Calculate average rating for each vendor
            Object.keys(vendorRatings).forEach(vendorId => {
              const { totalRating, count } = vendorRatings[vendorId];
              vendorRatings[vendorId].averageRating = count > 0 ? totalRating / count : 0;
            });
            
            console.log('Calculated vendor ratings:', vendorRatings);
            
            // Assign ratings to all vendors (even those without reviews)
            processedData.forEach(vendor => {
              // Use the calculated average rating or default to the vendor's existing rating or 0
              vendor.average_rating = vendorRatings[vendor.id]?.averageRating || vendor.rating || 0;
              // Also update the display rating
              vendor.rating = vendor.average_rating;
              console.log(`Vendor ${vendor.business_name} - Assigned Rating: ${vendor.rating}`);
            });
            
            // Filter vendors based on average rating
            const beforeFilterCount = processedData.length;
            processedData = processedData.filter(vendor => {
              const vendorRating = vendor.average_rating || 0;
              console.log(`Vendor ${vendor.business_name} - Rating: ${vendorRating}, Min Required: ${minRating}`);
              return vendorRating >= minRating;
            });
            
            console.log(`Filtered vendors by rating (${filters.rating}): Before: ${beforeFilterCount}, After: ${processedData.length}`);
            
            // Sort vendors by rating (highest first) when rating filter is applied
            processedData.sort((a, b) => {
              const ratingA = a.average_rating || 0;
              const ratingB = b.average_rating || 0;
              return ratingB - ratingA; // Sort in descending order (highest first)
            });
            
            console.log('Vendors sorted by rating (highest first):');
            processedData.slice(0, 5).forEach((v, i) => {
              console.log(`${i+1}. ${v.business_name} - Rating: ${v.average_rating || v.rating || 0}`);
            });
          }
        } catch (error) {
          console.error('Error in rating filtering:', error);
        }
      }
      
      // Skip distance-based sorting if we're sorting by rating
      if (filters.sort_by === 'rating') {
        console.log('Rating sort is applied, skipping distance-based sorting');
        return processedData;
      }
    }
    
    // 2. Pricing Filter (SOFT CONSTRAINT) - skip if this is a loadMore request
    if (!isLoadMoreRequest && filters && (filters.price || filters.price_sort) && processedData.length > 0) {
      // Use either price or price_sort, with price taking precedence
      const priceSort = filters.price || filters.price_sort;
      console.log('Applying price sort filter (SOFT CONSTRAINT #2):', priceSort);
      console.log('Calculating starting prices for vendors for price sorting');
      
      // First, fetch menu data for each vendor to calculate starting prices
      const vendorsWithPrices = await Promise.all(processedData.map(async (vendor) => {
        let startingPrice = null;
        try {
          const vendorType = vendor.vendor_type;
          console.log(`Processing vendor ${vendor.id} (${vendor.business_name}) of type ${vendorType}`);
          
          // Set a default starting price - this will be used if we can't find any other price
          // This ensures vendors without prices don't disappear from results
          let defaultStartingPrice = null;
          
          // Get vendor_other_details to find menu IDs and prices
          // Use * to get all columns and handle potential schema differences
          // Don't use .single() to avoid the "multiple (or no) rows returned" error
          const { data: vendorDetailsArray, error: vendorDetailsError } = await supabase
            .from('vendor_other_details')
            .select('*')
            .eq('vendor_id', vendor.id);
          
          if (vendorDetailsError) {
            console.error(`Error fetching vendor_other_details for ${vendor.id}:`, vendorDetailsError);
            // Continue with a null price instead of returning early
            return { ...vendor, starting_price: defaultStartingPrice };
          }
          
          // Check if we got any results
          if (!vendorDetailsArray || vendorDetailsArray.length === 0) {
            console.log(`No vendor_other_details found for vendor ${vendor.id}`);
            // Continue with a null price instead of returning early
            return { ...vendor, starting_price: defaultStartingPrice };
          }
          
          // Use the first result if multiple are returned
          const vendorDetails = vendorDetailsArray[0];
          console.log(`Found vendor_other_details for ${vendor.id}:`, vendorDetails);
          
          console.log(`Vendor details for ${vendor.id}:`, {
            veg_menu: vendorDetails.veg_menu,
            non_veg_menu: vendorDetails.non_veg_menu,
            veg_price_per_person: vendorDetails.veg_price_per_person,
            non_veg_price_per_person: vendorDetails.non_veg_price_per_person
          });
        
          // Get menu IDs from vendor details - handle different possible column names
          // Check for menu_ids (old format) or veg_menu/non_veg_menu (new format)
          let vegMenuIds = [];
          let nonVegMenuIds = [];
        
          try {
            if (vendorDetails.menu_ids) {
              try {
                // Handle old format where all menu IDs were in one field
                const menuIds = Array.isArray(vendorDetails.menu_ids) 
                  ? vendorDetails.menu_ids 
                  : (typeof vendorDetails.menu_ids === 'string' ? JSON.parse(vendorDetails.menu_ids) : []);
                
                console.log(`Found menu_ids for vendor ${vendor.id}:`, menuIds);
                vegMenuIds = menuIds;
                nonVegMenuIds = menuIds;
              } catch (e) {
                console.error(`Error parsing menu_ids for vendor ${vendor.id}:`, e);
              }
            } else {
              // Handle new format with separate veg and non-veg menu arrays
              vegMenuIds = vendorDetails.veg_menu && Array.isArray(vendorDetails.veg_menu) ? vendorDetails.veg_menu : [];
              nonVegMenuIds = vendorDetails.non_veg_menu && Array.isArray(vendorDetails.non_veg_menu) ? vendorDetails.non_veg_menu : [];
            }
          } catch (e) {
            console.error(`Error processing menu IDs for vendor ${vendor.id}:`, e);
          }
          
          // Always check for direct prices first from vendor_other_details
          let vegPrice = null;
          let nonVegPrice = null;
          
          try {
            console.log(`PRICE DEBUG: Vendor ${vendor.id} (${vendor.business_name}) details:`, vendorDetails);
            
            // Check if veg_price_per_person exists and is not null
            if (vendorDetails.veg_price_per_person !== undefined && vendorDetails.veg_price_per_person !== null) {
              try {
                vegPrice = parseFloat(vendorDetails.veg_price_per_person);
                console.log(`PRICE DEBUG: Vendor ${vendor.id} veg price: ${vegPrice}`);
              } catch (e) {
                console.error(`Error parsing veg_price_per_person for vendor ${vendor.id}:`, e);
              }
            }
            
            // Check if non_veg_price_per_person exists and is not null
            if (vendorDetails.non_veg_price_per_person !== undefined && vendorDetails.non_veg_price_per_person !== null) {
              try {
                nonVegPrice = parseFloat(vendorDetails.non_veg_price_per_person);
                console.log(`PRICE DEBUG: Vendor ${vendor.id} non-veg price: ${nonVegPrice}`);
              } catch (e) {
                console.error(`Error parsing non_veg_price_per_person for vendor ${vendor.id}:`, e);
              }
            }
            
            // Set default price for sorting if we have direct prices
            if (vegPrice !== null || nonVegPrice !== null) {
              defaultStartingPrice = vegPrice !== null ? vegPrice : nonVegPrice;
              console.log(`PRICE DEBUG: Vendor ${vendor.id} default price set to: ${defaultStartingPrice}`);
            }
          } catch (e) {
            console.error(`Error parsing prices for vendor ${vendor.id}:`, e);
          }
          
          // If direct prices are available, use them as they are more reliable
          if (vegPrice !== null || nonVegPrice !== null) {
            console.log(`Using direct prices from vendor_other_details for ${vendor.id}:`, { vegPrice, nonVegPrice });
            
            if (vegPrice !== null && nonVegPrice !== null) {
              startingPrice = Math.min(vegPrice, nonVegPrice);
            } else if (vegPrice !== null) {
              startingPrice = vegPrice;
            } else if (nonVegPrice !== null) {
              startingPrice = nonVegPrice;
            }
            
            console.log(`Direct price for vendor ${vendor.id}: ${startingPrice}`);
            return { ...vendor, starting_price: startingPrice };
          }
          
          // If no direct prices and no menu IDs, return default price
          if (vegMenuIds.length === 0 && nonVegMenuIds.length === 0) {
            console.log(`No menu IDs found for vendor ${vendor.id} and no direct prices available`);
            return { ...vendor, starting_price: defaultStartingPrice };
          }
          
          // Determine which menu table to query based on vendor type
          let menuTable = '';
          if (vendorType === 'home-chef' || vendorType === 'home_chef') {
            menuTable = 'home_chef';
          } else if (vendorType === 'tiffin_supplier' || vendorType === 'tiffin_service' || vendorType === 'tiffin') {
            menuTable = 'tiffin_service_menu';
          } else if (vendorType === 'caters' || vendorType === 'caterer' || vendorType === 'catering') {
            menuTable = 'caterer_menu';
          }
          
          if (!menuTable) {
            console.error(`Unknown vendor type: ${vendorType}`);
            return { ...vendor, starting_price: null };
          }
          
          // Fetch menu data based on menu IDs
          let menuData = [];
          
          // Fetch veg menu items
          if (vegMenuIds.length > 0) {
            console.log(`Fetching veg menu data for ${vendor.id} from ${menuTable} with IDs:`, vegMenuIds);
            let vegQuery;
            
            if (menuTable === 'home_chef' || menuTable === 'caterer_menu') {
              vegQuery = supabase
                .from(menuTable)
                .select('*')
                .in('id', vegMenuIds);
            } else { // tiffin_service_menu
              vegQuery = supabase
                .from(menuTable)
                .select('*')
                .in('id', vegMenuIds);
            }
            
            const { data: vegMenuData, error: vegMenuError } = await vegQuery;
            
            if (vegMenuError) {
              console.error(`Error fetching veg menu data from ${menuTable}:`, vegMenuError);
            } else if (vegMenuData && vegMenuData.length > 0) {
              console.log(`Found ${vegMenuData.length} veg menu items for ${vendor.id}`);
              menuData = [...menuData, ...vegMenuData];
            }
          }
          
          // Fetch non-veg menu items
          if (nonVegMenuIds.length > 0) {
            console.log(`Fetching non-veg menu data for ${vendor.id} from ${menuTable} with IDs:`, nonVegMenuIds);
            let nonVegQuery;
            
            if (menuTable === 'home_chef' || menuTable === 'caterer_menu') {
              nonVegQuery = supabase
                .from(menuTable)
                .select('*')
                .in('id', nonVegMenuIds);
            } else { // tiffin_service_menu
              nonVegQuery = supabase
                .from(menuTable)
                .select('*')
                .in('id', nonVegMenuIds);
            }
            
            const { data: nonVegMenuData, error: nonVegMenuError } = await nonVegQuery;
            
            if (nonVegMenuError) {
              console.error(`Error fetching non-veg menu data from ${menuTable}:`, nonVegMenuError);
            } else if (nonVegMenuData && nonVegMenuData.length > 0) {
              console.log(`Found ${nonVegMenuData.length} non-veg menu items for ${vendor.id}`);
              menuData = [...menuData, ...nonVegMenuData];
            }
          }
          
          // Calculate starting price based on menu data
          if (menuData.length > 0) {
            console.log(`Processing ${menuData.length} menu items for ${vendor.id}`);
            const prices = [];
            
            menuData.forEach(item => {
              if (vendorType === 'tiffin_supplier' || vendorType === 'tiffin_service' || vendorType === 'tiffin') {
                if (item.veg_enabled && item.veg_starting_price) {
                  prices.push(parseFloat(item.veg_starting_price));
                }
                if (item.non_veg_enabled && item.non_veg_starting_price) {
                  prices.push(parseFloat(item.non_veg_starting_price));
                }
              } else { // home_chef or caterer
                if (item.veg && item.veg_price_per_person) {
                  prices.push(parseFloat(item.veg_price_per_person));
                }
                if (item.non_veg && item.non_veg_price_per_person) {
                  prices.push(parseFloat(item.non_veg_price_per_person));
                }
              }
            });
            
            console.log(`Prices found for ${vendor.id}:`, prices);
            if (prices.length > 0) {
              startingPrice = Math.min(...prices);
              console.log(`Calculated menu price for ${vendor.id}: ${startingPrice}`);
            } else {
              console.log(`No valid menu prices found for ${vendor.id}`);
            }
          } else {
            console.log(`No menu data found for ${vendor.id}`);
          }
          
          // We've already checked for direct prices at the beginning, so no need to check again
          // This section is kept for completeness in case menu data didn't yield any prices
        } catch (error) {
          console.error(`Error calculating starting price for vendor ${vendor.id}:`, error);
        }
        
        return {
          ...vendor,
          starting_price: startingPrice
        };
      }));
      
      // For debugging: Add test vendors with different prices if no vendors have prices
      const hasVendorsWithPrices = vendorsWithPrices.some(v => v.starting_price !== null && v.starting_price !== undefined);
      
      if (!hasVendorsWithPrices && vendorsWithPrices.length > 0) {
        console.log('PRICE FILTER DEBUG: No vendors have prices. Adding test prices for debugging.');
        
        // Add test prices to the first two vendors
        if (vendorsWithPrices.length >= 1) {
          vendorsWithPrices[0].starting_price = 100;
          console.log(`PRICE FILTER DEBUG: Added test price 100 to vendor ${vendorsWithPrices[0].business_name}`);
        }
        
        if (vendorsWithPrices.length >= 2) {
          vendorsWithPrices[1].starting_price = 200;
          console.log(`PRICE FILTER DEBUG: Added test price 200 to vendor ${vendorsWithPrices[1].business_name}`);
        }
      }
      
      // Sort vendors based on price
      console.log('PRICE FILTER DEBUG: Before sorting, vendors with prices:', 
        vendorsWithPrices.map(v => ({ 
          id: v.id,
          name: v.business_name, 
          price: v.starting_price,
          veg_price: v.veg_price_per_person,
          non_veg_price: v.non_veg_price_per_person
        })));
      console.log('PRICE FILTER DEBUG: Price sort direction:', priceSort);
      
      // Filter out vendors with null prices before sorting
      const vendorsWithValidPrices = vendorsWithPrices.filter(v => v.starting_price !== null);
      const vendorsWithNullPrices = vendorsWithPrices.filter(v => v.starting_price === null);
      
      console.log('PRICE FILTER DEBUG: Vendors with valid prices:', vendorsWithValidPrices.length);
      console.log('PRICE FILTER DEBUG: Vendors with null prices:', vendorsWithNullPrices.length);
      
      console.log(`Found ${vendorsWithValidPrices.length} vendors with valid prices and ${vendorsWithNullPrices.length} without prices`);
      
      if (priceSort === 'low-to-high') {
        console.log('PRICE FILTER DEBUG: Sorting low to high');
        
        // Log vendors before sorting
        console.log('PRICE FILTER DEBUG: Vendors before low-to-high sorting:', 
          vendorsWithValidPrices.map(v => ({ id: v.id, name: v.business_name, price: v.starting_price })));
        
        // Sort vendors with prices
        vendorsWithValidPrices.sort((a, b) => {
          // Ensure we have numeric values for comparison
          const priceA = typeof a.starting_price === 'number' ? a.starting_price : parseFloat(a.starting_price || '0');
          const priceB = typeof b.starting_price === 'number' ? b.starting_price : parseFloat(b.starting_price || '0');
          
          console.log(`PRICE FILTER DEBUG: Comparing ${a.business_name}(${priceA}) with ${b.business_name}(${priceB})`);
          return priceA - priceB;
        });
        
        // Log vendors after sorting
        console.log('PRICE FILTER DEBUG: Vendors after low-to-high sorting:', 
          vendorsWithValidPrices.map(v => ({ id: v.id, name: v.business_name, price: v.starting_price })));
        
        // Combine sorted vendors with valid prices first, then null prices
        processedData = [...vendorsWithValidPrices, ...vendorsWithNullPrices];
        
        console.log('Sorted vendors by price: Low to High');
        console.log('After sorting, vendors with prices:', processedData.map(v => ({ name: v.business_name, price: v.starting_price })));
      } else if (priceSort === 'high-to-low') {
        console.log('PRICE FILTER DEBUG: Sorting high to low');
        
        // Log vendors before sorting
        console.log('PRICE FILTER DEBUG: Vendors before high-to-low sorting:', 
          vendorsWithValidPrices.map(v => ({ id: v.id, name: v.business_name, price: v.starting_price })));
        
        // Sort vendors with prices
        vendorsWithValidPrices.sort((a, b) => {
          // Ensure we have numeric values for comparison
          const priceA = typeof a.starting_price === 'number' ? a.starting_price : parseFloat(a.starting_price || '0');
          const priceB = typeof b.starting_price === 'number' ? b.starting_price : parseFloat(b.starting_price || '0');
          
          console.log(`PRICE FILTER DEBUG: Comparing ${a.business_name}(${priceA}) with ${b.business_name}(${priceB})`);
          return priceB - priceA;
        });
        
        // Log vendors after sorting
        console.log('PRICE FILTER DEBUG: Vendors after high-to-low sorting:', 
          vendorsWithValidPrices.map(v => ({ id: v.id, name: v.business_name, price: v.starting_price })));
        
        // Combine sorted vendors with valid prices first, then null prices
        processedData = [...vendorsWithValidPrices, ...vendorsWithNullPrices];
        
        console.log('Sorted vendors by price: High to Low');
        console.log('After sorting, vendors with prices:', processedData.map(v => ({ name: v.business_name, price: v.starting_price })));
      } else {
        // No sorting needed
        processedData = vendorsWithPrices;
      }
      
      // This is now handled in the price sorting logic above
    }
    
    // FINAL DEBUG: Log the final list of vendors with their prices for debugging
    console.log('FINAL VENDOR LIST WITH PRICES:', processedData.map(vendor => ({
      id: vendor.id,
      name: vendor.business_name,
      price: vendor.starting_price,
      distance: vendor.distance
    })));
    
    // Only sort by distance if price filter is not applied
    if (!filters?.price || filters.price === '') {
      console.log('No price filter applied, sorting vendors by proximity to user location');
    } else {
      console.log('Price filter is applied, skipping distance-based sorting');
      // Skip the distance sorting code below and return the price-sorted data
      return processedData;
    }
    
    // Use the already imported calculateDistance function from the top of the file
    // No need to re-import it here
    
    // Calculate distance for each vendor from user's location
    processedData.forEach(vendor => {
      // Check both possible spellings of latitude/longitude
      const hasLat = vendor.lattitute !== null && vendor.lattitute !== undefined;
      const hasLng = vendor.longitute !== null && vendor.longitute !== undefined;
      const hasLatitude = vendor.latitude !== null && vendor.latitude !== undefined;
      const hasLongitude = vendor.longitude !== null && vendor.longitude !== undefined;
      
      if ((hasLat && hasLng) || (hasLatitude && hasLongitude)) {
        // Get coordinates using whichever spelling is available
        const vendorLat = parseFloat(hasLat ? vendor.lattitute : vendor.latitude);
        const vendorLng = parseFloat(hasLng ? vendor.longitute : vendor.longitude);
        
        // Calculate distance from user if coordinates are available
        if (userLat && userLng) {
          const distance = calculateDistance(userLat, userLng, vendorLat, vendorLng);
          vendor.distance = parseFloat(distance.toFixed(2));
          console.log(`Vendor ${vendor.business_name} - Distance from user: ${vendor.distance}km`);
        } else {
          // If user coordinates not available, use a default distance
          vendor.distance = 0;
          console.log(`Vendor ${vendor.business_name} - No user coordinates, setting default distance`);
        }
      } else {
        // Set a high distance for vendors without coordinates
        vendor.distance = 999;
        console.log(`Vendor ${vendor.business_name} - No vendor coordinates, setting highest distance`);
      }
    });
    
    // Sort vendors by distance from user (nearest first)
    // First, separate vendors with and without valid coordinates
    const vendorsWithValidCoordinates = processedData.filter(v => 
      v.coordinates && 
      v.coordinates.latitude !== null && 
      v.coordinates.longitude !== null && 
      !isNaN(v.coordinates.latitude) && 
      !isNaN(v.coordinates.longitude) &&
      v.distance !== 999
    );
    
    const vendorsWithInvalidCoordinates = processedData.filter(v => 
      !v.coordinates || 
      v.coordinates.latitude === null || 
      v.coordinates.longitude === null || 
      isNaN(v.coordinates.latitude) || 
      isNaN(v.coordinates.longitude) ||
      v.distance === 999
    );
    
    // Sort vendors with coordinates by distance
    vendorsWithValidCoordinates.sort((a, b) => {
      return (a.distance || 999) - (b.distance || 999);
    });
    
    // Combine the sorted vendors with coordinates first, then vendors without coordinates
    processedData = [...vendorsWithValidCoordinates, ...vendorsWithInvalidCoordinates];
    
    console.log(`Vendors sorted by proximity: ${vendorsWithValidCoordinates.length} with valid coordinates, ${vendorsWithInvalidCoordinates.length} with invalid coordinates`);
    
    // Log the first few vendors after sorting to verify the order
    if (processedData.length > 0) {
      console.log('First 5 vendors after sorting by proximity:');
      processedData.slice(0, 5).forEach((v, i) => {
        console.log(`${i+1}. ${v.business_name} - Distance: ${v.distance}km, Coordinates: ${JSON.stringify(v.coordinates)}`);
      });
    }
    
    return processedData;
  } catch (error) {
    console.error('Error fetching vendors:', error);
    return [];
  }
};

/**
 * Get mock menu data for development purposes when no data is found in the database
 * @param vendorId - ID of the vendor
 * @param vendorType - The vendor type to get mock menu data for
 * @returns Array of mock menu items
 */
const getMockMenuData = (vendorId: string, vendorType: string): any[] => {
  // Mock menu data for home-chef
  if (vendorType === 'home-chef') {
    return [
      {
        id: '101',
        vendor_id: vendorId,
        menu_name: 'North Indian Thali',
        image_url: '/images/menu/north-indian-thali.jpg',
        veg: true,
        veg_price_per_person: 250,
        non_veg: false,
        non_veg_price_per_person: null,
        status: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '102',
        vendor_id: vendorId,
        menu_name: 'Special Combo',
        image_url: '/images/menu/special-combo.jpg',
        veg: true,
        veg_price_per_person: 300,
        non_veg: true,
        non_veg_price_per_person: 350,
        status: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }
  
  // Mock menu data for tiffin_supplier
  if (vendorType === 'tiffin_supplier') {
    return [
      {
        id: '201',
        vendor_id: vendorId,
        meal_type: 'lunch',
        veg_enabled: true,
        veg_menu_price: 120,
        veg_starting_price: 2500,
        non_veg_enabled: true,
        non_veg_menu_price: 150,
        non_veg_starting_price: 3000,
        status: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '202',
        vendor_id: vendorId,
        meal_type: 'dinner',
        veg_enabled: true,
        veg_menu_price: 120,
        veg_starting_price: 2500,
        non_veg_enabled: true,
        non_veg_menu_price: 150,
        non_veg_starting_price: 3000,
        status: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }
  
  // Mock menu data for caterer
  if (vendorType === 'caterer') {
    return [
      {
        id: '301',
        vendor_id: vendorId,
        menu_name: 'Wedding Package',
        image_url: '/images/menu/wedding-package.jpg',
        veg: true,
        veg_price_per_person: 800,
        non_veg: true,
        non_veg_price_per_person: 1000,
        status: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '302',
        vendor_id: vendorId,
        menu_name: 'Corporate Event Package',
        image_url: '/images/menu/corporate-package.jpg',
        veg: true,
        veg_price_per_person: 600,
        non_veg: true,
        non_veg_price_per_person: 750,
        status: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];
  }
  
  // Default empty array if vendor type doesn't match
  return [];
};

/**
 * Fetch vendor menu data based on the current vendor type
 * @param vendorId - ID of the vendor
 * @returns Promise with menu data
 */
export const fetchVendorMenu = async (vendorId: string): Promise<any[]> => {
  console.log('Fetching menu for vendor ID:', vendorId);
  try {
    // Get the current vendor type from localStorage
    const vendorType = localStorage.getItem('vendor_type') || 'home-chef';
    const tableName = getVendorTableName(vendorType);
    
    console.log(`Fetching menu for vendor ${vendorId} with type: ${vendorType}`);
    console.log(`Using table: ${tableName}`);
    
    // First, fetch the menu IDs and pricing from vendor_other_details table
    const { data: otherDetailsData, error: otherDetailsError } = await supabase
      .from('vendor_other_details')
      .select('veg_menu, non_veg_menu, veg_price_per_person, non_veg_price_per_person')
      .eq('vendor_id', vendorId)
      .single();
    
    if (otherDetailsError) {
      console.error('Error fetching vendor_other_details:', otherDetailsError);
      throw otherDetailsError;
    }
    
    console.log('Vendor other details data:', otherDetailsData);
    
    // Extract menu IDs and pricing from the response
    const vegMenuIds = otherDetailsData?.veg_menu || [];
    const nonVegMenuIds = otherDetailsData?.non_veg_menu || [];
    const vegPricePerPerson = otherDetailsData?.veg_price_per_person || null;
    const nonVegPricePerPerson = otherDetailsData?.non_veg_price_per_person || null;
    
    console.log('Veg price per person:', vegPricePerPerson);
    console.log('Non-veg price per person:', nonVegPricePerPerson);
    
    console.log('Veg menu IDs:', vegMenuIds);
    console.log('Non-veg menu IDs:', nonVegMenuIds);
    
    // Combine all menu IDs
    const allMenuIds = [...vegMenuIds, ...nonVegMenuIds];
    
    if (allMenuIds.length === 0) {
      console.log('No menu IDs found, returning mock menu data for development');
      return getMockMenuData(vendorId, vendorType);
    }
    
    // Query the appropriate table based on vendor type and menu IDs
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .in('id', allMenuIds)
      .eq('status', true);
    
    if (error) {
      console.error('Supabase menu query error:', error);
      throw error;
    }
    
    console.log('Supabase menu response data:', data);
    
    // If no data is found, return mock data for development
    if (!data || data.length === 0) {
      console.log('No menu data found, returning mock menu data for development');
      return getMockMenuData(vendorId, vendorType);
    }
    
    // Process the menu data based on vendor type
    const processedMenuData = processMenuData(data, vendorType, { 
      vegMenuIds, 
      nonVegMenuIds,
      vegPricePerPerson,
      nonVegPricePerPerson
    });
    
    return processedMenuData;
  } catch (error) {
    console.error('Error fetching vendor menu:', error);
    return [];
  }
};

/**
 * Fetch vendor services by vendor ID
 * @param vendorId - ID of the vendor
 * @returns Promise with array of service strings
 */
export const fetchVendorServices = async (vendorId: string): Promise<string[]> => {
  try {
    console.log('Fetching services for vendor ID:', vendorId);
    
    // Get the vendor type
    const { data: vendorData, error: vendorError } = await supabase
      .from('vendors')
      .select('vendor_type')
      .eq('id', vendorId)
      .single();
    
    if (vendorError || !vendorData) {
      console.error('Error fetching vendor type:', vendorError);
      return getDefaultServices(vendorData?.vendor_type || 'home-chef');
    }
    
    const vendorType = vendorData.vendor_type;
    
    // Fetch vendor_other_details to get service_types
    const { data: otherDetailsData, error: otherDetailsError } = await supabase
      .from('vendor_other_details')
      .select('service_types')
      .eq('vendor_id', vendorId)
      .single();
    
    if (otherDetailsError) {
      console.error('Error fetching vendor_other_details:', otherDetailsError);
      return getDefaultServices(vendorType);
    }
    
    // Process service_types field
    if (otherDetailsData && otherDetailsData.service_types) {
      try {
        // Handle both array and string formats
        let serviceIds: string[] = [];
        
        if (Array.isArray(otherDetailsData.service_types)) {
          serviceIds = otherDetailsData.service_types;
        } else if (typeof otherDetailsData.service_types === 'string') {
          // Try to parse as JSON
          try {
            const parsed = JSON.parse(otherDetailsData.service_types);
            if (Array.isArray(parsed)) {
              serviceIds = parsed;
            } else {
              console.warn(`Service types for vendor ${vendorId} is not an array after parsing:`, parsed);
            }
          } catch (parseError) {
            // If it's not valid JSON, check if it's a comma-separated string
            if (otherDetailsData.service_types.includes(',')) {
              serviceIds = otherDetailsData.service_types.split(',').map(id => id.trim());
            } else {
              // Treat as a single ID
              serviceIds = [otherDetailsData.service_types];
            }
          }
        }
        
        if (serviceIds.length > 0) {
          console.log('Service IDs from service_types field:', serviceIds);
          
          // Fetch service data from the services table
          const { data: servicesData, error: servicesError } = await supabase
            .from('services')
            .select('*')
            .in('id', serviceIds);
          
          if (!servicesError && servicesData && servicesData.length > 0) {
            const serviceNames = servicesData.map(service => service.name);
            console.log('Fetched service names:', serviceNames);
            return serviceNames;
          }
        }
      } catch (error) {
        console.error('Error processing service_types field:', error);
      }
    }
    
    // If all else fails, return default services
    console.log('No services found, returning defaults for vendor type:', vendorType);
    return getDefaultServices(vendorType);
  } catch (error) {
    console.error('Error fetching vendor services:', error);
    return getDefaultServices(vendorId);
  }
};

/**
 * Get default services based on vendor type
 * @param vendorId - ID of the vendor
 * @returns Array of default service strings
 */
const getDefaultServices = (vendorId: string): string[] => {
  // Get vendor type from localStorage
  const vendorType = localStorage.getItem('vendor_type') || 'home-chef';
  
  // Default services based on vendor type
  if (vendorType === 'home-chef') {
    return [
      "On Demand Customized Food",
      "Food for Senior Citizens",
      "Food for Patients",
      "Diet Food",
      "Protein Food",
      "Keto Food",
      "Jain Food",
      "Fasting Food"
    ];
  } else if (vendorType === 'tiffin_supplier') {
    return [
      "Daily Tiffin Service",
      "Monthly Subscription",
      "Customized Diet Plans",
      "Special Meal Packages",
      "Weekend Tiffin Service",
      "Corporate Tiffin Service",
      "Student Meal Plans",
      "Senior Citizen Meals"
    ];
  } else if (vendorType === 'caterer') {
    return [
      "Wedding Catering",
      "Corporate Events",
      "Birthday Parties",
      "Anniversary Celebrations",
      "Customized Menu Planning",
      "Buffet Service",
      "Live Cooking Stations",
      "Theme-based Catering"
    ];
  }
  
  // Default fallback services
  return [
    "Customized Food Service",
    "Special Dietary Options",
    "Event Catering",
    "Meal Planning",
    "Food Delivery",
    "Bulk Orders",
    "Special Occasion Meals",
    "Customized Menus"
  ];
};

/**
 * Fetch a single vendor by ID
 * @param vendorId - ID of the vendor
 * @returns Promise with vendor data
 */
// Mock vendor data for fallback when real data is not available
const mockVendor: BaseVendor = {
  id: '1',
  business_name: "Abha's Kitchen",
  phone_number: "+************",
  address: "A412 Bhadar Girnar Height 1, Joshipura, Junagadh-362001 (Opposite R S Kalaria School Bahauddin College Road)",
  city: "Junagadh",
  state: "Gujarat",
  profile_image: "/lovable-uploads/9404f837-a745-4b95-bdf9-e61948617947.png",
  vendor_type: "home-chef",
  operational_status: "active",
  profile_status: "approved",
  cuisine_type: "North Indian | Fast Food",
  rating: 4.9,
  distance: 2,
  isVeg: true,
  timings: "Lunch - Dinner",
  delivery_radius: 5,
  start_time: "09:00:00",
  end_time: "22:30:00",
  whatsapp: "************",
  coordinates: {
    latitude: 28.6139,
    longitude: 77.209
  }
};

/**
 * Process menu data based on vendor type
 * @param menuData - Raw menu data from the database
 * @param vendorType - The vendor type
 * @param options - Object containing vegMenuIds, nonVegMenuIds, and pricing information
 * @returns Processed menu data with pricing information
 */
interface ProcessMenuOptions {
  vegMenuIds: string[];
  nonVegMenuIds: string[];
  vegPricePerPerson?: number | null;
  nonVegPricePerPerson?: number | null;
}

const processMenuData = (menuData: any[], vendorType: string, options: ProcessMenuOptions): any[] => {
  const { vegMenuIds, nonVegMenuIds, vegPricePerPerson, nonVegPricePerPerson } = options;
  
  // Add pricing information to the first item for the component to access
  const pricingInfo = {
    id: 'pricing_info',
    veg_price_per_person: vegPricePerPerson,
    non_veg_price_per_person: nonVegPricePerPerson,
    isPricingInfo: true
  };

  // Add the pricing info as the first item in the array
  const menuDataWithPricing = [pricingInfo, ...menuData];
  
  // Process menu data based on vendor type
  if (vendorType === 'home-chef' || vendorType === 'caterer') {
    // For home chef and caterer menu items, set veg and non_veg flags based on menu IDs
    return menuDataWithPricing.map(item => ({
      ...item,
      // Ensure veg flag is set to true for items in vegMenuIds
      veg: vegMenuIds.includes(item.id),
      // Ensure non_veg flag is set to true for items in nonVegMenuIds
      non_veg: nonVegMenuIds.includes(item.id),
      // Keep these flags for backward compatibility
      isVeg: vegMenuIds.includes(item.id),
      isNonVeg: nonVegMenuIds.includes(item.id)
    }));
  } else if (vendorType === 'tiffin_supplier') {
    // For tiffin service menu items, set veg_enabled and non_veg_enabled flags based on menu IDs
    return menuDataWithPricing.map(item => ({
      ...item,
      // Ensure veg_enabled flag is set to true for items in vegMenuIds
      veg_enabled: vegMenuIds.includes(item.id),
      // Ensure non_veg_enabled flag is set to true for items in nonVegMenuIds
      non_veg_enabled: nonVegMenuIds.includes(item.id),
      // Keep these flags for backward compatibility
      isVeg: vegMenuIds.includes(item.id),
      isNonVeg: nonVegMenuIds.includes(item.id)
    }));
  } else {
    // Default case - just add isVeg and isNonVeg flags
    return menuDataWithPricing.map(item => ({
      ...item,
      isVeg: vegMenuIds.includes(item.id),
      isNonVeg: nonVegMenuIds.includes(item.id)
    }));
  }
};

/**
 * Fetch vendor by ID with all details including services
 * @param vendorId - ID of the vendor to fetch
 * @returns Promise with vendor data or null if not found
 */
/**
 * Fetches cuisine names from the cuisines table based on cuisine IDs
 * @param cuisineIds Array of cuisine IDs to fetch
 * @returns Array of cuisine names
 */
export const fetchCuisineNames = async (cuisineIds: string[]): Promise<string[]> => {
  if (!cuisineIds || !Array.isArray(cuisineIds) || cuisineIds.length === 0) {
    console.log('No cuisine IDs provided');
    return [];
  }

  try {
    console.log('Fetching cuisine names for IDs:', cuisineIds);
    const { data, error } = await supabase
      .from('cuisines')
      .select('name')
      .in('id', cuisineIds);

    if (error) {
      console.error('Error fetching cuisine names:', error);
      return [];
    }

    if (!data || data.length === 0) {
      console.log('No cuisines found for the provided IDs');
      return [];
    }

    // Extract cuisine names from the data
    const cuisineNames = data.map(cuisine => cuisine.name);
    console.log('Fetched cuisine names:', cuisineNames);
    return cuisineNames;
  } catch (error) {
    console.error('Error in fetchCuisineNames:', error);
    return [];
  }
};

export const fetchVendorById = async (vendorId: string): Promise<BaseVendor | null> => {
  console.log(`fetchVendorById called with ID: ${vendorId}`);
  try {
    console.log('Fetching vendor with ID:', vendorId);
    
    // Query the vendors table with a join to vendor_other_details
    const { data, error } = await supabase
      .from('vendors')
      .select(`
        *,
        vendor_other_details!vendor_id(id, delivery_radius, start_time, end_time, start_day, end_day, service_types, veg_menu, non_veg_menu, cuisines)
      `)
      .eq('id', vendorId)
      .single();
    
    if (error) {
      console.error('Supabase query error:', error);
      throw error;
    }
    
    if (!data) {
      console.log('No vendor found with ID:', vendorId);
      return mockVendor; // Return mock data if no vendor found
    }
    
    console.log('Raw vendor data:', JSON.stringify(data, null, 2));
    
    // Process the data similar to fetchVendors function
    let otherDetails;
    
    if (data.vendor_other_details) {
      // Handle both array and object formats
      if (Array.isArray(data.vendor_other_details)) {
        otherDetails = data.vendor_other_details[0] || {};
      } else {
        otherDetails = data.vendor_other_details;
      }
    } else {
      otherDetails = {};
    }
    
    // Extract delivery radius if available
    let deliveryRadius = null;
    if (otherDetails && otherDetails.delivery_radius !== undefined && otherDetails.delivery_radius !== null) {
      // Handle both string and number formats
      deliveryRadius = typeof otherDetails.delivery_radius === 'string' 
        ? parseFloat(otherDetails.delivery_radius) 
        : otherDetails.delivery_radius;
    }
    
    // Extract cuisine information if available
    let cuisineIds: string[] = [];
    let cuisineNames: string[] = [];
    
    if (otherDetails && otherDetails.cuisines) {
      // Handle both array and string formats
      if (Array.isArray(otherDetails.cuisines)) {
        cuisineIds = otherDetails.cuisines;
      } else if (typeof otherDetails.cuisines === 'string') {
        try {
          const parsedCuisines = JSON.parse(otherDetails.cuisines);
          if (Array.isArray(parsedCuisines)) {
            cuisineIds = parsedCuisines;
          }
        } catch (e) {
          console.error('Error parsing cuisines string:', e);
        }
      }
      
      // Fetch cuisine names if we have cuisine IDs
      if (cuisineIds.length > 0) {
        console.log('Fetching cuisine names for vendor:', data.business_name);
        cuisineNames = await fetchCuisineNames(cuisineIds);
      }
    }
    
    // Extract timing information if available
    let startTime = null;
    let endTime = null;
    let startDay = null;
    let endDay = null;
    if (otherDetails) {
      if (otherDetails.start_time) {
        startTime = otherDetails.start_time;
      }
      if (otherDetails.end_time) {
        endTime = otherDetails.end_time;
      }
      if (otherDetails.start_day) {
        startDay = otherDetails.start_day;
      }
      if (otherDetails.end_day) {
        endDay = otherDetails.end_day;
      }
    }
    
    // We'll fetch services separately using fetchVendorServices
    // Just store the service_types IDs for now
    let serviceTypes: string[] = [];
    if (otherDetails && otherDetails.service_types) {
      if (Array.isArray(otherDetails.service_types)) {
        serviceTypes = otherDetails.service_types;
      }
    }
    
    // Extract veg and non-veg menu items
    let vegMenuItems: string[] = [];
    let nonVegMenuItems: string[] = [];
    let isVeg = true;
    let isMixed = false;
    
    console.log(`Processing menu items for vendor: ${data.business_name} (ID: ${vendorId})`);
    console.log(`Vendor type: ${data.vendor_type}`);
    console.log(`Other details:`, otherDetails);
    
    // Special case for Meera's Homestyle Cooking - add default menu items if needed
    if (data.business_name && data.business_name.includes("Meera's Homestyle")) {
      console.log("Special case: Processing Meera's Homestyle Cooking menu items");
      
      // Only add default items if no menu items are found in the database
      if ((!otherDetails.veg_menu || otherDetails.veg_menu.length === 0) && 
          (!otherDetails.non_veg_menu || otherDetails.non_veg_menu.length === 0)) {
        // Make it consistent with listing page - only show as Non-Veg
        vegMenuItems = [];
        nonVegMenuItems = ['Butter Chicken', 'Mutton Curry', 'Fish Curry'];
        isVeg = false;
        isMixed = false; // Set to false to show only as Non-Veg
        console.log("Added default non-veg menu items for Meera's Homestyle Cooking");
      }
    }
    
    // Process veg menu items from database if they exist and we haven't already set defaults
    if (otherDetails && otherDetails.veg_menu && Array.isArray(otherDetails.veg_menu) && otherDetails.veg_menu.length > 0 && vegMenuItems.length === 0) {
      // Fetch menu item names based on vendor type
      try {
        const vendorType = data.vendor_type || '';
        const vegMenuIds = otherDetails.veg_menu;
        
        console.log(`Fetching veg menu items for vendor ${data.id} (${data.business_name}) with type: ${vendorType}`);
        console.log('Veg menu IDs:', vegMenuIds);
        
        if (vegMenuIds.length > 0) {
          let table = '';
          
          // Normalize vendor type to match table names based on PRD specifications
          if (vendorType.includes('home') || vendorType.includes('chef')) {
            table = 'home_chef';
          } else if (vendorType.includes('tiffin') || vendorType.includes('service')) {
            table = 'tiffin_service_menu';
          } else if (vendorType.includes('cater')) {
            table = 'caterer_menu';
          }
          
          // If no table determined, use a default based on vendor type pattern
          if (!table) {
            if (vendorType.toLowerCase().includes('home')) {
              table = 'home_chef';
            } else if (vendorType.toLowerCase().includes('tiffin')) {
              table = 'tiffin_service_menu';
            } else if (vendorType.toLowerCase().includes('cater')) {
              table = 'caterer_menu';
            } else {
              // Default to home_chef if we can't determine the type
              table = 'home_chef';
              console.log(`Could not determine table for vendor type: ${vendorType}, defaulting to home_chef`);
            }
          }
          
          console.log(`Using table '${table}' for veg menu items`);
          
          if (table) {
            // Query the appropriate table based on vendor type
            let query = supabase
              .from(table)
              .select('*')
              .in('id', vegMenuIds);
              
            // Execute the query
            const { data: menuData, error: menuError } = await query;
            
            console.log(`Queried ${table} table for veg menu items:`, { menuData, menuError });
            
            if (menuError) {
              console.error(`Error fetching veg menu data from ${table}:`, menuError);
            }
            
            if (!menuError && menuData && menuData.length > 0) {
              console.log(`Found ${menuData.length} veg menu items for ${data.id} (${data.business_name}):`, menuData);
              // Extract menu item names based on the table structure
              vegMenuItems = menuData.map(item => {
                // Different tables have different field names
                if (table === 'home_chef' || table === 'caterer_menu') {
                  return item.menu_name || 'Menu Item';
                } else if (table === 'tiffin_service_menu') {
                  return item.meal_type || 'Menu Item';
                } else {
                  // Fallback to any field that might contain the name
                  return item.menu_name || item.meal_type || item.name || 'Menu Item';
                }
              });
            } else {
              console.log(`No veg menu data found in ${table} for IDs:`, vegMenuIds);
            }
          }
        }
      } catch (menuError) {
        console.error('Error fetching veg menu items:', menuError);
      }
    }
    
    // Process non-veg menu items from database if they exist and we haven't already set defaults
    if (otherDetails && otherDetails.non_veg_menu && Array.isArray(otherDetails.non_veg_menu) && otherDetails.non_veg_menu.length > 0 && nonVegMenuItems.length === 0) {
      isVeg = false;
      
      // Fetch menu item names based on vendor type
      try {
        const vendorType = data.vendor_type || '';
        const nonVegMenuIds = otherDetails.non_veg_menu;
        
        console.log(`Fetching non-veg menu items for vendor ${data.id} (${data.business_name}) with type: ${vendorType}`);
        console.log('Non-veg menu IDs:', nonVegMenuIds);
        
        if (nonVegMenuIds.length > 0) {
          let table = '';
          
          // Normalize vendor type to match table names based on PRD specifications
          if (vendorType.includes('home') || vendorType.includes('chef')) {
            table = 'home_chef';
          } else if (vendorType.includes('tiffin') || vendorType.includes('service')) {
            table = 'tiffin_service_menu';
          } else if (vendorType.includes('cater')) {
            table = 'caterer_menu';
          }
          
          // If no table determined, use a default based on vendor type pattern
          if (!table) {
            if (vendorType.toLowerCase().includes('home')) {
              table = 'home_chef';
            } else if (vendorType.toLowerCase().includes('tiffin')) {
              table = 'tiffin_service_menu';
            } else if (vendorType.toLowerCase().includes('cater')) {
              table = 'caterer_menu';
            } else {
              // Default to home_chef if we can't determine the type
              table = 'home_chef';
              console.log(`Could not determine table for vendor type: ${vendorType}, defaulting to home_chef`);
            }
          }
          
          console.log(`Using table '${table}' for non-veg menu items`);
          
          if (table) {
            // Query the appropriate table based on vendor type
            let query = supabase
              .from(table)
              .select('*')
              .in('id', nonVegMenuIds);
              
            // Execute the query
            const { data: menuData, error: menuError } = await query;
            
            console.log(`Queried ${table} table for non-veg menu items:`, { menuData, menuError });
            
            if (menuError) {
              console.error(`Error fetching non-veg menu data from ${table}:`, menuError);
            }
            
            if (!menuError && menuData && menuData.length > 0) {
              console.log(`Found ${menuData.length} non-veg menu items for ${data.id} (${data.business_name}):`, menuData);
              // Extract menu item names based on the table structure
              nonVegMenuItems = menuData.map(item => {
                // Different tables have different field names
                if (table === 'home_chef' || table === 'caterer_menu') {
                  return item.menu_name || 'Menu Item';
                } else if (table === 'tiffin_service_menu') {
                  return item.meal_type || 'Menu Item';
                } else {
                  // Fallback to any field that might contain the name
                  return item.menu_name || item.meal_type || item.name || 'Menu Item';
                }
              });
            } else {
              console.log(`No non-veg menu data found in ${table} for IDs:`, nonVegMenuIds);
            }
          }
        }
      } catch (menuError) {
        console.error('Error fetching non-veg menu items:', menuError);
      }
    }
    
    // Determine if vendor is mixed (has both veg and non-veg options)
    if (vegMenuItems.length > 0 && nonVegMenuItems.length > 0) {
      isMixed = true;
    }
    
    // Log the menu items we found for this vendor
    console.log(`Menu items for vendor ${data.id} (${data.business_name}):`, {
      vegMenuItems,
      nonVegMenuItems,
      isVeg,
      isMixed
    });
    
    // Ensure we have proper menu items arrays
    if (!vegMenuItems || !Array.isArray(vegMenuItems)) {
      console.log(`Initializing empty vegMenuItems array for vendor ${data.id}`);
      vegMenuItems = [];
    }
    
    if (!nonVegMenuItems || !Array.isArray(nonVegMenuItems)) {
      console.log(`Initializing empty nonVegMenuItems array for vendor ${data.id}`);
      nonVegMenuItems = [];
    }
    
    // If we still don't have any menu items, check if we can extract them from menuItems property
    if (vegMenuItems.length === 0 && nonVegMenuItems.length === 0 && data.menuItems) {
      console.log(`Trying to extract menu items from menuItems property for vendor ${data.id}`);
      try {
        // If menuItems is a string, try to parse it as JSON
        if (typeof data.menuItems === 'string') {
          const parsedItems = JSON.parse(data.menuItems);
          if (Array.isArray(parsedItems)) {
            // If we can't determine veg/non-veg, just add all items to vegMenuItems
            vegMenuItems = parsedItems.filter(item => typeof item === 'string');
            console.log(`Extracted ${vegMenuItems.length} items from menuItems string`);
          }
        } 
        // If menuItems is already an array, use it directly
        else if (Array.isArray(data.menuItems)) {
          vegMenuItems = data.menuItems.filter(item => typeof item === 'string');
          console.log(`Extracted ${vegMenuItems.length} items from menuItems array`);
        }
      } catch (e) {
        console.error(`Error parsing menuItems for vendor ${data.id}:`, e);
      }
    }
    
    // If we still don't have menu items and this is a veg vendor, add some default items
    if (vegMenuItems.length === 0 && nonVegMenuItems.length === 0) {
      console.log(`No menu items found for vendor ${data.id}, adding generic placeholders`);
      if (isVeg) {
        vegMenuItems = ['Various Veg Items'];
      } else if (isMixed) {
        vegMenuItems = ['Various Veg Items'];
        nonVegMenuItems = ['Various Non-Veg Items'];
      } else {
        nonVegMenuItems = ['Various Non-Veg Items'];
      }
    }
    
    // Create the processed vendor object
    const processedVendor = {
      ...data,
      // Include delivery_radius and timing information in the vendor object
      delivery_radius: deliveryRadius,
      start_time: startTime,
      end_time: endTime,
      start_day: startDay,
      end_day: endDay,
      // Add menu items and veg/non-veg status
      isVeg,
      isMixed,
      vegMenuItems,
      nonVegMenuItems,
      service_types: serviceTypes, // Store service_types IDs for later use
      // Add cuisine information
      cuisineIds,
      cuisineNames,
      cuisine_type: cuisineNames.length > 0 ? cuisineNames.join(', ') : data.cuisine_type || 'Various Cuisines',
      // Remove the nested vendor_other_details to keep the structure flat
      vendor_other_details: undefined
    };
    
    console.log('Final processed vendor with menu items:', {
      id: processedVendor.id,
      name: processedVendor.business_name,
      vegMenuItems: processedVendor.vegMenuItems,
      nonVegMenuItems: processedVendor.nonVegMenuItems,
      isVeg: processedVendor.isVeg,
      isMixed: processedVendor.isMixed
    });
    
    console.log('Processed vendor details:', processedVendor);
    
    return processedVendor;
  } catch (error) {
    console.error('Error fetching vendor by ID:', error);
    return mockVendor; // Return mock data in case of error
  }
};

/**
 * Fetch vendor preferences based on vendor type
 * @param vendorId - ID of the vendor
 * @returns Promise with array of preference items
 */
export const fetchVendorPreferences = async (vendorId: string): Promise<{ name: string; isActive?: boolean }[]> => {
  try {
    console.log('Fetching preferences for vendor ID:', vendorId);
    
    // First, get the vendor data with vendor_other_details to get both vendor type and selected preferences
    const { data: vendorData, error: vendorError } = await supabase
      .from('vendors')
      .select(`
        vendor_type,
        vendor_other_details!vendor_id(id, preferences)
      `)
      .eq('id', vendorId)
      .single();
    
    if (vendorError) {
      console.error('Error fetching vendor data:', vendorError);
      throw vendorError;
    }
    
    if (!vendorData || !vendorData.vendor_type) {
      console.log('No vendor type found for vendor ID:', vendorId);
      return [];
    }
    
    const vendorType = vendorData.vendor_type;
    console.log('Vendor type:', vendorType);
    
    // Extract vendor's selected preferences from vendor_other_details
    let selectedPreferenceIds: string[] = [];
    
    if (vendorData.vendor_other_details) {
      // Handle both array and object formats
      const otherDetails = Array.isArray(vendorData.vendor_other_details) 
        ? vendorData.vendor_other_details[0] 
        : vendorData.vendor_other_details;
      
      if (otherDetails && otherDetails.preferences) {
        selectedPreferenceIds = Array.isArray(otherDetails.preferences) 
          ? otherDetails.preferences 
          : [otherDetails.preferences];
        
        console.log('Selected preference IDs:', selectedPreferenceIds);
      }
    }
    
    // If no preferences are selected, return empty array
    if (selectedPreferenceIds.length === 0) {
      console.log('No preferences selected for this vendor');
      return [];
    }
    
    // Only fetch the preferences that the vendor has selected
    const { data: preferencesData, error: preferencesError } = await supabase
      .from('preferences')
      .select('id, name')
      .in('id', selectedPreferenceIds)
      .eq('is_active', true);
    
    if (preferencesError) {
      console.error('Error fetching preferences:', preferencesError);
      throw preferencesError;
    }
    
    if (!preferencesData || preferencesData.length === 0) {
      console.log('No matching preferences found for the selected IDs');
      return [];
    }
    
    // Transform the data to match the expected format
    // All returned preferences are active since we're only fetching selected ones
    const preferences = preferencesData.map(pref => ({
      id: pref.id,
      name: pref.name,
      isActive: true // All preferences are active since we only fetched selected ones
    }));
    
    console.log('Fetched selected preferences:', preferences);
    return preferences;
  } catch (error) {
    console.error('Error in fetchVendorPreferences:', error);
    return [];
  }
};

/**
 * Fetch vendor payment methods based on vendor type
 * @param vendorId - ID of the vendor
 * @returns Promise with array of payment method items
 */
export const fetchVendorPaymentMethods = async (vendorId: string): Promise<{ id: string; method_name: string; description: string | null; image_icon: string | null; selected: boolean }[]> => {
  try {
    if (!vendorId) {
      throw new Error('Invalid vendor ID provided');
    }
    
    console.log('Fetching payment methods for vendor ID:', vendorId);
    
    // First, get the vendor data with vendor_other_details to get vendor type and payment methods
    const { data: vendorData, error: vendorError } = await supabase
      .from('vendors')
      .select(`
        id,
        vendor_type,
        vendor_other_details!vendor_id(id, payment_methods)
      `)
      .eq('id', vendorId)
      .single();
    
    if (vendorError) {
      console.error('Error fetching vendor data:', vendorError);
      if (vendorError.code === 'PGRST116') {
        throw new Error(`Vendor with ID ${vendorId} not found`);
      }
      throw new Error(`Error fetching vendor data: ${vendorError.message}`);
    }
    
    if (!vendorData) {
      throw new Error(`No data found for vendor ID: ${vendorId}`);
    }
    
    console.log('Vendor data retrieved:', vendorData);
    
    // Get selected payment method IDs from vendor_other_details
    let selectedPaymentMethodIds: string[] = [];
    
    if (vendorData.vendor_other_details) {
      // Handle both array and object formats
      const otherDetails = Array.isArray(vendorData.vendor_other_details) 
        ? vendorData.vendor_other_details[0] 
        : vendorData.vendor_other_details;
      
      console.log('Vendor other details:', JSON.stringify(otherDetails, null, 2));
      
      if (otherDetails && otherDetails.payment_methods) {
        try {
          // Log the raw payment_methods data for debugging
          console.log('Raw payment_methods data:', otherDetails.payment_methods);
          console.log('Type of payment_methods:', typeof otherDetails.payment_methods);
          
          // Handle string JSON format if it's stored that way
          if (typeof otherDetails.payment_methods === 'string') {
            if (otherDetails.payment_methods.startsWith('[')) {
              selectedPaymentMethodIds = JSON.parse(otherDetails.payment_methods);
              console.log('Parsed payment_methods from JSON string:', selectedPaymentMethodIds);
            } else {
              // Single ID as string
              selectedPaymentMethodIds = [otherDetails.payment_methods];
              console.log('Using single payment_method as string:', selectedPaymentMethodIds);
            }
          } else if (Array.isArray(otherDetails.payment_methods)) {
            // Array of IDs
            selectedPaymentMethodIds = otherDetails.payment_methods;
            console.log('Using payment_methods as array:', selectedPaymentMethodIds);
          } else if (typeof otherDetails.payment_methods === 'object' && otherDetails.payment_methods !== null) {
            // Object with IDs
            selectedPaymentMethodIds = Object.values(otherDetails.payment_methods);
            console.log('Extracted payment_methods from object:', selectedPaymentMethodIds);
          } else {
            // Single ID
            selectedPaymentMethodIds = [otherDetails.payment_methods];
            console.log('Using payment_method as single value:', selectedPaymentMethodIds);
          }
          
          // Filter out any non-string values and nulls
          selectedPaymentMethodIds = selectedPaymentMethodIds
            .filter(id => id !== null && id !== undefined)
            .map(id => String(id)); // Convert all IDs to strings for consistent comparison
          
          console.log('Final selected payment method IDs after filtering:', selectedPaymentMethodIds);
        } catch (parseError) {
          console.error('Error parsing payment methods:', parseError);
          // Continue with empty array
          selectedPaymentMethodIds = [];
        }
      } else {
        console.log('No payment_methods found in vendor_other_details');
      }
    } else {
      console.log('No vendor_other_details found for this vendor');
    }
    
    // Fetch ALL active payment methods from payment_methods table
    const { data: paymentMethodsData, error: paymentMethodsError } = await supabase
      .from('payment_methods')
      .select('id, method_name, description, image_icon')
      .eq('status', true);
    
    if (paymentMethodsError) {
      console.error('Error fetching payment methods:', paymentMethodsError);
      throw new Error(`Error fetching payment methods: ${paymentMethodsError.message}`);
    }
    
    console.log('Payment methods from database:', paymentMethodsData);
    
    if (!paymentMethodsData || paymentMethodsData.length === 0) {
      console.log('No payment methods found in database');
      // Return default payment methods instead of empty array
      return [
        {
          id: "cash",
          method_name: "Cash",
          description: "Pay cash or ask for QR code",
          image_icon: null,
          selected: true
        },
        {
          id: "card",
          method_name: "Card",
          description: "PhonePe, Amazon Pay & more",
          image_icon: null,
          selected: true
        },
        {
          id: "upi",
          method_name: "UPI",
          description: "Select from a list of banks",
          image_icon: null,
          selected: false
        }
      ];
    }
    
    // Add a selected property to each payment method based on whether its ID is in selectedPaymentMethodIds
    const paymentMethodsWithSelection = paymentMethodsData.map(method => {
      // Convert method.id to string for consistent comparison
      const methodId = String(method.id);
      // Check if this method's ID is in the selected IDs array
      const isSelected = selectedPaymentMethodIds.some(id => String(id) === methodId);
      
      console.log(`Payment method ${method.method_name} (ID: ${methodId}): ${isSelected ? 'SELECTED' : 'not selected'}`);
      
      return {
        ...method,
        selected: isSelected
      };
    });
    
    // Log the final payment methods with their selection status
    console.log('Payment methods with selection status:');
    paymentMethodsWithSelection.forEach(method => {
      console.log(`- ${method.method_name} (ID: ${method.id}): ${method.selected ? 'SELECTED' : 'not selected'}`);
    });
    
    // Only ensure at least one payment method is selected if we're using default data
    // Otherwise, respect the database selections exactly as they are
    if (!vendorData.vendor_other_details && !paymentMethodsWithSelection.some(method => method.selected) && paymentMethodsWithSelection.length > 0) {
      console.log('No payment methods were selected and no vendor_other_details found, selecting the first one by default');
      // Select the first payment method by default only if we don't have vendor_other_details
      paymentMethodsWithSelection[0].selected = true;
    }
    
    console.log('Final payment methods with selection:', paymentMethodsWithSelection);
    return paymentMethodsWithSelection;
  } catch (error: any) {
    console.error('Error in fetchVendorPaymentMethods:', error);
    // Re-throw the error with a more descriptive message for better debugging
    throw new Error(`Failed to fetch payment methods: ${error.message || 'Unknown error'}`);
  }
};

/**
 * Update vendor payment methods in the database
 * @param vendorId - ID of the vendor
 * @param selectedPaymentMethodIds - Array of selected payment method IDs
 * @returns Promise with success status
 */
export const updateVendorPaymentMethods = async (vendorId: string, selectedPaymentMethodIds: string[]): Promise<boolean> => {
  try {
    if (!vendorId) {
      throw new Error('Invalid vendor ID provided');
    }
    
    console.log('Updating payment methods for vendor ID:', vendorId);
    console.log('Selected payment method IDs:', selectedPaymentMethodIds);
    
    // First, check if vendor_other_details record exists for this vendor
    const { data: existingDetails, error: checkError } = await supabase
      .from('vendor_other_details')
      .select('id')
      .eq('vendor_id', vendorId)
      .single();
    
    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 means not found
      console.error('Error checking vendor_other_details:', checkError);
      throw new Error(`Error checking vendor details: ${checkError.message}`);
    }
    
    let result;
    
    if (existingDetails) {
      // Update existing record
      const { data, error } = await supabase
        .from('vendor_other_details')
        .update({ payment_methods: selectedPaymentMethodIds })
        .eq('vendor_id', vendorId);
      
      result = { data, error };
    } else {
      // Create new record
      const { data, error } = await supabase
        .from('vendor_other_details')
        .insert([{ 
          vendor_id: vendorId,
          payment_methods: selectedPaymentMethodIds 
        }]);
      
      result = { data, error };
    }
    
    if (result.error) {
      console.error('Error updating payment methods:', result.error);
      throw new Error(`Error updating payment methods: ${result.error.message}`);
    }
    
    console.log('Payment methods updated successfully');
    return true;
  } catch (error: any) {
    console.error('Error in updateVendorPaymentMethods:', error);
    throw new Error(`Failed to update payment methods: ${error.message || 'Unknown error'}`);
  }
};

/**
 * Fetch vendors based on user coordinates from localStorage
 * This function specifically uses the 'userCoordinates' key from localStorage
 * to get user's latitude and longitude, then returns vendors with distance information
 */
export const fetchVendorsByUserCoordinates = async (): Promise<BaseVendor[]> => {
  try {
    // Get user coordinates from localStorage using the 'userCoordinates' key
    let userCoordinates = null;
    try {
      const userCoordinatesStr = localStorage.getItem('userCoordinates');
      if (userCoordinatesStr) {
        userCoordinates = JSON.parse(userCoordinatesStr);
        console.log('User coordinates from localStorage:', userCoordinates);
      }
    } catch (error) {
      console.error('Error parsing userCoordinates from localStorage:', error);
    }

    if (!userCoordinates || !userCoordinates.lat || !userCoordinates.lng) {
      console.error('User coordinates not found in localStorage under key "userCoordinates"');
      return [];
    }

    // Get the current vendor type from localStorage
    const vendorType = localStorage.getItem('vendor_type') || 'home-chef';
    console.log('Fetching vendors with type:', vendorType, 'and user coordinates:', userCoordinates);

    // Start building the Supabase query
    let query;
    
    if (vendorType === 'caters' || vendorType === 'caterer' || vendorType === 'catering') {
      // For caterer type, fetch both 'caterer' and 'catering' vendors
      query = supabase
        .from('vendors')
        .select(`
          id, business_name, phone_number, address, city, state, profile_image, vendor_type, operational_status, profile_status, lattitute, longitute,
          vendor_other_details!vendor_id(id, delivery_radius, start_time, end_time, cuisines, service_types)
        `)
        .in('vendor_type', ['caterer', 'catering', 'caters'])
        .eq('is_active', true);
    } else if (vendorType === 'tiffin' || vendorType === 'tiffin_supplier' || vendorType === 'tiffin_service') {
      // For tiffin type, fetch 'tiffin_supplier' and 'tiffin_service' vendors
      query = supabase
        .from('vendors')
        .select(`
          id, business_name, phone_number, address, city, state, profile_image, vendor_type, operational_status, profile_status, lattitute, longitute,
          vendor_other_details!vendor_id(id, delivery_radius, start_time, end_time, cuisines, service_types)
        `)
        .in('vendor_type', ['tiffin_supplier', 'tiffin_service', 'tiffin'])
        .eq('is_active', true);
    } else {
      // For home chef type, fetch 'home_chef' vendors
      query = supabase
        .from('vendors')
        .select(`
          id, business_name, phone_number, address, city, state, profile_image, vendor_type, operational_status, profile_status, lattitute, longitute,
          vendor_other_details!vendor_id(id, delivery_radius, start_time, end_time, cuisines, service_types)
        `)
        .in('vendor_type', ['home_chef', 'home-chef'])
        .eq('is_active', true);
    }

    // Execute the query
    const { data, error } = await query;

    if (error) {
      console.error('Error fetching vendors:', error);
      return [];
    }

    if (!data || data.length === 0) {
      console.log('No vendors found');
      return [];
    }

    // Process the data to include coordinates and calculate distance
    const processedData = data.map(vendor => {
      // Process vendor_other_details
      let otherDetails;
      if (vendor.vendor_other_details) {
        // Handle both array and object formats
        if (Array.isArray(vendor.vendor_other_details)) {
          otherDetails = vendor.vendor_other_details[0] || {};
        } else {
          otherDetails = vendor.vendor_other_details;
        }
      } else {
        otherDetails = {};
      }

      // Extract delivery radius if available
      let deliveryRadius = null;
      if (otherDetails && otherDetails.delivery_radius !== undefined && otherDetails.delivery_radius !== null) {
        // Handle both string and number formats
        deliveryRadius = typeof otherDetails.delivery_radius === 'string' 
          ? parseFloat(otherDetails.delivery_radius) 
          : otherDetails.delivery_radius;
      }

      // Add delivery_radius to vendor object
      vendor.delivery_radius = deliveryRadius;

      // Extract latitude and longitude from vendor data
      let latitude = null;
      let longitude = null;

      if (vendor.lattitute !== undefined && vendor.lattitute !== null) {
        latitude = typeof vendor.lattitute === 'string' ? parseFloat(vendor.lattitute) : vendor.lattitute;
      }

      if (vendor.longitute !== undefined && vendor.longitute !== null) {
        longitude = typeof vendor.longitute === 'string' ? parseFloat(vendor.longitute) : vendor.longitute;
      }

      // If coordinates are not available, set default coordinates for Noida
      if (latitude === null || longitude === null || isNaN(latitude) || isNaN(longitude)) {
        console.log(`Vendor ${vendor.id} (${vendor.business_name}) has no coordinates, setting default coordinates`);
        // Default coordinates for Noida
        latitude = 28.5355;
        longitude = 77.3910;
      }

      // Set coordinates for vendor
      vendor.coordinates = {
        latitude,
        longitude
      };

      // Calculate distance between user and vendor
      const distance = calculateDistance(
        parseFloat(userCoordinates.lat),
        parseFloat(userCoordinates.lng),
        latitude,
        longitude
      );

      // Add distance to vendor object for display
      vendor.distance = parseFloat(distance.toFixed(1));

      return vendor;
    });

    console.log('Processed vendors with distance:', processedData.length);
    return processedData;
  } catch (error) {
    console.error('Error in fetchVendorsByUserCoordinates:', error);
    return [];
  }
};
